import { Suspense } from 'react'
import { notFound, redirect } from 'next/navigation'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { CheckoutSkeleton } from '../components/CheckoutSkeleton'
import { CheckoutPageContent } from '../components/CheckoutPageContent'

interface CheckoutPageProps {
  params: Promise<{
    paymentId: string
  }>
}

export default async function CheckoutPage({ params }: CheckoutPageProps) {
  const { paymentId } = await params
  // Verificar se usuário está autenticado
  const user = await getCurrentUser()
  if (!user) {
    redirect('/login')
  }

  // Validar formato do paymentId (UUID)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(paymentId)) {
    notFound()
  }

  return (
    <Suspense fallback={<CheckoutSkeleton />}>
      <CheckoutPageContent paymentId={paymentId} userId={user.id} />
    </Suspense>
  )
}
