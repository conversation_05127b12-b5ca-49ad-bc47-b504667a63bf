import { NextRequest, NextResponse } from 'next/server';
import { processOverdueNotifications } from '@/services/billing/notification-service';
import { CronLockManager } from '@/services/classes/cron-lock-manager';

// Configuração para runtime do Next.js
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 300; // 5 minutos de timeout

/**
 * Chave secreta para proteger o endpoint do cron job
 * Deve ser definida no .env como CRON_SECRET_KEY
 */
const CRON_SECRET_KEY = process.env.CRON_SECRET_KEY;

/**
 * Contador de execuções para estatísticas
 */
let executionCount = 0;

/**
 * Valida se a requisição está autorizada
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Verificar se a chave secreta está configurada
  if (!CRON_SECRET_KEY) {
    console.error('🚨 CRON_SECRET_KEY não configurada');
    return { isValid: false, error: 'Cron job não configurado' };
  }

  // Verificar header de autorização
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!providedKey || providedKey !== CRON_SECRET_KEY) {
    console.error('🚨 Tentativa de acesso não autorizada ao cron job de notificações de atraso');
    return { isValid: false, error: 'Não autorizado' };
  }

  return { isValid: true };
}

/**
 * Endpoint principal do cron job para notificações de atraso
 * Método: POST
 * Header: Authorization: Bearer <CRON_SECRET_KEY>
 * 
 * Execução programada: Diário às 10:00
 * Função: 
 * - Notificar alunos com pagamentos atrasados
 * - Escalar notificações (3, 7, 15 dias de atraso)
 * - Alertar administradores sobre inadimplência
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  console.log(`🚨 [${requestId}] Iniciando cron job de notificações de atraso...`);

  try {
    // Validar requisição
    const validation = validateCronRequest(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: validation.error,
          timestamp: new Date().toISOString(),
          requestId
        },
        { status: validation.error === 'Não autorizado' ? 401 : 409 }
      );
    }

    // Criar gerenciador de lock
    const lockManager = new CronLockManager('overdue-notifications', 5); // 5 minutos de timeout

    // Executar com lock automático
    const lockResult = await lockManager.executeWithLock(requestId, async () => {
      executionCount++;
      console.log(`🚀 [${requestId}] Execução #${executionCount} autorizada`);

      // Executar processamento de notificações de atraso
      return await processOverdueNotifications();
    });

    const executionTime = Date.now() - startTime;

    // Verificar se conseguiu adquirir o lock
    if (!lockResult.lockAcquired) {
      console.warn(`⚠️ [${requestId}] Lock não adquirido: ${lockResult.error}`);
      return NextResponse.json({
        success: false,
        error: lockResult.error || 'Não foi possível adquirir lock',
        lockAcquired: false,
        timestamp: new Date().toISOString(),
        requestId
      }, { status: 409 });
    }

    // Verificar se a execução foi bem-sucedida
    if (!lockResult.success || !lockResult.data) {
      console.error(`❌ [${requestId}] Erro durante execução: ${lockResult.error}`);
      return NextResponse.json({
        success: false,
        error: lockResult.error || 'Erro durante execução',
        lockAcquired: true,
        executionTimeMs: executionTime,
        requestId,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    const result = lockResult.data;

    // Log do resultado
    console.log(`✅ [${requestId}] Cron job de notificações de atraso concluído em ${executionTime}ms:`);
    console.log(`  - Total processados: ${result.data?.totalProcessed || 0}`);
    console.log(`  - Notificações enviadas: ${result.data?.notificationsSent || 0}`);
    console.log(`  - Alertas admin enviados: ${result.data?.adminAlertsSent || 0}`);
    console.log(`  - Erros: ${result.data?.errors?.length || 0}`);

    // Verificar se houve erros
    if (result.data?.errors && result.data.errors.length > 0) {
      console.error(`❌ [${requestId}] Erros durante execução:`, result.data.errors);
    }

    // Log detalhado das notificações por nível de escalação
    const notificationsByEscalation = result.data?.notifications
      ?.filter((n: { type: string }) => n.type === 'student_overdue')
      ?.reduce((acc: Record<number, number>, notification: { escalation_level: number }) => {
        const level = notification.escalation_level;
        acc[level] = (acc[level] || 0) + 1;
        return acc;
      }, {} as Record<number, number>) || {};

    console.log(`📊 [${requestId}] Notificações por nível de escalação:`, notificationsByEscalation);

    return NextResponse.json({
      success: result.success,
      data: {
        ...result.data,
        notificationsByEscalation,
        executionId: requestId,
        cronExecutionCount: executionCount,
        apiExecutionTimeMs: executionTime,
        lockAcquired: true
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    console.error(`💥 [${requestId}] Erro crítico no cron job de notificações de atraso:`, error);

    return NextResponse.json({
      success: false,
      error: 'Erro interno do cron job',
      details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
      executionTimeMs: executionTime,
      requestId,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Endpoint para verificar status do cron job
 * Método: GET
 */
export async function GET(request: NextRequest) {
  // Verificar autorização (mesma chave do POST)
  const authHeader = request.headers.get('authorization');
  const providedKey = authHeader?.replace('Bearer ', '');

  if (!CRON_SECRET_KEY || !providedKey || providedKey !== CRON_SECRET_KEY) {
    return NextResponse.json(
      { error: 'Não autorizado' },
      { status: 401 }
    );
  }

  try {
    // Verificar quantos pagamentos em atraso existem
    const { createClient } = await import('@/services/supabase/server');
    const supabase = await createClient();

    const { data: overduePayments, error } = await supabase
      .from('payments')
      .select('id, due_date, overdue_date, amount')
      .eq('status', 'overdue')
      .not('overdue_date', 'is', null);

    if (error) {
      throw error;
    }

    // Categorizar por nível de escalação
    const escalationLevels = {
      level_1: 0, // 3-6 dias
      level_2: 0, // 7-14 dias
      level_3: 0, // 15+ dias
      total_amount_at_risk: 0
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    overduePayments?.forEach(payment => {
      const dueDate = new Date(payment.due_date);
      dueDate.setHours(0, 0, 0, 0);
      const overdueDays = Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
      
      escalationLevels.total_amount_at_risk += parseFloat(payment.amount);
      
      if (overdueDays >= 15) {
        escalationLevels.level_3++;
      } else if (overdueDays >= 7) {
        escalationLevels.level_2++;
      } else if (overdueDays >= 3) {
        escalationLevels.level_1++;
      }
    });

    // Verificar locks ativos
    const lockManager = new CronLockManager('overdue-notifications');
    const lockStatus = await lockManager.hasActiveLock();

    return NextResponse.json({
      status: 'healthy',
      isRunning: lockStatus.hasLock,
      runningBy: lockStatus.lockedBy || null,
      lockExpiresAt: lockStatus.expiresAt || null,
      executionCount,
      overduePayments: {
        total: overduePayments?.length || 0,
        escalation_level_1: escalationLevels.level_1,
        escalation_level_2: escalationLevels.level_2,
        escalation_level_3: escalationLevels.level_3,
        total_amount_at_risk: escalationLevels.total_amount_at_risk
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao verificar status do cron job de notificações de atraso:', error);
    return NextResponse.json({
      status: 'error',
      error: 'Erro ao verificar status',
      executionCount,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Bloquear outros métodos HTTP
 */
export async function PUT() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 });
}
