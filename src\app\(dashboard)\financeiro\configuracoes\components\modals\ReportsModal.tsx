'use client';

import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ConfigurationModal } from './ConfigurationModal';

interface ReportsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ReportsModal({ isOpen, onClose }: ReportsModalProps) {
  const [automaticReports, setAutomaticReports] = useState(true);
  const [reportFrequency, setReportFrequency] = useState('monthly');
  const [exportFormats, setExportFormats] = useState(['pdf', 'excel']);
  const [emailReports, setEmailReports] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const handleFormatChange = (format: string, checked: boolean) => {
    if (checked) {
      setExportFormats([...exportFormats, format]);
    } else {
      setExportFormats(exportFormats.filter(f => f !== format));
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Implementar lógica de salvamento
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular API call
      console.log('Salvando relatórios:', {
        automaticReports,
        reportFrequency,
        exportFormats,
        emailReports
      });
      onClose();
    } catch (error) {
      console.error('Erro ao salvar relatórios:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ConfigurationModal
      isOpen={isOpen}
      onClose={onClose}
      title="Configurações de Relatórios"
      onSave={handleSave}
      isLoading={isLoading}
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Relatórios Automáticos</Label>
            <p className="text-sm text-muted-foreground">
              Gerar relatórios automaticamente
            </p>
          </div>
          <Switch
            checked={automaticReports}
            onCheckedChange={setAutomaticReports}
          />
        </div>

        {automaticReports && (
          <div className="space-y-2 ml-4">
            <Label htmlFor="report-frequency">Frequência</Label>
            <Select value={reportFrequency} onValueChange={setReportFrequency}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Semanal</SelectItem>
                <SelectItem value="monthly">Mensal</SelectItem>
                <SelectItem value="quarterly">Trimestral</SelectItem>
                <SelectItem value="yearly">Anual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="space-y-3">
          <Label>Formatos de Exportação</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="pdf"
                checked={exportFormats.includes('pdf')}
                onCheckedChange={(checked) => handleFormatChange('pdf', checked as boolean)}
              />
              <Label htmlFor="pdf">PDF</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="excel"
                checked={exportFormats.includes('excel')}
                onCheckedChange={(checked) => handleFormatChange('excel', checked as boolean)}
              />
              <Label htmlFor="excel">Excel</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="csv"
                checked={exportFormats.includes('csv')}
                onCheckedChange={(checked) => handleFormatChange('csv', checked as boolean)}
              />
              <Label htmlFor="csv">CSV</Label>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Envio por Email</Label>
            <p className="text-sm text-muted-foreground">
              Enviar relatórios por email
            </p>
          </div>
          <Switch
            checked={emailReports}
            onCheckedChange={setEmailReports}
          />
        </div>
      </div>
    </ConfigurationModal>
  );
}