'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Accordion } from '@/components/ui/accordion'
import { cn } from '@/lib/utils'

interface PlanFormContainerProps {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
}

interface PlanFormHeaderProps {
  title: string
  description?: string
  className?: string
}

type PlanFormContentProps = {
  children: React.ReactNode;
  className?: string;
  collapsible?: boolean;
} & (
  | {
      accordionType?: 'single';
      value?: string;
      onValueChange?: (value: string) => void;
    }
  | {
      accordionType: 'multiple';
      value?: string[];
      onValueChange?: (value: string[]) => void;
    }
);

interface PlanFormActionsProps {
  children: React.ReactNode
  className?: string
  alignment?: 'left' | 'center' | 'right'
}

const alignmentClasses = {
  left: 'justify-start',
  center: 'justify-center',
  right: 'justify-end'
}

export function PlanFormContainer({
  title,
  description,
  children,
  className
}: PlanFormContainerProps) {
  return (
    <div className={cn('max-w-4xl mx-auto', className)}>
      <Card>
        {(title || description) && (
          <CardHeader>
            {title && <CardTitle className="text-xl">{title}</CardTitle>}
            {description && <p className="text-muted-foreground mt-2">{description}</p>}
          </CardHeader>
        )}
        <CardContent className="space-y-6">
          {children}
        </CardContent>
      </Card>
    </div>
  )
}

export function PlanFormHeader({ title, description, className }: PlanFormHeaderProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <h2 className="text-xl font-semibold">{title}</h2>
      {description && (
        <p className="text-muted-foreground">{description}</p>
      )}
    </div>
  )
}

export function PlanFormContent(props: PlanFormContentProps) {
  if (props.accordionType === 'multiple') {
    const { children, className, value, onValueChange } = props
    return (
      <Accordion
        type="multiple"
        className={cn('w-full space-y-4', className)}
        value={value}
        onValueChange={onValueChange}
      >
        {children}
      </Accordion>
    )
  }

  const {
    children,
    className,
    collapsible = true,
    value,
    onValueChange
  } = props
  return (
    <Accordion
      type="single"
      collapsible={collapsible}
      className={cn('w-full space-y-4', className)}
      value={value}
      onValueChange={onValueChange}
    >
      {children}
    </Accordion>
  )
}

export function PlanFormActions({
  children,
  className,
  alignment = 'center'
}: PlanFormActionsProps) {
  return (
    <div className={cn(
      'flex pt-6',
      alignmentClasses[alignment],
      className
    )}>
      {children}
    </div>
  )
}
