"use client";

/**
 * Gráfico de Status dos Alunos
 * Exibe a distribuição dos alunos por status (ativo, pausado, cancelado)
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import { Users, Activity, Pause, UserX } from 'lucide-react';
import { cn } from '@/lib/utils';

import { formatNumber, formatPercentage } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface StudentStatusData {
  status: string;
  count: number;
  percentage: number;
  color: string;
  icon: React.ReactNode;
}

interface StudentStatusChartProps {
  data?: {
    active: number;
    paused: number;
    canceled: number;
    total: number;
  };
  loading?: boolean;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
}

// ============================================================================
// CONFIGURAÇÕES DO GRÁFICO
// ============================================================================

const STATUS_COLORS = {
  active: '#10b981',
  paused: '#f59e0b',
  canceled: '#ef4444',
};

const STATUS_ICONS = {
  active: <Activity className="h-4 w-4" />,
  paused: <Pause className="h-4 w-4" />,
  canceled: <UserX className="h-4 w-4" />,
};

const STATUS_LABELS = {
  active: 'Ativos',
  paused: 'Pausados',
  canceled: 'Cancelados',
};

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <div className="flex items-center gap-2 mb-1">
          {data.icon}
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {data.status}
          </span>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Quantidade: </span>
          {formatNumber(data.count)}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Percentual: </span>
          {formatPercentage(data.percentage)}
        </p>
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE DE LEGENDA CUSTOMIZADA
// ============================================================================

const CustomLegend = ({ data }: { data: StudentStatusData[] }) => (
  <div className="flex flex-wrap justify-center gap-4 mt-4">
    {data.map((item, index) => (
      <div key={index} className="flex items-center gap-2">
        <div 
          className="w-3 h-3 rounded-full" 
          style={{ backgroundColor: item.color }}
        />
        <div className="flex items-center gap-1">
          {item.icon}
          <span className="text-sm font-medium">{item.status}</span>
        </div>
        <span className="text-sm text-gray-500">
          ({formatNumber(item.count)})
        </span>
      </div>
    ))}
  </div>
);

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const StudentStatusChart: React.FC<StudentStatusChartProps> = ({
  data,
  loading = false,
  className = ''
}) => {
  const [chartData, setChartData] = useState<StudentStatusData[]>([]);

  useEffect(() => {
    if (data) {
      const total = data.total || (data.active + data.paused + data.canceled);
      
      const processedData: StudentStatusData[] = [
        {
          status: STATUS_LABELS.active,
          count: data.active,
          percentage: total > 0 ? (data.active / total) * 100 : 0,
          color: STATUS_COLORS.active,
          icon: STATUS_ICONS.active,
        },
        {
          status: STATUS_LABELS.paused,
          count: data.paused,
          percentage: total > 0 ? (data.paused / total) * 100 : 0,
          color: STATUS_COLORS.paused,
          icon: STATUS_ICONS.paused,
        },
        {
          status: STATUS_LABELS.canceled,
          count: data.canceled,
          percentage: total > 0 ? (data.canceled / total) * 100 : 0,
          color: STATUS_COLORS.canceled,
          icon: STATUS_ICONS.canceled,
        },
      ].filter(item => item.count > 0); // Filtrar itens com count 0

      setChartData(processedData);
    }
  }, [data]);

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Status dos Alunos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-4"></div>
              <div className="w-64 h-64 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || chartData.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Status dos Alunos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Nenhum dado disponível</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const total = data.active + data.paused + data.canceled;

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Status dos Alunos
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total: {formatNumber(total)}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                dataKey="count"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Legenda Customizada */}
        <CustomLegend data={chartData} />

        {/* Estatísticas Detalhadas */}
        <div className="grid grid-cols-3 gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Activity className="h-4 w-4 text-green-500" />
              <span className="text-sm text-gray-500 dark:text-gray-400">Ativos</span>
            </div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              {formatPercentage(data.active > 0 ? (data.active / total) * 100 : 0)}
            </div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Pause className="h-4 w-4 text-yellow-500" />
              <span className="text-sm text-gray-500 dark:text-gray-400">Pausados</span>
            </div>
            <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
              {formatPercentage(data.paused > 0 ? (data.paused / total) * 100 : 0)}
            </div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <UserX className="h-4 w-4 text-red-500" />
              <span className="text-sm text-gray-500 dark:text-gray-400">Cancelados</span>
            </div>
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              {formatPercentage(data.canceled > 0 ? (data.canceled / total) * 100 : 0)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
