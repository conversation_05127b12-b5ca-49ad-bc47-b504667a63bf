"use server";

/**
 * Server Actions para gráficos de pagamentos
 */

import { 
  DashboardActionResult, 
  PaymentStatusData, 
  PaymentMethodData 
} from '../../types/dashboard-types';
import { ensureNumber, formatCurrency } from '../../utils/dashboard-utils';
import { formatPaymentMethodName } from '@/utils/payment-method-formatter';
import { getAuthenticatedClient } from '../shared/auth-utils';

/**
 * Buscar dados para gráfico de status de pagamentos
 */
export async function getPaymentStatusChart(): Promise<DashboardActionResult<PaymentStatusData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const { data: payments, error } = await supabase
      .from('payments')
      .select('status, amount')
      .eq('tenant_id', tenantId);

    if (error) {
      console.error('Error fetching payment status data:', error);
      return { success: false, error: 'Erro ao buscar status de pagamentos' };
    }

    // Processar dados por status
    const statusData: { [key: string]: { amount: number; count: number } } = {};
    const statusLabels: { [key: string]: string } = {
      'paid': 'Pagos',
      'pending': 'Pendentes',
      'overdue': 'Vencidos',
      'canceled': 'Cancelados',
      'awaiting_confirmation': 'Aguardando Confirmação'
    };

    (payments || []).forEach((payment: any) => {
      const status = payment.status;
      if (!statusData[status]) {
        statusData[status] = { amount: 0, count: 0 };
      }
      statusData[status].amount += ensureNumber(payment.amount);
      statusData[status].count += 1;
    });

    // Converter para array
    const totalAmount = Object.values(statusData).reduce((sum, item) => sum + item.amount, 0);
    const totalCount = Object.values(statusData).reduce((sum, item) => sum + item.count, 0);

    const chartData: PaymentStatusData[] = Object.entries(statusData)
      .map(([status, data]) => ({
        status,
        label: statusLabels[status] || status,
        amount: data.amount,
        count: data.count,
        percentage: totalAmount > 0 ? Math.round((data.amount / totalAmount) * 100 * 100) / 100 : 0,
        countPercentage: totalCount > 0 ? Math.round((data.count / totalCount) * 100 * 100) / 100 : 0,
        formattedAmount: formatCurrency(data.amount),
        color: getStatusColor(status)
      }))
      .sort((a, b) => b.amount - a.amount);

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getPaymentStatusChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Buscar dados para gráfico de métodos de pagamento
 */
export async function getPaymentMethodsChart(): Promise<DashboardActionResult<PaymentMethodData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const { data: payments, error } = await supabase
      .from('payments')
      .select('payment_method, amount')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid');

    if (error) {
      console.error('Error fetching payment methods data:', error);
      return { success: false, error: 'Erro ao buscar métodos de pagamento' };
    }

    // Processar dados por método de pagamento
    const methodData: { [key: string]: { amount: number; count: number } } = {};

    (payments || []).forEach((payment: any) => {
      const method = payment.payment_method || 'Não informado';
      if (!methodData[method]) {
        methodData[method] = { amount: 0, count: 0 };
      }
      methodData[method].amount += ensureNumber(payment.amount);
      methodData[method].count += 1;
    });

    // Converter para array
    const totalAmount = Object.values(methodData).reduce((sum, item) => sum + item.amount, 0);
    const totalCount = Object.values(methodData).reduce((sum, item) => sum + item.count, 0);

    const chartData: PaymentMethodData[] = Object.entries(methodData)
      .map(([method, data]) => ({
        method: formatPaymentMethodName(method),
        amount: data.amount,
        count: data.count,
        percentage: totalAmount > 0 ? Math.round((data.amount / totalAmount) * 100 * 100) / 100 : 0,
        countPercentage: totalCount > 0 ? Math.round((data.count / totalCount) * 100 * 100) / 100 : 0,
        formattedAmount: formatCurrency(data.amount),
        color: getPaymentMethodColor(method)
      }))
      .sort((a, b) => b.amount - a.amount);

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getPaymentMethodsChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

/**
 * Obter cor para status de pagamento
 */
function getStatusColor(status: string): string {
  const statusColors: { [key: string]: string } = {
    'paid': '#10b981', // green
    'pending': '#f59e0b', // amber
    'overdue': '#ef4444', // red
    'canceled': '#6b7280', // gray
    'awaiting_confirmation': '#8b5cf6' // violet
  };

  return statusColors[status] || '#6b7280';
}

/**
 * Obter cor para método de pagamento
 */
function getPaymentMethodColor(method: string): string {
  const methodColors: { [key: string]: string } = {
    'pix': '#10b981', // green
    'credit_card': '#3b82f6', // blue
    'debit_card': '#06b6d4', // cyan
    'bank_transfer': '#8b5cf6', // violet
    'cash': '#84cc16', // lime
    'check': '#f59e0b' // amber
  };

  return methodColors[method.toLowerCase()] || '#6b7280';
}
