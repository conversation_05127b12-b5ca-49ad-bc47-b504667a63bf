'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useRecurrentMetrics } from '@/hooks/use-recurrent-metrics';
import { AlertCircle, TrendingUp, TrendingDown, Users, DollarSign, Clock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface MetricCardProps {
  value: string;
  label: string;
  color: string;
  isLoading?: boolean;
  growth?: number;
}

function MetricCard({ value, label, color, isLoading, growth }: MetricCardProps) {
  // Mapear cores para configuração consistente
  const colorConfig = {
    'text-green-600': {
      text: 'text-emerald-600 dark:text-emerald-400',
      bg: 'bg-emerald-100 dark:bg-emerald-900/30',
      header: 'from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent',
      icon: 'text-emerald-600 dark:text-emerald-400',
      iconComponent: Users
    },
    'text-blue-600': {
      text: 'text-blue-600 dark:text-blue-400',
      bg: 'bg-blue-100 dark:bg-blue-900/30',
      header: 'from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent',
      icon: 'text-blue-600 dark:text-blue-400',
      iconComponent: DollarSign
    },
    'text-orange-600': {
      text: 'text-orange-600 dark:text-orange-400',
      bg: 'bg-orange-100 dark:bg-orange-900/30',
      header: 'from-orange-50/50 to-transparent dark:from-orange-900/20 dark:to-transparent',
      icon: 'text-orange-600 dark:text-orange-400',
      iconComponent: Clock
    },
    'text-purple-600': {
      text: 'text-purple-600 dark:text-purple-400',
      bg: 'bg-purple-100 dark:bg-purple-900/30',
      header: 'from-purple-50/50 to-transparent dark:from-purple-900/20 dark:to-transparent',
      icon: 'text-purple-600 dark:text-purple-400',
      iconComponent: TrendingUp
    }
  };

  const config = colorConfig[color as keyof typeof colorConfig] || colorConfig['text-blue-600'];
  const IconComponent = config.iconComponent;

  if (isLoading) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
        <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
          <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-24 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
          </div>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="text-center space-y-2">
            <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-20 mx-auto relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Função para formatar o crescimento baseado no tipo de métrica
  const formatGrowth = (growth: number, label: string) => {
    // Para taxa de sucesso, mostrar como pontos percentuais
    if (label.includes('Taxa de Sucesso')) {
      if (growth === 0) return null; // Não mostrar se não há mudança
      const sign = growth > 0 ? '+' : '';
      return `${sign}${growth}pp`; // pp = pontos percentuais
    }

    // Para outras métricas, mostrar como porcentagem normal
    if (growth === 0) return null; // Não mostrar se não há mudança
    const sign = growth > 0 ? '+' : '';
    return `${sign}${Math.abs(growth)}%`;
  };

  const growthText = growth !== undefined ? formatGrowth(growth, label) : null;

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className={`pb-4 bg-gradient-to-r ${config.header}`}>
        <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
          <div className={`p-1 ${config.bg} rounded-full`}>
            <IconComponent className={`w-4 h-4 ${config.icon}`} />
          </div>
          {label}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="text-center space-y-1">
          <div className="flex items-center justify-center gap-2">
            <p className={`text-2xl font-bold ${config.text} tracking-tight`}>{value}</p>
            {growthText && (
              <div className={`flex items-center text-sm ${
                growth! >= 0 ? 'text-emerald-600 dark:text-emerald-400' : 'text-red-600 dark:text-red-400'
              }`}>
                {growth! >= 0 ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                <span className="ml-1">{growthText}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface MetricsGridProps {
  showComparison?: boolean;
}

export function MetricsGrid({ showComparison = false }: MetricsGridProps) {
  const { metrics, comparison, isLoading, error } = useRecurrentMetrics(
    undefined,
    showComparison
  );

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value}%`;
  };

  // Mostrar erro se houver
  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <div className="md:col-span-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  // Dados padrão para fallback
  const defaultMetrics = {
    alunosEmDia: 0,
    receitaMensal: 0,
    alunosAtrasados: 0,
    taxaSucesso: 0,
  };

  const currentMetrics = metrics || defaultMetrics;
  const growthData = showComparison && comparison ? comparison.growth : undefined;

  return (
    <div className="grid gap-4 md:grid-cols-4 mb-6">
      <MetricCard
        value={currentMetrics.alunosEmDia.toString()}
        label="Alunos com Mensalidade em Dia"
        color="text-green-600"
        isLoading={isLoading}
        growth={growthData?.alunosEmDia}
      />
      <MetricCard
        value={formatCurrency(currentMetrics.receitaMensal)}
        label="Receita Mensal de Matrículas"
        color="text-blue-600"
        isLoading={isLoading}
        growth={growthData?.receitaMensal}
      />
      <MetricCard
        value={currentMetrics.alunosAtrasados.toString()}
        label="Alunos com Pagamento Atrasado"
        color="text-orange-600"
        isLoading={isLoading}
        growth={growthData?.alunosAtrasados}
      />
      <MetricCard
        value={formatPercentage(currentMetrics.taxaSucesso)}
        label="Taxa de Sucesso nas Cobranças"
        color="text-purple-600"
        isLoading={isLoading}
        growth={growthData?.taxaSucesso}
      />
    </div>
  );
}
