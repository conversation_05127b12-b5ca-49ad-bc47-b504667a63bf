import { Metadata } from 'next';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calculator, Download, FileText, Calendar, BarChart3 } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Contabilidade - Financeiro',
  description: 'Relatórios contábeis e exportação de dados',
};

export default function ContabilidadePage() {
  return (
    <>
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Contabilidade</h1>
            <p className="text-muted-foreground">
              Relatórios contábeis e exportação de dados para contabilidade.
            </p>
          </div>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            G<PERSON><PERSON>
          </Button>
        </div>
      </div>

      {/* Resu<PERSON> contábil */}
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">R$ 45.680,00</p>
              <p className="text-xs text-muted-foreground">Receita Bruta</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">R$ 8.250,00</p>
              <p className="text-xs text-muted-foreground">Despesas</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">R$ 37.430,00</p>
              <p className="text-xs text-muted-foreground">Lucro Líquido</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">R$ 2.840,00</p>
              <p className="text-xs text-muted-foreground">Impostos</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 mb-6">
        {/* Relatórios Disponíveis */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <CardTitle>Relatórios Disponíveis</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">DRE - Demonstrativo de Resultado</p>
                  <p className="text-sm text-muted-foreground">Dezembro 2024</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Balancete</p>
                  <p className="text-sm text-muted-foreground">Dezembro 2024</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Fluxo de Caixa</p>
                  <p className="text-sm text-muted-foreground">Dezembro 2024</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configurações Contábeis */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Calculator className="h-5 w-5 text-red-600" />
              <CardTitle>Configurações Contábeis</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Regime Tributário</span>
                <Badge variant="secondary">Simples Nacional</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">CNPJ</span>
                <span className="text-sm font-medium">12.345.678/0001-90</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Contador</span>
                <span className="text-sm font-medium">José Silva CRC 123456</span>
              </div>
              <Button variant="outline" className="w-full" size="sm">
                Editar Configurações
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Exportações e Integrações */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-green-600" />
            <CardTitle>Exportações e Integrações</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <p className="font-medium mb-2">SPED Fiscal</p>
              <Button variant="outline" size="sm" className="w-full">
                Gerar SPED
              </Button>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Calculator className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <p className="font-medium mb-2">XML Contador</p>
              <Button variant="outline" size="sm" className="w-full">
                Exportar XML
              </Button>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <BarChart3 className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <p className="font-medium mb-2">Backup Dados</p>
              <Button variant="outline" size="sm" className="w-full">
                Download Backup
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
} 