'use client'

import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { detalhesPlanoSchema as detailsSchema } from '@/schemas/plan-schemas'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { User } from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'

type DetailsFormValues = z.infer<typeof detailsSchema>

interface PlanFormDetailsSectionProps {
  onSubmit?: (data: DetailsFormValues) => void
  defaultValues?: Partial<DetailsFormValues>
}

export function PlanFormDetailsSection({
  onSubmit,
  defaultValues
}: PlanFormDetailsSectionProps) {
  const { formData, updateSection, submissionErrors } = usePlanForm()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<DetailsFormValues>({
    resolver: zodResolver(detailsSchema),
    defaultValues: {
      titulo: formData.details?.titulo || defaultValues?.titulo || '',
      tipo: formData.details?.tipo || defaultValues?.tipo || 'individual'
    }
  })

  const selectedType = watch('tipo')
  const titulo = watch('titulo')

  // Sincroniza o formulário com o contexto em tempo-real, mesmo quando o título está vazio.
  useEffect(() => {
    const subscription = watch((value) => {
      updateSection('details', value as DetailsFormValues)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection])

  const handleFormSubmit = (data: DetailsFormValues) => {
    updateSection('details', data)
    onSubmit?.(data)
  }

  let tituloError = submissionErrors?.details?.titulo?._errors?.[0]
    || submissionErrors?.details?._errors?.[0]
    || errors.titulo?.message

  if (tituloError === 'Required') {
    tituloError = 'Título é obrigatório'
  }

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="space-y-2">
        <Label htmlFor="titulo" className="text-sm font-medium">
          TÍTULO
        </Label>
        <Input
          id="titulo"
          placeholder="Digite o título do plano..."
          {...register('titulo')}
          className={cn(
            'w-full',
            (errors.titulo || submissionErrors?.details?.titulo) && 'border-red-500 focus:border-red-500'
          )}
        />
        {tituloError && (
          <p className="text-sm text-red-600">{tituloError}</p>
        )}
      </div>

      {/* Tipo */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">
          TIPO
        </Label>
        <div className="flex gap-3">
          <Button
            type="button"
            variant={selectedType === 'individual' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setValue('tipo', 'individual')}
            className={cn(
              'flex items-center gap-2 px-4 py-2',
              selectedType === 'individual'
                ? 'bg-teal-100 dark:bg-teal-900/30 border-teal-300 dark:border-teal-600 text-teal-700 dark:text-teal-300 hover:bg-teal-200 dark:hover:bg-teal-900/50'
                : 'border-input text-foreground hover:bg-accent hover:text-accent-foreground'
            )}
          >
            <User className="h-4 w-4" />
            Membro Individual
          </Button>
        </div>
        {errors.tipo && (
          <p className="text-sm text-red-600">{errors.tipo.message}</p>
        )}
      </div>

      {/* Descrição do tipo selecionado */}
      {selectedType === 'individual' && (
        <div className="p-4 bg-muted rounded-lg border-l-4 border-teal-500 dark:border-teal-400">
          <p className="text-sm text-muted-foreground">
            Plano individual destinado a um único membro da academia.
          </p>
        </div>
      )}
    </div>
  )
}
