"use server";

/**
 * Server Actions para gráficos de despesas
 */

import { 
  DashboardActionResult, 
  DateRange 
} from '../../types/dashboard-types';
import { ensureNumber, formatCurrency } from '../../utils/dashboard-utils';
import { getAuthenticatedClient, formatDateForSQL } from '../shared/auth-utils';

// ============================================================================
// TIPOS ESPECÍFICOS PARA GRÁFICOS DE DESPESAS
// ============================================================================

export interface MonthlyExpenseData {
  month: string;
  expense: number;
  formattedExpense: string;
}

export interface ExpenseCategoryChartData {
  categoryId: string;
  categoryName: string;
  categoryColor: string;
  amount: number;
  count: number;
  percentage: number;
  formattedAmount: string;
}

export interface ExpenseTypeData {
  type: 'fixed' | 'variable';
  label: string;
  amount: number;
  count: number;
  percentage: number;
  formattedAmount: string;
  color: string;
}

export interface PendingExpenseData {
  id: string;
  supplierName: string;
  amount: number;
  formattedAmount: string;
  dueDate: string;
  formattedDueDate: string;
  daysPastDue: number;
  categoryName: string;
  categoryColor: string;
  description?: string;
  status: 'pending' | 'overdue';
}

// ============================================================================
// GRÁFICO DE DESPESAS POR CATEGORIA
// ============================================================================

/**
 * Buscar dados para gráfico de despesas por categoria
 */
export async function getExpensesByCategoryChart(
  dateRange: DateRange
): Promise<DashboardActionResult<ExpenseCategoryChartData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const { data: expenses, error } = await supabase
      .from('expenses')
      .select(`
        amount,
        status,
        expense_categories (
          id,
          name,
          color
        )
      `)
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('paid_at', formatDateForSQL(dateRange.startDate))
      .lte('paid_at', formatDateForSQL(dateRange.endDate));

    if (error) {
      console.error('Error fetching expenses by category:', error);
      return { success: false, error: 'Erro ao buscar despesas por categoria' };
    }

    // Processar dados por categoria
    const categoryMap = new Map<string, {
      categoryId: string;
      categoryName: string;
      categoryColor: string;
      amount: number;
      count: number;
    }>();

    (expenses || []).forEach((expense: any) => {
      const category = expense.expense_categories;
      const categoryId = category?.id || 'uncategorized';
      const categoryName = category?.name || 'Sem categoria';
      const categoryColor = category?.color || '#6B7280';

      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, {
          categoryId,
          categoryName,
          categoryColor,
          amount: 0,
          count: 0
        });
      }

      const categoryData = categoryMap.get(categoryId)!;
      categoryData.amount += ensureNumber(expense.amount);
      categoryData.count += 1;
    });

    // Calcular percentuais
    const totalAmount = Array.from(categoryMap.values()).reduce((sum, cat) => sum + cat.amount, 0);

    const chartData: ExpenseCategoryChartData[] = Array.from(categoryMap.values())
      .map(category => ({
        ...category,
        percentage: totalAmount > 0 ? Math.round((category.amount / totalAmount) * 100 * 100) / 100 : 0,
        formattedAmount: formatCurrency(category.amount)
      }))
      .sort((a, b) => b.amount - a.amount);

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getExpensesByCategoryChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

// ============================================================================
// GRÁFICO DE EVOLUÇÃO DE DESPESAS
// ============================================================================

/**
 * Buscar dados para gráfico de evolução de despesas mensais
 */
export async function getMonthlyExpenseChart(
  year: number = new Date().getFullYear()
): Promise<DashboardActionResult<MonthlyExpenseData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const startOfYear = new Date(year, 0, 1);
    const endOfYear = new Date(year, 11, 31, 23, 59, 59);

    const { data: expenses, error } = await supabase
      .from('expenses')
      .select('amount, status, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('paid_at', startOfYear.toISOString())
      .lte('paid_at', endOfYear.toISOString());

    if (error) {
      console.error('Error fetching monthly expense chart data:', error);
      return { success: false, error: 'Erro ao buscar dados de despesas mensais' };
    }

    // Processar dados por mês
    const monthlyData: { [key: string]: number } = {};
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];

    // Inicializar todos os meses com 0
    months.forEach(month => {
      monthlyData[month] = 0;
    });

    // Somar despesas por mês
    (expenses || []).forEach((expense: any) => {
      const paidDate = new Date(expense.paid_at);
      const monthIndex = paidDate.getMonth();
      const monthName = months[monthIndex];
      monthlyData[monthName] += ensureNumber(expense.amount);
    });

    // Converter para array
    const chartData: MonthlyExpenseData[] = months.map(month => ({
      month,
      expense: monthlyData[month],
      formattedExpense: formatCurrency(monthlyData[month])
    }));

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getMonthlyExpenseChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

// ============================================================================
// GRÁFICO DE DESPESAS FIXAS VS VARIÁVEIS
// ============================================================================

/**
 * Buscar dados para gráfico de despesas fixas vs variáveis
 */
export async function getFixedVsVariableExpensesChart(
  dateRange: DateRange
): Promise<DashboardActionResult<ExpenseTypeData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const { data: expenses, error } = await supabase
      .from('expenses')
      .select('amount, is_recurring, status')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('paid_at', formatDateForSQL(dateRange.startDate))
      .lte('paid_at', formatDateForSQL(dateRange.endDate));

    if (error) {
      console.error('Error fetching fixed vs variable expenses:', error);
      return { success: false, error: 'Erro ao buscar despesas fixas vs variáveis' };
    }

    // Separar despesas fixas e variáveis
    let fixedAmount = 0;
    let variableAmount = 0;
    let fixedCount = 0;
    let variableCount = 0;

    (expenses || []).forEach((expense: any) => {
      const amount = ensureNumber(expense.amount);
      if (expense.is_recurring) {
        fixedAmount += amount;
        fixedCount += 1;
      } else {
        variableAmount += amount;
        variableCount += 1;
      }
    });

    const totalAmount = fixedAmount + variableAmount;

    const chartData: ExpenseTypeData[] = [
      {
        type: 'fixed',
        label: 'Despesas Fixas',
        amount: fixedAmount,
        count: fixedCount,
        percentage: totalAmount > 0 ? Math.round((fixedAmount / totalAmount) * 100 * 100) / 100 : 0,
        formattedAmount: formatCurrency(fixedAmount),
        color: '#ef4444' // red
      },
      {
        type: 'variable',
        label: 'Despesas Variáveis',
        amount: variableAmount,
        count: variableCount,
        percentage: totalAmount > 0 ? Math.round((variableAmount / totalAmount) * 100 * 100) / 100 : 0,
        formattedAmount: formatCurrency(variableAmount),
        color: '#3b82f6' // blue
      }
    ];

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getFixedVsVariableExpensesChart:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}

// ============================================================================
// LISTA DE DESPESAS PENDENTES
// ============================================================================

/**
 * Buscar lista de despesas pendentes
 */
export async function getPendingExpensesList(): Promise<DashboardActionResult<PendingExpenseData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    const { data: expenses, error } = await supabase
      .from('expenses')
      .select(`
        id,
        supplier_name,
        amount,
        due_date,
        description,
        status,
        expense_categories (
          name,
          color
        )
      `)
      .eq('tenant_id', tenantId)
      .in('status', ['pending', 'overdue'])
      .order('due_date', { ascending: true })
      .limit(10);

    if (error) {
      console.error('Error fetching pending expenses:', error);
      return { success: false, error: 'Erro ao buscar despesas pendentes' };
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const chartData: PendingExpenseData[] = (expenses || []).map((expense: any) => {
      const dueDate = new Date(expense.due_date);
      const daysDiff = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

      return {
        id: expense.id,
        supplierName: expense.supplier_name,
        amount: ensureNumber(expense.amount),
        formattedAmount: formatCurrency(expense.amount),
        dueDate: expense.due_date,
        formattedDueDate: dueDate.toLocaleDateString('pt-BR'),
        daysPastDue: Math.max(0, daysDiff),
        categoryName: expense.expense_categories?.name || 'Sem categoria',
        categoryColor: expense.expense_categories?.color || '#6B7280',
        description: expense.description,
        status: expense.status as 'pending' | 'overdue'
      };
    });

    return { success: true, data: chartData };
  } catch (error) {
    console.error('Error in getPendingExpensesList:', error);
    return { success: false, error: 'Erro interno do servidor' };
  }
}
