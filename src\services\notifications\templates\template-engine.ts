/**
 * Template Engine para renderização de templates de notificação
 * Suporta sintaxe {{variableName}} para substituição de variáveis
 */

import type { TemplateVariable } from '../types/notification-types';

export interface RenderOptions {
  escapeHtml?: boolean;
  allowMissingVariables?: boolean;
  defaultValues?: Record<string, any>;
}

export interface RenderResult {
  rendered: string;
  missingVariables: string[];
  usedVariables: string[];
}

export class TemplateEngine {
  private static readonly VARIABLE_REGEX = /\{\{([^}]+)\}\}/g;

  /**
   * Renderiza um template substituindo variáveis
   */
  static render(
    template: string,
    variables: Record<string, any>,
    options: RenderOptions = {}
  ): RenderResult {
    const {
      escapeHtml = true,
      allowMissingVariables = false,
      defaultValues = {}
    } = options;

    const usedVariables: string[] = [];
    const missingVariables: string[] = [];

    const rendered = template.replace(this.VARIABLE_REGEX, (match, variablePath) => {
      const trimmedPath = variablePath.trim();
      usedVariables.push(trimmedPath);

      // Buscar valor da variável (suporta caminhos aninhados como academy.name)
      const value = this.getNestedValue(variables, trimmedPath) 
        ?? this.getNestedValue(defaultValues, trimmedPath);

      if (value === undefined || value === null) {
        missingVariables.push(trimmedPath);
        
        if (!allowMissingVariables) {
          return match; // Manter a variável original se não encontrada
        }
        
        return ''; // Substituir por string vazia se permitir variáveis faltantes
      }

      const stringValue = String(value);
      return escapeHtml ? this.escapeHtml(stringValue) : stringValue;
    });

    return {
      rendered,
      missingVariables: [...new Set(missingVariables)], // Remove duplicatas
      usedVariables: [...new Set(usedVariables)] // Remove duplicatas
    };
  }

  /**
   * Extrai todas as variáveis usadas em um template
   */
  static extractVariables(template: string): string[] {
    const variables: string[] = [];
    let match;

    while ((match = this.VARIABLE_REGEX.exec(template)) !== null) {
      variables.push(match[1].trim());
    }

    // Reset regex lastIndex para próximas execuções
    this.VARIABLE_REGEX.lastIndex = 0;

    return [...new Set(variables)]; // Remove duplicatas
  }

  /**
   * Valida se um template tem sintaxe válida
   */
  static validateSyntax(template: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Verificar se todas as chaves estão balanceadas
    const openBraces = (template.match(/\{\{/g) || []).length;
    const closeBraces = (template.match(/\}\}/g) || []).length;

    if (openBraces !== closeBraces) {
      errors.push('Chaves não balanceadas: número de {{ não corresponde ao número de }}');
    }

    // Verificar se há variáveis vazias
    if (template.includes('{{}}')) {
      errors.push('Variável vazia encontrada: {{}}');
    }

    // Verificar se há espaços em branco apenas
    const emptyVariables = template.match(/\{\{\s*\}\}/g);
    if (emptyVariables) {
      errors.push('Variáveis com apenas espaços em branco encontradas');
    }

    // Verificar caracteres inválidos em nomes de variáveis
    const variables = this.extractVariables(template);
    for (const variable of variables) {
      if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(variable)) {
        errors.push(`Nome de variável inválido: ${variable}. Use apenas letras, números, underscore e pontos.`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Busca valor em objeto aninhado usando caminho com pontos
   */
  private static getNestedValue(obj: Record<string, any>, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' ? current[key] : undefined;
    }, obj);
  }

  /**
   * Escapa HTML para prevenir XSS
   */
  private static escapeHtml(text: string): string {
    const htmlEscapes: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;'
    };

    return text.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
  }

  /**
   * Cria variáveis de exemplo baseadas no tipo de dados
   */
  static createExampleVariables(templateVariables: TemplateVariable[]): Record<string, any> {
    const examples: Record<string, any> = {};

    for (const variable of templateVariables) {
      const value = variable.example_value || this.getDefaultValueByType(variable.data_type);
      
      // Suportar caminhos aninhados
      if (variable.variable_key.includes('.')) {
        this.setNestedValue(examples, variable.variable_key, value);
      } else {
        examples[variable.variable_key] = value;
      }
    }

    return examples;
  }

  /**
   * Define valor em objeto aninhado usando caminho com pontos
   */
  private static setNestedValue(obj: Record<string, any>, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);

    target[lastKey] = value;
  }

  /**
   * Retorna valor padrão baseado no tipo de dados
   */
  private static getDefaultValueByType(dataType: string): any {
    switch (dataType) {
      case 'string':
        return 'Exemplo de texto';
      case 'number':
        return 123;
      case 'date':
        return new Date().toLocaleDateString('pt-BR');
      case 'boolean':
        return true;
      case 'url':
        return 'https://exemplo.com';
      default:
        return 'Valor de exemplo';
    }
  }

  /**
   * Renderiza template com dados da academia
   */
  static renderWithAcademyData(
    template: string,
    variables: Record<string, any>,
    academyData: {
      name: string;
      logo_url?: string;
      primary_color?: string;
      secondary_color?: string;
      slug: string;
    }
  ): RenderResult {
    // Combinar variáveis com dados da academia
    const combinedVariables = {
      ...variables,
      academyName: academyData.name,
      academyLogo: academyData.logo_url || '',
      primaryColor: academyData.primary_color || '#333333',
      secondaryColor: academyData.secondary_color || '#666666',
      academySlug: academyData.slug
    };

    return this.render(template, combinedVariables, {
      escapeHtml: true,
      allowMissingVariables: false
    });
  }
}
