import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não autenticado' },
        { status: 401 }
      )
    }

    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant não identificado' },
        { status: 400 }
      )
    }

    // Buscar todos os planos do tenant
    const { data: plansData, error: plansError } = await supabase
      .from('plans')
      .select(`
        id,
        title,
        status,
        pricing_config,
        access_config,
        benefits
      `)
      .eq('tenant_id', tenantId)
      .in('status', ['active', 'draft', 'paused'])
      .order('title')

    if (plansError) {
      return NextResponse.json(
        { error: `Erro ao buscar planos: ${plansError.message}` },
        { status: 500 }
      )
    }

    // Buscar todas as modalidades do tenant para fazer o mapeamento
    const { data: modalitiesData } = await supabase
      .from('modalities')
      .select('id, name')
      .eq('tenant_id', tenantId)

    const modalitiesMap = new Map(
      (modalitiesData || []).map((m: any) => [m.id, m.name])
    )

    // Transformar os dados dos planos
    const availablePlans = (plansData || []).map((plan: any) => {
      const pricingConfig = plan.pricing_config || {}
      const accessConfig = plan.access_config || {}

      // Mapear IDs de modalidades para nomes
      let modalityNames: string[] = []
      if (accessConfig.modalities && Array.isArray(accessConfig.modalities)) {
        modalityNames = accessConfig.modalities
          .map((id: string) => modalitiesMap.get(id))
          .filter(Boolean)
      }

      // Determinar o período de cobrança baseado no tipo de plano
      let billingPeriod = 'Mensal'
      const planType = pricingConfig.type || pricingConfig.tipo || 'recurring'

      if (planType === 'one-time') {
        billingPeriod = 'Pagamento único'
      } else if (pricingConfig.frequency && pricingConfig.frequency_number) {
        const frequency = pricingConfig.frequency
        const number = pricingConfig.frequency_number

        if (frequency === 'month') {
          billingPeriod = number === 1 ? 'Mensal' : `${number} meses`
        } else if (frequency === 'year') {
          billingPeriod = number === 1 ? 'Anual' : `${number} anos`
        } else if (frequency === 'week') {
          billingPeriod = number === 1 ? 'Semanal' : `${number} semanas`
        } else if (frequency === 'day') {
          billingPeriod = number === 1 ? 'Diário' : `${number} dias`
        }
      }

      return {
        id: plan.id,
        title: plan.title,
        price: parseFloat(pricingConfig.amount || '0'),
        billing_period: billingPeriod,
        modalities: modalityNames,
        benefits: plan.benefits || [],
        status: plan.status,
        pricing_config: pricingConfig // Incluir o pricing_config completo
      }
    })

    return NextResponse.json(availablePlans)
  } catch (error) {
    console.error('Erro ao buscar planos disponíveis:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
