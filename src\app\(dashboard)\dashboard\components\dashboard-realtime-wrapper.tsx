'use client';

import { DashboardData } from '../types';
import { useRealtimeDashboard } from '../hooks/use-realtime-dashboard';
import { DashboardSkeleton } from './dashboard-skeleton';
import {
  DashboardStatsGrid,
  RecentActivityList,
  StudentsLineChart,
  StudentsPieChart,
  RevenueGoal,
  InstructorsBeltChart,
  InstructorsContractChart
} from './index';
import { RefreshCw, Play, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { LiveIndicator } from './live-indicator';
import { DebugNotificationButton } from './debug-notification-button';

interface DashboardRealtimeWrapperProps {
  tenantId: string;
  initialData: DashboardData;
}

export function DashboardRealtimeWrapper({
  tenantId,
  initialData
}: DashboardRealtimeWrapperProps) {
  const {
    data,
    isLoading,
    error,
    isConnected,
    isActive,
    lastUpdated,
    currentInterval,
    refreshData,
    togglePolling
  } = useRealtimeDashboard({
    tenantId,
    initialData,
    pollingInterval: 30000, // 30 segundos
    enablePolling: true
  });

  // Mostrar erro com opção de tentar novamente
  if (error && !data) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-red-600 dark:text-red-400">
            Erro ao carregar dashboard
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {error}
          </p>
        </div>
        <Button
          onClick={refreshData}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Tentar novamente
        </Button>
      </div>
    );
  }

  // Mostrar skeleton enquanto carrega dados iniciais
  if (isLoading && !data) {
    return <DashboardSkeleton />;
  }

  // Dados não disponíveis
  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-gray-600 dark:text-gray-400">
          Nenhum dado disponível
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Controles e Indicador Live */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <DebugNotificationButton />
        </div>
        <div className="flex items-center gap-4">
          <div className="text-xs text-muted-foreground">
            Intervalo: {Math.round(currentInterval / 1000)}s
            {isActive && " (ativo)"}
          </div>
          <LiveIndicator
            isConnected={isConnected}
            lastUpdated={lastUpdated || undefined}
            isActive={isActive}
          />
        </div>
      </div>

      {/* Indicador de erro (não-blocking) */}
      {error && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 rounded">
          <div className="flex items-center justify-between">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Houve um problema ao atualizar os dados. Os dados exibidos podem estar desatualizados.
                </p>
              </div>
            </div>
            <Button
              onClick={refreshData}
              variant="ghost"
              size="sm"
              className="text-yellow-700 dark:text-yellow-300 hover:bg-yellow-100 dark:hover:bg-yellow-800/30"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <DashboardStatsGrid stats={data.stats} />

      {/* Charts Row: Line chart on the left, 2x2 pie/other charts on the right */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Gráfico de linha dos estudantes ocupa maior parte da largura */}
        <div className="lg:col-span-8">
          <StudentsLineChart data={data.studentsChart} />
        </div>
        {/* Grid 2x2 dos gráficos menores */}
        <div className="lg:col-span-4">
          <div className="grid grid-cols-2 gap-4 h-full">
            <div className="aspect-square">
              <StudentsPieChart data={data.studentsChart} />
            </div>
            <div className="aspect-square">
              <RevenueGoal tenantId={tenantId} />
            </div>
            <div className="aspect-square">
              <InstructorsBeltChart data={data.instructorsBeltChart} />
            </div>
            <div className="aspect-square">
              <InstructorsContractChart data={data.instructorsContractChart} />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity abaixo dos gráficos */}
      <div className="grid grid-cols-1 gap-6">
        <RecentActivityList activities={data.recentActivity} />
      </div>
    </div>
  );
} 