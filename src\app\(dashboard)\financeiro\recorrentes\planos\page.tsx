import { getAllPlans } from '@/app/(dashboard)/academia/actions/plan-actions'
import { Metadata } from 'next'
import { PlanHeader } from './components/PlanHeader'
import { PlanMetrics } from './components/PlanMetrics'
import { PlanList } from './components/PlanList'

export const metadata: Metadata = {
  title: 'Recorrentes - Planos',
  description: 'Gerencie os planos de assinatura da academia'
}

export default async function PlanosPage() {
  const plansResult = await getAllPlans()
  const plans = plansResult.success && Array.isArray(plansResult.data) ? plansResult.data : []
  console.log(plans)

  return (
    <>
      <PlanHeader />
      <PlanMetrics />
      <PlanList plans={plans} />
    </>
  )
}
