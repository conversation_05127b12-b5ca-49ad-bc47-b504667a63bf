'use client'

import { useEffect } from 'react'
import { usePageTitle } from '@/contexts/PageTitleContext'
import { CreditCard } from 'lucide-react'
import { CheckoutForm } from './CheckoutForm'

interface CheckoutPageContentProps {
  paymentId: string
  userId: string
}

export function CheckoutPageContent({ paymentId, userId }: CheckoutPageContentProps) {
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle()

  useEffect(() => {
    setPageTitle('Checkout - Pagamento PIX')
    setPageSubtitle('Finalize seu pagamento de forma rápida e segura')
    setPageIcon(<CreditCard className="h-6 w-6 text-primary" />)

    // Cleanup ao desmontar o componente
    return () => {
      setPageTitle(null)
      setPageSubtitle(null)
      setPageIcon(null)
    }
  }, [setPageTitle, setPageSubtitle, setPageIcon])

  return (
    <div className="min-h-screen bg-gradient-to-br">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <CheckoutForm paymentId={paymentId} userId={userId} />
        </div>
      </div>
    </div>
  )
}
