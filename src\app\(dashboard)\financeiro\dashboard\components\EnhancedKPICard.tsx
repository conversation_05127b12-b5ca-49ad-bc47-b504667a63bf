"use client";

/**
 * Componente de KPI Card Aprimorado - Fase 2
 * Card de KPI com indicadores visuais avançados, comparações e tendências
 */

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Too<PERSON>ip<PERSON>ontent, TooltipProvider, TooltipRoot, TooltipTrigger } from '@/components/ui/tooltip';
import { Info, TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';

import { MetricWithGrowth } from '../types/dashboard-types';
import { TrendIndicator, Sparkline, StatusBadge } from './TrendIndicator';

// ============================================================================
// TIPOS
// ============================================================================

interface EnhancedKPICardProps {
  title: string;
  metric: MetricWithGrowth;
  icon?: React.ReactNode;
  description?: string;
  target?: number;
  sparklineData?: number[];
  status?: 'excellent' | 'good' | 'warning' | 'critical';
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

const getCardVariantStyles = (variant: string) => {
  switch (variant) {
    case 'compact':
      return 'p-4';
    case 'detailed':
      return 'p-6';
    default:
      return 'p-5';
  }
};

const getPerformanceStatus = (current: number, target?: number) => {
  if (!target) return null;
  
  const percentage = (current / target) * 100;
  
  if (percentage >= 100) return 'excellent';
  if (percentage >= 80) return 'good';
  if (percentage >= 60) return 'warning';
  return 'critical';
};

const formatTargetComparison = (current: number, target: number, isCurrency: boolean) => {
  const percentage = (current / target) * 100;
  const formatter = isCurrency 
    ? (val: number) => new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(val)
    : (val: number) => val.toLocaleString('pt-BR');
  
  return {
    percentage: percentage.toFixed(1),
    remaining: target - current,
    formattedTarget: formatter(target),
    formattedRemaining: formatter(Math.abs(target - current))
  };
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const EnhancedKPICard: React.FC<EnhancedKPICardProps> = ({
  title,
  metric,
  icon,
  description,
  target,
  sparklineData,
  status,
  className,
  variant = 'default'
}) => {
  const cardStyles = getCardVariantStyles(variant);
  const autoStatus = status || getPerformanceStatus(metric.current, target);
  const targetComparison = target ? formatTargetComparison(
    metric.current, 
    target, 
    metric.formatted.current.includes('R$')
  ) : null;

  const isCompact = variant === 'compact';
  const isDetailed = variant === 'detailed';

  return (
    <Card className={cn(
      "border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden transition-all duration-200 hover:shadow-xl",
      className
    )}>
      <CardHeader className={cn(
        "pb-3 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent",
        isCompact ? "pb-2" : "pb-3"
      )}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className={cn(
              "font-medium text-gray-600 dark:text-gray-400",
              isCompact ? "text-xs" : "text-sm"
            )}>
              {title}
            </CardTitle>
            {description && (
              <TooltipProvider>
                <TooltipRoot>
                  <TooltipTrigger>
                    <Info className="h-3 w-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs text-sm">{description}</p>
                  </TooltipContent>
                </TooltipRoot>
              </TooltipProvider>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {autoStatus && (
              <StatusBadge 
                status={autoStatus} 
                text={autoStatus === 'excellent' ? 'Excelente' : 
                      autoStatus === 'good' ? 'Bom' : 
                      autoStatus === 'warning' ? 'Atenção' : 'Crítico'} 
                size="sm"
              />
            )}
            {icon && (
              <div className="text-gray-400 dark:text-gray-500">
                {icon}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={cn("pt-0", cardStyles)}>
        <div className="space-y-3">
          {/* Valor Principal */}
          <div className="flex items-end justify-between">
            <div className={cn(
              "font-bold text-gray-900 dark:text-gray-100",
              isCompact ? "text-lg" : isDetailed ? "text-3xl" : "text-2xl"
            )}>
              {metric.formatted.current}
            </div>
            
            {sparklineData && sparklineData.length > 1 && (
              <div className="flex items-center gap-2">
                <Sparkline 
                  data={sparklineData} 
                  trend={metric.trend}
                  width={isCompact ? 40 : 60}
                  height={isCompact ? 15 : 20}
                />
                <TrendIndicator 
                  trend={metric.trend} 
                  growth={metric.growth}
                  size={isCompact ? "sm" : "md"}
                />
              </div>
            )}
          </div>

          {/* Comparação com Período Anterior */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendIndicator 
                trend={metric.trend} 
                growth={metric.growth}
                size="sm"
                showText
              />
              <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">
                vs período anterior
              </span>
            </div>
            
            {!isCompact && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Anterior: {metric.formatted.previous}
              </div>
            )}
          </div>

          {/* Meta e Progresso (se disponível) */}
          {targetComparison && isDetailed && (
            <div className="pt-2 border-t border-gray-100 dark:border-gray-700">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400">
                  Meta: {targetComparison.formattedTarget}
                </span>
                <span className={cn(
                  "font-medium",
                  parseFloat(targetComparison.percentage) >= 100 
                    ? "text-green-600 dark:text-green-400"
                    : parseFloat(targetComparison.percentage) >= 80
                    ? "text-blue-600 dark:text-blue-400"
                    : "text-orange-600 dark:text-orange-400"
                )}>
                  {targetComparison.percentage}%
                </span>
              </div>
              
              {/* Barra de Progresso */}
              <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div 
                  className={cn(
                    "h-1.5 rounded-full transition-all duration-300",
                    parseFloat(targetComparison.percentage) >= 100 
                      ? "bg-green-500"
                      : parseFloat(targetComparison.percentage) >= 80
                      ? "bg-blue-500"
                      : "bg-orange-500"
                  )}
                  style={{ 
                    width: `${Math.min(parseFloat(targetComparison.percentage), 100)}%` 
                  }}
                />
              </div>
              
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {targetComparison.remaining > 0 
                  ? `Faltam ${targetComparison.formattedRemaining} para a meta`
                  : `Meta superada em ${targetComparison.formattedRemaining}`
                }
              </div>
            </div>
          )}

          {/* Insights Rápidos (apenas para versão detalhada) */}
          {isDetailed && (
            <div className="pt-2 border-t border-gray-100 dark:border-gray-700">
              <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                {metric.trend === 'up' && (
                  <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                    <TrendingUp className="h-3 w-3" />
                    <span>Tendência positiva</span>
                  </div>
                )}
                {metric.trend === 'down' && (
                  <div className="flex items-center gap-1 text-red-600 dark:text-red-400">
                    <TrendingDown className="h-3 w-3" />
                    <span>Requer atenção</span>
                  </div>
                )}
                {metric.trend === 'stable' && (
                  <div className="flex items-center gap-1">
                    <span>Estável</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
