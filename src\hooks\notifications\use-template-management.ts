/**
 * Hook para gerenciamento de templates de notificação
 * Fornece operações CRUD e funcionalidades avançadas
 */

'use client';

import { useState, useCallback } from 'react';
import { useTenant } from '@/hooks/tenant';
import { TemplateManagementService } from '@/services/notifications';
import type {
  NotificationTemplate,
  NotificationType,
  CreateTemplateData,
  UpdateTemplateData,
  TemplateValidationResult
} from '@/services/notifications/types/notification-types';

interface UseTemplateManagementReturn {
  // Estado
  templates: NotificationTemplate[];
  loading: boolean;
  error: string | null;
  
  // Operações CRUD
  loadTemplates: (type?: NotificationType) => Promise<void>;
  createTemplate: (data: CreateTemplateData) => Promise<NotificationTemplate | null>;
  updateTemplate: (id: string, data: UpdateTemplateData) => Promise<NotificationTemplate | null>;
  deleteTemplate: (id: string) => Promise<boolean>;
  duplicateTemplate: (id: string, newName: string) => Promise<NotificationTemplate | null>;
  
  // Operações avançadas
  validateTemplate: (template: string, type: NotificationType) => Promise<TemplateValidationResult | null>;
  resetToDefault: (id: string) => Promise<NotificationTemplate | null>;
  
  // Utilitários
  clearError: () => void;
  refreshTemplates: () => Promise<void>;
}

export function useTemplateManagement(): UseTemplateManagementReturn {
  const { tenantInfo } = useTenant();
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const templateService = new TemplateManagementService();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const loadTemplates = useCallback(async (type?: NotificationType) => {
    if (!tenantInfo?.id) return;

    setLoading(true);
    setError(null);

    try {
      const result = await templateService.getTemplatesByTenant(tenantInfo.id, type);
      
      if (result.success) {
        setTemplates(result.data || []);
      } else {
        setError(result.error || 'Erro ao carregar templates');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, [tenantInfo?.id, templateService]);

  const createTemplate = useCallback(async (data: CreateTemplateData): Promise<NotificationTemplate | null> => {
    if (!tenantInfo?.id) {
      setError('Tenant não encontrado');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await templateService.createTemplate(tenantInfo.id, data);
      
      if (result.success) {
        // Atualizar lista local
        setTemplates(prev => [result.data!, ...prev]);
        return result.data!;
      } else {
        setError(result.error || 'Erro ao criar template');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    } finally {
      setLoading(false);
    }
  }, [tenantInfo?.id, templateService]);

  const updateTemplate = useCallback(async (
    id: string, 
    data: UpdateTemplateData
  ): Promise<NotificationTemplate | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await templateService.updateTemplate(id, data);
      
      if (result.success) {
        // Atualizar lista local
        setTemplates(prev => 
          prev.map(template => 
            template.id === id ? result.data! : template
          )
        );
        return result.data!;
      } else {
        setError(result.error || 'Erro ao atualizar template');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    } finally {
      setLoading(false);
    }
  }, [templateService]);

  const deleteTemplate = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const result = await templateService.deleteTemplate(id);
      
      if (result.success) {
        // Remover da lista local
        setTemplates(prev => prev.filter(template => template.id !== id));
        return true;
      } else {
        setError(result.error || 'Erro ao deletar template');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return false;
    } finally {
      setLoading(false);
    }
  }, [templateService]);

  const duplicateTemplate = useCallback(async (
    id: string, 
    newName: string
  ): Promise<NotificationTemplate | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await templateService.duplicateTemplate(id, newName);
      
      if (result.success) {
        // Adicionar à lista local
        setTemplates(prev => [result.data!, ...prev]);
        return result.data!;
      } else {
        setError(result.error || 'Erro ao duplicar template');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    } finally {
      setLoading(false);
    }
  }, [templateService]);

  const validateTemplate = useCallback(async (
    template: string, 
    type: NotificationType
  ): Promise<TemplateValidationResult | null> => {
    setError(null);

    try {
      const result = await templateService.validateTemplate(template, type);
      
      if (result.success) {
        return result.data!;
      } else {
        setError(result.error || 'Erro ao validar template');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    }
  }, [templateService]);

  const resetToDefault = useCallback(async (id: string): Promise<NotificationTemplate | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await templateService.resetToDefault(id);
      
      if (result.success) {
        // Atualizar lista local
        setTemplates(prev => 
          prev.map(template => 
            template.id === id ? result.data! : template
          )
        );
        return result.data!;
      } else {
        setError(result.error || 'Erro ao resetar template');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    } finally {
      setLoading(false);
    }
  }, [templateService]);

  const refreshTemplates = useCallback(async () => {
    await loadTemplates();
  }, [loadTemplates]);

  return {
    // Estado
    templates,
    loading,
    error,
    
    // Operações CRUD
    loadTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    
    // Operações avançadas
    validateTemplate,
    resetToDefault,
    
    // Utilitários
    clearError,
    refreshTemplates
  };
}
