-- Migration: Add multiple memberships setting to tenants
-- Purpose: Add configuration to control whether students can have multiple active memberships
-- Affected tables: tenants (modified)
-- Dependencies: 003_create_memberships_table.sql
-- Date: 2024-12-21

-- Add function to safely update tenant settings JSONB
create or replace function public.update_tenant_setting(
  p_tenant_id uuid,
  p_key text,
  p_value jsonb
)
returns void
language plpgsql
security invoker
set search_path = ''
as $$
begin
  update public.tenants 
  set settings = coalesce(settings, '{}'::jsonb) || jsonb_build_object(p_key, p_value)
  where id = p_tenant_id;
end;
$$;

-- Add the multiple memberships setting to all existing tenants
-- Default to false (single membership per student) for safety
update public.tenants 
set settings = coalesce(settings, '{}'::jsonb) || '{"allow_multiple_memberships": false}'::jsonb
where settings ->> 'allow_multiple_memberships' is null;

-- Create function to check single membership constraint
create or replace function public.check_single_membership()
returns trigger
language plpgsql
security invoker
set search_path = ''
as $$
declare
  allow_multiple boolean;
  active_count integer;
begin
  -- Only check for active memberships on INSERT and UPDATE to active status
  if (tg_op = 'INSERT' and new.status = 'active') or
     (tg_op = 'UPDATE' and new.status = 'active' and old.status != 'active') then
    
    -- Get tenant setting for multiple memberships
    select coalesce((settings ->> 'allow_multiple_memberships')::boolean, false)
    into allow_multiple
    from public.tenants
    where id = new.tenant_id;
    
    -- If multiple memberships are not allowed, check for existing active memberships
    if not allow_multiple then
      select count(*)
      into active_count
      from public.memberships
      where tenant_id = new.tenant_id
        and student_id = new.student_id
        and status = 'active'
        and id != coalesce(new.id, '00000000-0000-0000-0000-000000000000'::uuid);
      
      -- Raise exception if student already has an active membership
      if active_count > 0 then
        raise exception 'Student already has an active membership. Multiple active memberships are not allowed for this tenant.'
          using errcode = 'P0001',
                hint = 'Cancel or pause the existing membership before creating a new one, or enable multiple memberships in tenant settings.';
      end if;
    end if;
  end if;
  
  return new;
end;
$$;

-- Create trigger to enforce single membership constraint
create trigger check_single_membership_trigger
  before insert or update on public.memberships
  for each row
  execute function public.check_single_membership();

-- Add comment for the function
comment on function public.check_single_membership() is 'Enforces single active membership per student when tenant setting allow_multiple_memberships is false.';

-- Add comment for the setting
comment on function public.update_tenant_setting(uuid, text, jsonb) is 'Safely updates a specific setting in the tenant settings JSONB field.'; 