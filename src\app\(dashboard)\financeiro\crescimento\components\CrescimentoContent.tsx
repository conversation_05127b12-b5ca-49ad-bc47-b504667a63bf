'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, Download } from 'lucide-react';
import { GrowthMetrics } from './GrowthMetrics';
import { GrowthChart } from './GrowthChart';

export function CrescimentoContent() {
  const [viewMode, setViewMode] = useState<'year-to-date' | 'projection'>('year-to-date');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Análise de Crescimento</h1>
            <p className="text-muted-foreground">
              Análise de crescimento da receita e métricas de expansão da academia.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>
      </div>

      {/* Seletor de visualização */}
      <div className="flex gap-2 mb-6">
        <Button
          variant={viewMode === 'year-to-date' ? 'default' : 'outline'}
          onClick={() => setViewMode('year-to-date')}
          className="flex-1 sm:flex-none"
        >
          Ano-a-data
        </Button>
        <Button
          variant={viewMode === 'projection' ? 'default' : 'outline'}
          onClick={() => setViewMode('projection')}
          className="flex-1 sm:flex-none"
        >
          Projeção até final do ano
        </Button>
      </div>

      {/* Layout com métricas na esquerda e gráfico na direita */}
      <div className="grid gap-6 lg:grid-cols-3 lg:items-start">
        {/* Métricas de crescimento - coluna da esquerda */}
        <div className="lg:col-span-1 flex flex-col">
          <GrowthMetrics viewMode={viewMode} />
        </div>

        {/* Gráfico de receita - coluna da direita */}
        <div className="lg:col-span-2 flex flex-col">
          <GrowthChart viewMode={viewMode} />
        </div>
      </div>
    </div>
  );
}
