'use client'

import React, { useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'
import { configTrialSchema } from '@/schemas/plan-schemas'

type TrialFormValues = z.infer<typeof configTrialSchema>

interface PlanFormTrialSectionProps {
  defaultValues?: Partial<TrialFormValues>
}

const timeUnitOptions = [
  { value: 'days', label: 'Dia(s)' },
  { value: 'weeks', label: 'Semana(s)' },
  { value: 'months', label: 'Mê<PERSON>(es)' }
]

const frequencyOptions = [
  { value: 'day', label: '<PERSON><PERSON><PERSON>' },
  { value: 'week', label: 'Semanal' },
  { value: 'month', label: 'Mensal' },
  { value: 'year', label: 'Anual' }
]

export function PlanFormTrialSection({
  defaultValues
}: PlanFormTrialSectionProps) {
  const { formData, updateSection } = usePlanForm()

  // Obter dados do pricing quando o tipo for trial
  const trialData = formData.pricing?.tipo === 'trial' ? formData.pricing : null;
  const defaultTrialValues = defaultValues as any;
  
  const {
    register,
    formState: { errors },
    control,
    watch,
    getValues,
  } = useForm<TrialFormValues>({
    resolver: zodResolver(configTrialSchema),
    mode: 'onBlur',
    defaultValues: {
      tipo: 'trial',
      duracao: {
        valor:
          trialData?.duracao?.valor ? Number(trialData.duracao.valor) :
          defaultTrialValues?.duracao?.valor ? Number(defaultTrialValues.duracao.valor) :
          7,
        unidade: trialData?.duracao?.unidade || defaultTrialValues?.duracao?.unidade || 'days',
      },
      valorDuranteTrial:
        trialData?.valorDuranteTrial ? Number(trialData.valorDuranteTrial) :
        defaultTrialValues?.valorDuranteTrial ?? 0,
      valorAposTrial:
        trialData?.valorAposTrial ? Number(trialData.valorAposTrial) :
        defaultTrialValues?.valorAposTrial ?? 0,
      frequenciaAposTrial: trialData?.frequenciaAposTrial || defaultTrialValues?.frequenciaAposTrial || 'month',
      taxaInscricao:
        trialData?.taxaInscricao ? Number(trialData.taxaInscricao) :
        defaultTrialValues?.taxaInscricao ?? 0,
    },
  })

  // Popula o contexto com os valores iniciais quando o componente é montado
  useEffect(() => {
    const initialValues = getValues()
    if (formData.pricing?.tipo === 'trial') {
      updateSection('pricing', initialValues)
    }
  }, [formData.pricing?.tipo])

  const formatToCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value)
  }

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>, fieldOnChange: (value: number) => void) => {
    const value = e.target.value.replace(/\D/g, '')
    fieldOnChange(Number(value) / 100)
  }

  const duracaoValor = watch('duracao.valor')
  const duracaoUnidade = watch('duracao.unidade')

  // Sincronizar mudanças com o context apenas se o tipo de pricing for trial
  useEffect(() => {
    const subscription = watch((value) => {
      // Só atualizar se o tipo de pricing for trial
      if (formData.pricing?.tipo === 'trial') {
        // Mantém a seção pricing alinhada ao schema global
        updateSection('pricing', value as TrialFormValues)
      }
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection, formData.pricing?.tipo])

  return (
    <div className="space-y-6">

      {/* Seção de Duração do Trial */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Período de Teste</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Duração */}
          <div className="space-y-2">
            <Label htmlFor="duracao.valor" className="text-sm font-medium">
              DURAÇÃO
            </Label>
            <Input
              id="duracao.valor"
              placeholder="7"
              type="number"
              {...register('duracao.valor', { valueAsNumber: true })}
              className={cn(
                'text-center',
                errors.duracao?.valor && 'border-red-500 focus:border-red-500'
              )}
            />
            {errors.duracao?.valor && (
              <p className="text-sm text-red-600">{errors.duracao.valor.message}</p>
            )}
          </div>

          {/* Unidade de Tempo */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">UNIDADE</Label>
            <Controller
              name="duracao.unidade"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {timeUnitOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Preview da duração */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">PREVIEW</Label>
            <div className="p-2 bg-muted rounded border text-sm text-muted-foreground text-center">
              {duracaoValor} {timeUnitOptions.find(opt => opt.value === duracaoUnidade)?.label?.toLowerCase()}
            </div>
          </div>
        </div>
      </div>

      {/* Seção de Preços */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Preços</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Valor durante o trial */}
          <div className="space-y-2">
            <Label htmlFor="valorDuranteTrial" className="text-sm font-medium">
              VALOR DURANTE O TESTE
            </Label>
            <Controller
              name="valorDuranteTrial"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    R$
                  </span>
                  <Input
                    id="valorDuranteTrial"
                    placeholder="0,00"
                    type="text"
                    value={field.value ? formatToCurrency(field.value) : ''}
                    onChange={(e) => handleCurrencyChange(e, field.onChange)}
                    className="pl-8"
                  />
                </div>
              )}
            />
            <p className="text-xs text-muted-foreground">
              Deixe em branco ou 0 para trial gratuito
            </p>
          </div>

          {/* Valor após o trial */}
          <div className="space-y-2">
            <Label htmlFor="valorAposTrial" className="text-sm font-medium">
              VALOR APÓS O TESTE
            </Label>
            <Controller
              name="valorAposTrial"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    R$
                  </span>
                  <Input
                    id="valorAposTrial"
                    placeholder="0,00"
                    type="text"
                    value={field.value ? formatToCurrency(field.value) : ''}
                    onChange={(e) => handleCurrencyChange(e, field.onChange)}
                    className={cn(
                      'pl-8',
                      errors.valorAposTrial && 'border-red-500 focus:border-red-500'
                    )}
                  />
                </div>
              )}
            />
            {errors.valorAposTrial && (
              <p className="text-sm text-red-600">{errors.valorAposTrial.message}</p>
            )}
          </div>
        </div>

        {/* Frequência após trial */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">FREQUÊNCIA DE COBRANÇA APÓS TRIAL</Label>
            <Controller
              name="frequenciaAposTrial"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    className={cn(
                      errors.frequenciaAposTrial && 'border-red-500 focus:border-red-500'
                    )}
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {frequencyOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.frequenciaAposTrial && (
              <p className="text-sm text-red-600">{errors.frequenciaAposTrial.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Seção de Taxas */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Taxas</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Taxa de Inscrição */}
          <div className="space-y-2">
            <Label htmlFor="taxaInscricao" className="text-sm font-medium">
              TAXA DE INSCRIÇÃO
            </Label>
            <Controller
              name="taxaInscricao"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                    R$
                  </span>
                  <Input
                    id="taxaInscricao"
                    placeholder="0,00"
                    type="text"
                    value={field.value ? formatToCurrency(field.value) : ''}
                    onChange={(e) => handleCurrencyChange(e, field.onChange)}
                    className="pl-8"
                  />
                </div>
              )}
            />
            <p className="text-xs text-muted-foreground">
              Taxa única cobrada na inscrição do plano
            </p>
          </div>
        </div>
      </div>

      {/* Informações Adicionais */}
      <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border-l-4 border-blue-500 dark:border-blue-400">
        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Como funciona o período de teste:</h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• O período de teste inicia imediatamente após a inscrição</li>
          <li>• Após o trial, a cobrança regular será iniciada automaticamente</li>
          <li>• O membro pode cancelar durante o período de teste sem cobrança</li>
          <li>• Notificações são enviadas antes do fim do período de teste</li>
        </ul>
      </div>
    </div>
  )
} 