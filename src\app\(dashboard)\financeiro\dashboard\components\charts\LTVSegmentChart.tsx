"use client";

/**
 * Gráfico de LTV por Segmento
 * Exibe o Lifetime Value médio segmentado por diferentes critérios
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { DollarSign, Users, TrendingUp, Layers } from 'lucide-react';
import { cn } from '@/lib/utils';

import { formatCurrency, formatNumber } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface LTVSegmentData {
  segment: string;
  ltv: number;
  studentCount: number;
  averageMonthlyRevenue: number;
  averageLifespan: number; // em meses
}

interface LTVSegmentChartProps {
  data?: LTVSegmentData[];
  loading?: boolean;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          {label}
        </p>
        <div className="space-y-1">
          <p className="text-sm text-purple-600 dark:text-purple-400">
            <span className="font-medium">LTV: </span>
            {formatCurrency(data.ltv)}
          </p>
          <p className="text-sm text-blue-600 dark:text-blue-400">
            <span className="font-medium">Alunos: </span>
            {formatNumber(data.studentCount)}
          </p>
          <p className="text-sm text-green-600 dark:text-green-400">
            <span className="font-medium">Receita Mensal: </span>
            {formatCurrency(data.averageMonthlyRevenue)}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <span className="font-medium">Permanência: </span>
            {data.averageLifespan} meses
          </p>
        </div>
      </div>
    );
  }
  return null;
};

// ============================================================================
// DADOS MOCK PARA DEMONSTRAÇÃO
// ============================================================================

const mockData: LTVSegmentData[] = [
  {
    segment: 'Jiu-Jitsu',
    ltv: 2400,
    studentCount: 35,
    averageMonthlyRevenue: 200,
    averageLifespan: 12
  },
  {
    segment: 'Muay Thai',
    ltv: 1800,
    studentCount: 28,
    averageMonthlyRevenue: 180,
    averageLifespan: 10
  },
  {
    segment: 'Boxe',
    ltv: 1500,
    studentCount: 22,
    averageMonthlyRevenue: 150,
    averageLifespan: 10
  },
  {
    segment: 'MMA',
    ltv: 2100,
    studentCount: 18,
    averageMonthlyRevenue: 210,
    averageLifespan: 10
  },
  {
    segment: 'Funcional',
    ltv: 1200,
    studentCount: 25,
    averageMonthlyRevenue: 120,
    averageLifespan: 10
  }
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const LTVSegmentChart: React.FC<LTVSegmentChartProps> = ({
  data = mockData,
  loading = false,
  className = ''
}) => {
  const [chartData, setChartData] = useState<LTVSegmentData[]>([]);

  useEffect(() => {
    if (data) {
      // Ordenar por LTV decrescente
      const sortedData = [...data].sort((a, b) => b.ltv - a.ltv);
      setChartData(sortedData);
    }
  }, [data]);

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            LTV por Segmento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (chartData.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            LTV por Segmento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <Layers className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Nenhum dado de segmentação disponível</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalStudents = chartData.reduce((sum, item) => sum + item.studentCount, 0);
  const averageLTV = chartData.reduce((sum, item) => sum + (item.ltv * item.studentCount), 0) / totalStudents;
  const highestLTV = Math.max(...chartData.map(item => item.ltv));
  const bestSegment = chartData.find(item => item.ltv === highestLTV);

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            LTV por Segmento
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {chartData.length} segmentos
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="segment" 
                className="text-xs"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => formatCurrency(value, { minimumFractionDigits: 0 })}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="ltv" 
                fill="#8b5cf6"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Estatísticas Resumidas */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">LTV Médio</div>
            <div className="text-lg font-semibold text-purple-600 dark:text-purple-400">
              {formatCurrency(averageLTV)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">Maior LTV</div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              {formatCurrency(highestLTV)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">Melhor Segmento</div>
            <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {bestSegment?.segment || '-'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">Total Alunos</div>
            <div className="text-lg font-semibold text-gray-600 dark:text-gray-400">
              {formatNumber(totalStudents)}
            </div>
          </div>
        </div>

        {/* Ranking dos Segmentos */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
            Ranking por LTV
          </h4>
          <div className="space-y-2">
            {chartData.slice(0, 3).map((item, index) => (
              <div key={item.segment} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white",
                    index === 0 ? "bg-yellow-500" : index === 1 ? "bg-gray-400" : "bg-orange-500"
                  )}>
                    {index + 1}
                  </div>
                  <span className="font-medium">{item.segment}</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-purple-600 dark:text-purple-400">
                    {formatCurrency(item.ltv)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatNumber(item.studentCount)} alunos
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Insights */}
        <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
              Insights de LTV
            </span>
          </div>
          <div className="text-sm text-purple-700 dark:text-purple-300">
            {bestSegment && (
              <>
                O segmento <strong>{bestSegment.segment}</strong> apresenta o maior LTV 
                ({formatCurrency(bestSegment.ltv)}), indicando maior valor por aluno. 
                Considere estratégias para atrair mais alunos para este segmento.
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
