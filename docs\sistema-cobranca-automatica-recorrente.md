# Sistema de Cobrança Automática Recorrente

## 📋 Resumo da Implementação

Implementado um sistema onde:
1. **Quando um plano recorrente é atribuído**: Cria apenas **1 cobrança inicial**
2. **Quando uma cobrança é paga**: Automaticamente cria a **próxima cobrança do mês seguinte**

## 🔧 Componentes Implementados

### 1. Função `create_next_recurring_payment`

**Arquivo:** `migrations/create_next_recurring_payment_function.sql`

**Funcionalidade:**
- Cria automaticamente o próximo pagamento recorrente quando um pagamento é marcado como pago
- Calcula a próxima data de vencimento baseada na frequência do plano (weekly, monthly, yearly)
- Evita duplicação verificando se já existe um pagamento para a próxima data
- Atualiza o `next_billing_date` da membership

**Exemplo de uso:**
```sql
SELECT create_next_recurring_payment('payment-uuid'::uuid);
```

### 2. Trigger Automático

**Trigger:** `trigger_auto_create_next_payment`
**Função:** `trigger_auto_create_next_recurring_payment()`

**Funcionalidade:**
- Executado automaticamente quando um pagamento recorrente muda de status para 'paid'
- Chama a função `create_next_recurring_payment` para criar o próximo pagamento
- Registra warnings em caso de erro para facilitar debug

### 3. Atualização da Quantidade Inicial

**Arquivos modificados:**
- `src/app/(dashboard)/academia/actions/membership-actions.ts`
- `src/services/billing/payment-service.ts`
- `migrations/update_recurring_payments_default_count.sql`

**Mudanças:**
- Valor padrão alterado de **3 para 1** pagamento inicial
- Consistência em todos os pontos do sistema

## 🎯 Fluxo de Funcionamento

### Cenário: Atribuição de Plano Recorrente

1. **Usuário atribui plano recorrente ao aluno**
2. **Sistema cria membership**
3. **Sistema cria apenas 1 pagamento inicial** (due_date = data atual ou next_billing_date)
4. **Aluno tem 1 cobrança pendente**

### Cenário: Pagamento de Mensalidade

1. **Aluno paga a mensalidade** (status muda para 'paid')
2. **Trigger é executado automaticamente**
3. **Sistema cria próximo pagamento** (due_date = data_paga + 1 mês)
4. **Sistema atualiza next_billing_date da membership**
5. **Aluno tem nova cobrança pendente para o próximo mês**

## 📊 Exemplo Prático

```sql
-- Membership criada em 2025-01-19
-- Plano: R$ 150/mês

-- 1. Pagamento inicial criado
INSERT INTO payments (due_date, amount, status) 
VALUES ('2025-01-19', 150, 'pending');

-- 2. Aluno paga em 2025-01-20
UPDATE payments SET status = 'paid' WHERE id = 'payment-1';

-- 3. Trigger cria automaticamente:
INSERT INTO payments (due_date, amount, status, auto_created) 
VALUES ('2025-02-19', 150, 'pending', true);

-- 4. Aluno paga em 2025-02-18
UPDATE payments SET status = 'paid' WHERE id = 'payment-2';

-- 5. Trigger cria automaticamente:
INSERT INTO payments (due_date, amount, status, auto_created) 
VALUES ('2025-03-19', 150, 'pending', true);
```

## ✅ Vantagens da Implementação

1. **Eficiência**: Apenas 1 pagamento por vez no banco
2. **Automação**: Não requer intervenção manual
3. **Flexibilidade**: Suporta diferentes frequências (weekly, monthly, yearly)
4. **Rastreabilidade**: Metadata indica pagamentos criados automaticamente
5. **Segurança**: Evita duplicação de pagamentos
6. **Performance**: Reduz carga no banco de dados

## 🔍 Monitoramento

### Verificar Funcionamento
```sql
-- Ver pagamentos com auto_created = true
SELECT 
  id,
  due_date,
  amount,
  status,
  metadata->>'auto_created' as auto_created,
  metadata->>'previous_payment_id' as previous_payment_id
FROM payments 
WHERE metadata->>'auto_created' = 'true'
ORDER BY created_at DESC;
```

### Logs de Debug
```sql
-- Verificar warnings no log do PostgreSQL
-- Procurar por: "Falha ao criar próximo pagamento recorrente"
```

## 🚀 Resultado Final

**Problema resolvido:** O sistema agora cria cobranças recorrentes de forma inteligente e automática, criando apenas 1 pagamento inicial e gerando automaticamente o próximo pagamento sempre que uma mensalidade é paga.

**Impacto:** Redução significativa de dados desnecessários no banco e automação completa do processo de cobrança recorrente.
