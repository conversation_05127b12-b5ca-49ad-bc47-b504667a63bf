/**
 * Hook otimizado para gerenciar status de aulas
 * Remove código de debug e otimiza cálculos
 */

import React, { useMemo } from 'react';
import {
  getClassStatus,
  isClassActive,
  isWithin24HoursAfterEnd
} from '@/lib/date-utils';

interface ClassData {
  start_time: string;
  end_time: string;
  status?: string;
  name?: string;
  class_group?: {
    name?: string;
  };
}

interface UseClassStatusReturn {
  status: 'scheduled' | 'completed' | 'ongoing' | 'cancelled' | 'rescheduled';
  isActive: boolean;
  isWithinTimeLimit: boolean;
  statusConfig: {
    variant: 'default' | 'secondary' | 'outline';
    className: string;
    label: string;
    icon: React.ReactElement;
  };
}

/**
 * Hook para calcular e memoizar o status da aula
 */
export function useClassStatus(classData: ClassData): UseClassStatusReturn {
  return useMemo(() => {
    const status = getClassStatus(
      classData.start_time,
      classData.end_time,
      classData.status
    );
    
    const isWithinTimeLimit = isWithin24HoursAfterEnd(classData.end_time);
    const isActive = isClassActive(
      classData.start_time,
      classData.end_time,
      classData.status
    );

    // Configuração do badge de status
    const getStatusConfig = () => {
      switch (status) {
        case 'ongoing':
          return {
            variant: 'default' as const,
            className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800',
            label: 'Em andamento',
            icon: <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
          };
        case 'scheduled':
          return {
            variant: 'secondary' as const,
            className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200 dark:border-blue-800',
            label: 'Próxima',
            icon: <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
          };
        case 'cancelled':
          return {
            variant: 'outline' as const,
            className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800',
            label: 'Cancelada',
            icon: <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
          };
        case 'rescheduled':
          return {
            variant: 'outline' as const,
            className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800',
            label: 'Reagendada',
            icon: <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2" />
          };
        default: // completed
          return {
            variant: 'outline' as const,
            className: 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700',
            label: 'Concluída',
            icon: <div className="w-2 h-2 bg-gray-400 rounded-full mr-2" />
          };
      }
    };

    return {
      status,
      isActive,
      isWithinTimeLimit,
      statusConfig: getStatusConfig()
    };
  }, [classData.start_time, classData.end_time, classData.status]);
}

/**
 * Hook para debug (apenas em desenvolvimento)
 * Será removido em produção pelo bundler
 */
export function useClassStatusDebug(classData: ClassData, classId: string) {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return useMemo(() => {
    // Debug info será tree-shaken em produção
    return {
      classId,
      startTime: classData.start_time,
      endTime: classData.end_time,
      status: classData.status,
      name: classData.name,
      groupName: classData.class_group?.name
    };
  }, [classData, classId]);
}
