import { Card } from "@/components/ui/card"
import { AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { PaymentInfoProps } from "../types/types"

export function PaymentInfo({ pagamentoInfo }: PaymentInfoProps) {
  const getDiaVencimento = () => {
    if (!pagamentoInfo.proximoVencimento) return '21'
    return format(pagamentoInfo.proximoVencimento, "dd", { locale: ptBR })
  }

  return (
    <Card className="p-6 bg-white dark:bg-slate-800">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
        Informações de Pagamento
      </h3>
      
      <div className="space-y-6">
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            PIX
          </h4>
          <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <p className="text-sm text-gray-500 mb-1">Chave PIX (CNPJ)</p>
            <p className="font-medium text-gray-900 dark:text-gray-100">
              00.000.000/0001-00
            </p>
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            Informações Importantes
          </h4>
          <ul className="space-y-2 text-sm text-gray-500">
            <li className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-primary" />
              Pagamentos até o dia {getDiaVencimento()} de cada mês
            </li>
            <li className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-primary" />
              Em caso de atraso, entre em contato com a administração
            </li>
          </ul>
        </div>
      </div>
    </Card>
  )
}
