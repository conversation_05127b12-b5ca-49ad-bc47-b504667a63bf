'use client';

import { useFormContext } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { MessageSquare } from 'lucide-react';
import { DespesaFormValues } from '../../schemas/despesa-schema';

export function AdditionalInfoSection() {
  const form = useFormContext<DespesaFormValues>();
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <MessageSquare className="h-5 w-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Informações Adicionais
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Observações e detalhes extras
          </p>
        </div>
      </div>

      {/* Descrição */}
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Descrição (Opcional)
            </FormLabel>
            <FormControl>
              <Textarea
                {...field}
                placeholder="Adicione observações, detalhes ou informações extras sobre esta despesa..."
                rows={4}
                className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200 resize-none"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
