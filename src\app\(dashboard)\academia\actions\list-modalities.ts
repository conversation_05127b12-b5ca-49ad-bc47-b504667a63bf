'use server'

import { requireAuth } from '@/services/auth/actions/auth-actions'
import { listModalities } from '@/services/modalities'

interface ActionResult {
  success: boolean
  modalities?: { id: string; slug: string; name: string; enabled: boolean }[]
  errors?: any
}

/**
 * Server Action: Lista modalidades ativas da academia (tenant) atual
 */
export async function listModalitiesAction(): Promise<ActionResult> {
  try {
    const { user } = await requireAuth()
    const tenantId: string | undefined = (user.app_metadata as any)?.tenant_id

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado' }
      }
    }

    const modalities = await listModalities(tenantId)

    return {
      success: true,
      modalities
    }
  } catch (error: any) {
    console.error('[listModalitiesAction] erro:', error)
    return {
      success: false,
      errors: { _form: 'Falha ao buscar modalidades' }
    }
  }
} 