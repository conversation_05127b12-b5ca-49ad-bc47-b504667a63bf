/**
 * Exportações do Serviço de Cobranças
 * Baseado no documento: docs/planejamento-sistema-cobrancas.md
 */

// Exportar tipos
export * from './payment-types'

// Exportar schemas
export * from './payment-schemas'

// Exportar serviço principal
export { PaymentService, paymentService } from './payment-service'

// Exportar actions
export {
  createManualPayment,
  createGraduationFeePayment,
  createSignupFeePayment,
  createCancellationFeePayment,
  processOverduePayments,
  updatePaymentStatus,
  getPaymentsByStudent,
  getPaymentsByMembership,
  getPaymentMetrics,
  getMonthlyPaymentMetrics
} from './payment-actions'

// Exportações específicas para facilitar o uso
export type {
  Payment,
  PaymentType,
  PaymentStatus,
  PaymentMetrics,
  PaymentRPCResult,
  CreateManualPaymentData,
  CreateGraduationFeeData,
  CreateSignupFeeData,
  CreateCancellationFeeData,
  UpdatePaymentStatusData,
  GetPaymentsByStudentData,
  GetPaymentsByMembershipData,
  ProcessOverduePaymentsData,
  ActionResult
} from './payment-types'

export {
  paymentTypeSchema,
  paymentStatusSchema,
  criarCobrancaManualSchema,
  criarTaxaGraduacaoSchema,
  criarTaxaInscricaoSchema,
  criarTaxaCancelamentoSchema,
  atualizarStatusPagamentoSchema,
  buscarPagamentosPorAlunoSchema,
  buscarPagamentosPorMembershipSchema,
  processarPagamentosAtrasadosSchema,
  metricasPagamentosSchema,
  paymentSchema
} from './payment-schemas'

export type {
  CriarCobrancaManualData,
  CriarTaxaGraduacaoData,
  CriarTaxaInscricaoData,
  CriarTaxaCancelamentoData,
  AtualizarStatusPagamentoData,
  BuscarPagamentosPorAlunoData,
  BuscarPagamentosPorMembershipData,
  ProcessarPagamentosAtrasadosData,
  MetricasPagamentosData,
  PaymentData
} from './payment-schemas'
