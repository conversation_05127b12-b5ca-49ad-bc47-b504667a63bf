"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Pie, Tooltip, ResponsiveContainer, Cell, Legend } from 'recharts';
import { CreditCard, Banknote, Building2 } from 'lucide-react';
import { FaPix } from 'react-icons/fa6';
import { cn } from '@/lib/utils';

import { getPaymentMethodsChart } from '../../actions/charts/payment-chart-actions';
import { PaymentMethodData } from '../../types/dashboard-types';
import { formatCurrency } from '@/utils/format-utils';

interface PaymentMethodsChartProps {
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}


const methodIcons = {
  'PIX': FaPix,
  'Cartão de Crédito': CreditCard,
  'Cartão de Débito': CreditCard,
  'Dinheiro': Banknote,
  'Transferência Bancária': Building2,
  'Boleto Bancário': Building2
};


const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100">
          {data.method}
        </p>
        <p className="text-sm text-blue-600 dark:text-blue-400">
          <span className="font-medium">Valor: </span>
          {data.formattedAmount}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Transações: </span>
          {data.count}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Participação: </span>
          {data.percentage.toFixed(1)}%
        </p>
      </div>
    );
  }
  return null;
};



export const PaymentMethodsChart: React.FC<PaymentMethodsChartProps> = ({
  className
}) => {
  const [data, setData] = useState<PaymentMethodData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getPaymentMethodsChart();

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        setData(result.data || []);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar gráfico de métodos de pagamento:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);


  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);
  const totalTransactions = data.reduce((sum, item) => sum + item.count, 0);


  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Métodos de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Métodos de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <div className="text-red-500 mb-2">
                <CreditCard className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Métodos de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-400 mb-2">
                <CreditCard className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                Nenhum método de pagamento encontrado
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Métodos de Pagamento
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
              <span className="font-medium">Total: {formatCurrency(totalAmount)}</span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                dataKey="amount"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend
                verticalAlign="bottom"
                height={36}
                formatter={(_, entry: any) => (
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {entry.payload.method}
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Lista de Métodos */}
        <div className="mt-4 space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          {data.map((item, index) => {
            const IconComponent = methodIcons[item.method as keyof typeof methodIcons] || CreditCard;

            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <IconComponent className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {item.method}
                  </span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {item.formattedAmount}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {item.count} transações • {item.percentage.toFixed(1)}%
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Resumo */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total de Transações</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {totalTransactions}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Ticket Médio</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {formatCurrency(totalTransactions > 0 ? totalAmount / totalTransactions : 0)}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
