'use server'

import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { cancelMembership } from '@/app/(dashboard)/academia/actions/membership-actions'

interface CancelPlanData {
  membershipId: string
  motivo?: string
  aplicarTaxaCancelamento?: boolean
}

interface ActionResult<T = unknown> {
  success: boolean
  data?: T
  errors?: Record<string, string>
}

/**
 * Server action para cancelar plano de um estudante
 * Obtém automaticamente o tenant_id do usuário atual
 */
export async function cancelStudentPlan(data: CancelPlanData): Promise<ActionResult> {
  try {
    // Obter usuário atual
    const user = await getCurrentUser()
    
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    // Obter tenant_id do usuário
    const tenantId = user.app_metadata?.tenant_id

    if (!tenantId) {
      return {
        success: false,
        errors: { _form: 'Tenant não identificado' }
      }
    }

    // Chamar a server action de cancelamento com o tenant_id
    const result = await cancelMembership({
      membershipId: data.membershipId,
      motivo: data.motivo || 'Cancelamento solicitado pelo administrador',
      aplicarTaxaCancelamento: data.aplicarTaxaCancelamento || false
    }, tenantId)

    return result
  } catch (error) {
    console.error('Erro ao cancelar plano do estudante:', error)
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    }
  }
}
