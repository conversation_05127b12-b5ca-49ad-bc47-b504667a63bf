// Real-time configuration for payments

export const REALTIME_CONFIG = {
    // Polling intervals in milliseconds
    POLLING_INTERVALS: {
        ACTIVE_FOCUSED: 5000,    // 5 seconds when tab is active and focused
        ACTIVE_UNFOCUSED: 10000, // 10 seconds when tab is active but window unfocused
        INACTIVE: 30000,         // 30 seconds when tab is inactive
        BACKGROUND: 0            // Paused when in background
    },

    // Error handling
    MAX_RETRY_ATTEMPTS: 3,
    BACKOFF_MULTIPLIER: 2,
    MAX_BACKOFF_DELAY: 60000, // 1 minute

    // Performance
    STALE_TIME: 1000,         // Consider data stale after 1 second
    CACHE_TIME: 5 * 60 * 1000, // Keep in cache for 5 minutes

    // Feature flags
    ENABLE_REAL_TIME: process.env.NODE_ENV !== 'test', // Disable in tests
    ENABLE_VISUAL_INDICATORS: true,
    ENABLE_POLLING_METRICS: process.env.NODE_ENV === 'development',

    // Visual feedback
    UPDATE_HIGHLIGHT_DURATION: 2000, // 2 seconds
    INDICATOR_UPDATE_DELAY: 500,     // 0.5 seconds

    // Development
    DEBUG_POLLING: process.env.NODE_ENV === 'development',
    LOG_CHANGES: process.env.NODE_ENV === 'development'
} as const

export type RealtimeConfig = typeof REALTIME_CONFIG