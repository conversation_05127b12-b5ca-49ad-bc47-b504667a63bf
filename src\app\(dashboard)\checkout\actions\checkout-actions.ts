'use server'

import { z } from 'zod'
import { createTenantServerClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { getPixSettings } from '@/app/(dashboard)/financeiro/formas-pagamento/actions/pix-config-actions'
import { CheckoutData } from '../types/checkout-types'
import { PixQRCodeService } from '../services/pix-qrcode-service'
import { CheckoutRateLimit } from '../[paymentId]/middleware'
import { CheckoutSecurityValidator } from '../utils/security-validators'

const getCheckoutDataSchema = z.object({
  paymentId: z.string().uuid('ID do pagamento inválido'),
  userId: z.string().uuid('ID do usuário inválido')
})

const confirmPaymentSchema = z.object({
  paymentId: z.string().uuid('ID do pagamento inválido')
})

const generatePixQRCodeSchema = z.object({
  paymentId: z.string().uuid('ID do pagamento inválido')
})

const getPendingPaymentNotificationsSchema = z.object({
  limit: z.number().min(1).max(100).optional().default(10)
})

const approvePaymentSchema = z.object({
  paymentId: z.string().uuid('ID do pagamento inválido'),
  approved: z.boolean()
})

/**
 * Busca todos os dados necessários para o checkout
 */
export async function getCheckoutData(input: z.infer<typeof getCheckoutDataSchema>) {
  try {
    const parsed = getCheckoutDataSchema.safeParse(input)
    if (!parsed.success) {
      return {
        success: false,
        error: 'Dados inválidos fornecidos'
      }
    }

    const { paymentId, userId } = parsed.data

    // Verificar autenticação
    const currentUser = await getCurrentUser()
    if (!currentUser || currentUser.id !== userId) {
      return {
        success: false,
        error: 'Acesso não autorizado'
      }
    }

    // Buscar pagamento diretamente sem usar a função RPC problemática
    const supabase = await createTenantServerClient()

    // Verificar se o pagamento existe e pertence ao usuário
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        students!inner (
          id,
          user_id,
          users!students_user_id_fkey (
            id,
            email,
            first_name,
            last_name,
            full_name
          )
        ),
        memberships (
          id,
          plan_id,
          plans (
            id,
            title
          )
        )
      `)
      .eq('id', paymentId)
      .eq('students.user_id', userId)
      .single()

    if (paymentError || !paymentData) {
      console.error('Erro ao buscar pagamento:', paymentError)
      return {
        success: false,
        error: 'Pagamento não encontrado ou acesso não autorizado'
      }
    }

    // Validar status do pagamento
    if (paymentData.status !== 'pending') {
      let errorMessage = 'Este pagamento não pode ser processado'

      switch (paymentData.status) {
        case 'paid':
          errorMessage = 'Este pagamento já foi realizado'
          break
        case 'cancelled':
          errorMessage = 'Este pagamento foi cancelado'
          break
        case 'awaiting_confirmation':
          errorMessage = 'Este pagamento já está aguardando confirmação'
          break
        case 'overdue':
          errorMessage = 'Este pagamento está vencido e não pode ser processado'
          break
      }

      return {
        success: false,
        error: errorMessage
      }
    }

    // Validar valor do pagamento
    if (paymentData.amount < 0.01 || paymentData.amount > 50000) {
      return {
        success: false,
        error: 'Valor do pagamento inválido'
      }
    }

    // As validações de status já foram feitas pelo SecurityValidator

    // Buscar configurações PIX
    const pixSettings = await getPixSettings()
    if (!pixSettings.pixKey) {
      return {
        success: false,
        error: 'Chave PIX não configurada. Entre em contato com a academia.'
      }
    }

    // Validar chave PIX
    if (!PixQRCodeService.validatePixKey(pixSettings.pixKey)) {
      return {
        success: false,
        error: 'Chave PIX inválida configurada. Entre em contato com a academia.'
      }
    }

    // Montar dados do checkout usando os dados da consulta direta
    const student = paymentData.students
    const membership = paymentData.memberships
    const user = student.users

    const checkoutData: CheckoutData = {
      payment: {
        id: paymentData.id,
        amount: paymentData.amount,
        currency: paymentData.currency,
        status: paymentData.status,
        payment_method: paymentData.payment_method,
        due_date: paymentData.due_date,
        description: paymentData.description,
        membership_id: paymentData.membership_id,
        student_id: paymentData.student_id,
        created_at: paymentData.created_at,
        updated_at: paymentData.updated_at
      },
      student: {
        id: student.id,
        name: user.full_name || `${user.first_name} ${user.last_name || ''}`.trim(),
        email: user.email
      },
      plan: {
        id: membership?.plan_id || '',
        title: membership?.plans?.title || 'Plano não encontrado',
        billing_period: 'monthly', // TODO: extrair do pricing_config se necessário
        pricing_config: null // Pode ser adicionado à função RPC se necessário
      },
      pixSettings: {
        pixKey: pixSettings.pixKey,
        updatedAt: new Date().toISOString()
      }
    }

    return {
      success: true,
      data: checkoutData
    }

  } catch (error) {
    console.error('Erro ao buscar dados do checkout:', error)
    return {
      success: false,
      error: 'Erro interno do servidor'
    }
  }
}

/**
 * Confirma que o usuário realizou o pagamento
 */
export async function confirmPayment(input: z.infer<typeof confirmPaymentSchema>) {
  try {
    const parsed = confirmPaymentSchema.safeParse(input)
    if (!parsed.success) {
      return {
        success: false,
        error: 'ID do pagamento inválido'
      }
    }

    const { paymentId } = parsed.data

    // Verificar autenticação
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      }
    }

    // Verificar rate limiting
    if (!CheckoutRateLimit.checkRateLimit(currentUser.id, 'confirm_payment')) {
      const timeUntilReset = CheckoutRateLimit.getTimeUntilReset(currentUser.id, 'confirm_payment')
      const minutesUntilReset = Math.ceil(timeUntilReset / (60 * 1000))

      return {
        success: false,
        error: `Muitas tentativas de confirmação. Tente novamente em ${minutesUntilReset} minutos.`
      }
    }

    const supabase = await createTenantServerClient()

    // Verificar se o pagamento pertence ao usuário usando join com students
    const { data: paymentCheck, error: checkError } = await supabase
      .from('payments')
      .select(`
        id,
        status,
        students!inner (
          id,
          user_id
        )
      `)
      .eq('id', paymentId)
      .eq('students.user_id', currentUser.id)
      .single()

    if (checkError || !paymentCheck) {
      return {
        success: false,
        error: 'Pagamento não encontrado ou acesso não autorizado'
      }
    }

    if (paymentCheck.status !== 'pending') {
      return {
        success: false,
        error: 'Este pagamento não pode ser confirmado'
      }
    }

    // Atualizar status do pagamento e método de pagamento para PIX
    const { error: updateError } = await supabase
      .from('payments')
      .update({
        status: 'awaiting_confirmation',
        payment_method: 'pix',
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentId)

    if (updateError) {
      console.error('Erro ao atualizar pagamento:', updateError)
      return {
        success: false,
        error: 'Erro ao confirmar pagamento'
      }
    }

    // TODO: Implementar notificação para admin
    // await notifyAdminPaymentConfirmation(paymentId)

    return {
      success: true,
      message: 'Pagamento confirmado! O administrador será notificado para validação.'
    }

  } catch (error) {
    console.error('Erro ao confirmar pagamento:', error)
    return {
      success: false,
      error: 'Erro interno do servidor'
    }
  }
}

/**
 * Gera QR Code PIX para um pagamento específico
 */
export async function generatePixQRCode(input: z.infer<typeof generatePixQRCodeSchema>) {
  try {
    const parsed = generatePixQRCodeSchema.safeParse(input)
    if (!parsed.success) {
      return {
        success: false,
        error: 'ID do pagamento inválido'
      }
    }

    const { paymentId } = parsed.data

    // Verificar autenticação
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      }
    }

    // Verificar rate limiting
    if (!CheckoutRateLimit.checkRateLimit(currentUser.id, 'generate_qr')) {
      const timeUntilReset = CheckoutRateLimit.getTimeUntilReset(currentUser.id, 'generate_qr')
      const minutesUntilReset = Math.ceil(timeUntilReset / (60 * 1000))

      return {
        success: false,
        error: `Muitas tentativas de geração de QR Code. Tente novamente em ${minutesUntilReset} minutos.`
      }
    }

    // Buscar dados do checkout
    const checkoutResult = await getCheckoutData({
      paymentId,
      userId: currentUser.id
    })

    if (!checkoutResult.success || !checkoutResult.data) {
      return {
        success: false,
        error: checkoutResult.error || 'Erro ao buscar dados do pagamento'
      }
    }

    const { payment, student, pixSettings } = checkoutResult.data

    // Gerar QR Code PIX
    const qrCodeResult = await PixQRCodeService.generatePixQRCode({
      pixKey: pixSettings.pixKey,
      amount: payment.amount,
      description: payment.description || `Pagamento - ${student.name}`,
      merchantName: 'Academia',
      merchantCity: 'Brasil'
    })

    return {
      success: true,
      data: {
        qrCode: qrCodeResult.qrCode,
        pixKey: qrCodeResult.pixKey,
        amount: qrCodeResult.amount,
        description: qrCodeResult.description,
        brCode: qrCodeResult.brCode
      }
    }

  } catch (error) {
    console.error('Erro ao gerar QR Code PIX:', error)
    return {
      success: false,
      error: 'Erro interno do servidor'
    }
  }
}

/**
 * Busca pagamentos aguardando confirmação (para admins)
 */
export async function getPendingPaymentNotifications(input?: z.infer<typeof getPendingPaymentNotificationsSchema>) {
  try {
    const parsed = getPendingPaymentNotificationsSchema.safeParse(input || {})
    if (!parsed.success) {
      return {
        success: false,
        error: 'Parâmetros inválidos'
      }
    }

    const { limit } = parsed.data

    // Verificar se é admin
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      }
    }

    const supabase = await createTenantServerClient()

    // Verificar se o usuário é admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single()

    if (userError || !userData || userData.role !== 'admin') {
      return {
        success: false,
        error: 'Acesso não autorizado'
      }
    }

    // Buscar pagamentos aguardando confirmação
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select(`
        id,
        amount,
        currency,
        status,
        description,
        due_date,
        updated_at,
        memberships!inner (
          id,
          student_id,
          plan_id,
          plans!inner (
            id,
            title
          )
        )
      `)
      .eq('status', 'awaiting_confirmation')
      .order('updated_at', { ascending: false })
      .limit(limit)

    if (paymentsError) {
      console.error('Erro ao buscar pagamentos pendentes:', paymentsError)
      return {
        success: false,
        error: 'Erro ao buscar pagamentos pendentes'
      }
    }

    // Buscar dados dos estudantes
    const studentIds = payments.map(p => p.memberships.student_id)
    const { data: students, error: studentsError } = await supabase
      .from('users')
      .select('id, first_name, last_name, full_name, email')
      .in('id', studentIds)

    if (studentsError) {
      console.error('Erro ao buscar dados dos estudantes:', studentsError)
      return {
        success: false,
        error: 'Erro ao buscar dados dos estudantes'
      }
    }

    // Combinar dados
    const notifications = payments.map(payment => {
      const student = students.find(s => s.id === payment.memberships.student_id)
      return {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        description: payment.description,
        due_date: payment.due_date,
        updated_at: payment.updated_at,
        student: {
          id: student?.id || '',
          name: student?.full_name || `${student?.first_name} ${student?.last_name || ''}`.trim(),
          email: student?.email || ''
        },
        plan: {
          id: payment.memberships.plans.id,
          title: payment.memberships.plans.title
        }
      }
    })

    return {
      success: true,
      data: notifications
    }

  } catch (error) {
    console.error('Erro ao buscar notificações de pagamento:', error)
    return {
      success: false,
      error: 'Erro interno do servidor'
    }
  }
}

/**
 * Aprova ou rejeita um pagamento (apenas admins)
 */
export async function approvePayment(input: z.infer<typeof approvePaymentSchema>) {
  try {
    const parsed = approvePaymentSchema.safeParse(input)
    if (!parsed.success) {
      return {
        success: false,
        error: 'Dados inválidos fornecidos'
      }
    }

    const { paymentId, approved } = parsed.data

    // Verificar se é admin
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      }
    }

    const supabase = await createTenantServerClient()

    // Verificar se o usuário é admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single()

    if (userError || !userData || userData.role !== 'admin') {
      return {
        success: false,
        error: 'Acesso não autorizado'
      }
    }

    // Verificar se o pagamento existe e está aguardando confirmação
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .select('id, status')
      .eq('id', paymentId)
      .single()

    if (paymentError || !paymentData) {
      return {
        success: false,
        error: 'Pagamento não encontrado'
      }
    }

    if (paymentData.status !== 'awaiting_confirmation') {
      return {
        success: false,
        error: 'Este pagamento não está aguardando confirmação'
      }
    }

    // Atualizar status do pagamento
    const newStatus = approved ? 'paid' : 'overdue'
    const { error: updateError } = await supabase
      .from('payments')
      .update({
        status: newStatus,
        paid_at: approved ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', paymentId)

    if (updateError) {
      console.error('Erro ao atualizar status do pagamento:', updateError)
      return {
        success: false,
        error: 'Erro ao atualizar pagamento'
      }
    }

    return {
      success: true,
      message: approved
        ? 'Pagamento aprovado com sucesso!'
        : 'Pagamento rejeitado com sucesso!'
    }

  } catch (error) {
    console.error('Erro ao aprovar/rejeitar pagamento:', error)
    return {
      success: false,
      error: 'Erro interno do servidor'
    }
  }
}


