---
description: 
globs: 
alwaysApply: true
---
# Seleção de Bibliotecas e Dependências

## Prioridade de Uso
1. **Funcionalidades nativas** do projeto e das tecnologias principais (Next.js, React, etc.)
2. **Bibliotecas já incluídas** no projeto ([package.json](mdc:package.json))
3. **Novas bibliotecas** apenas quando estritamente necessário

## Verificação Antes de Sugerir Novas Bibliotecas
Antes de sugerir uma nova biblioteca, verifique:

1. **Se a funcionalidade já existe no projeto**:
   - Procure em [src/utils](mdc:src/utils) e [src/lib](mdc:src/lib) por utilitários existentes
   - Verifique [src/hooks](mdc:src/hooks) para hooks personalizados
   - Consulte [src/components/ui](mdc:src/components/ui) para componentes de UI reutilizáveis

2. **Se a biblioteca já está instalada**:
   - Confira as dependências no [package.json](mdc:package.json)
   - Verifique se há alternativas no projeto que possam ser utilizadas

3. **Se a necessidade justifica uma nova dependência**:
   - Considere o custo de manutenção
   - Avalie o tamanho e impacto no bundle
   - Verifique a maturidade e suporte da biblioteca

## Padrões de Implementação
- Use **importações dinâmicas** para código raramente utilizado
- Prefira bibliotecas com **suporte a TypeScript** nativo
- Opte por soluções que seguem os mesmos paradigmas do projeto

## Exemplos de Dependências Existentes
- **UI/Componentes**: shadcn/ui, tailwindcss
- **Formulários**: react-hook-form, zod
- **Autenticação**: Supabase
- **Banco de Dados**: Supabase


