'use client'

import { useMemo } from 'react'
import {
  RefreshCw,
  CreditCard,
  Calendar as CalendarIcon,
  Clock as ClockIcon,
} from 'lucide-react'
import {
  Plan,
  PlanStatus,
} from '@/app/(dashboard)/financeiro/recorrentes/planos/types'

const priceTypeThemes: Record<
  string,
  {
    icon: React.ElementType
    iconColor: string
    bgColor: string
    priceColor: string
    borderColor?: string
    badgeClass?: string
  }
> = {
  recurring: {
    icon: RefreshCw,
    iconColor: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    priceColor: 'text-blue-600',
  },
  'one-time': {
    icon: CreditCard,
    iconColor: 'text-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    priceColor: 'text-purple-600',
  },
  'per-session': {
    icon: CalendarIcon,
    iconColor: 'text-green-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    priceColor: 'text-green-600',
  },
  trial: {
    icon: ClockIcon,
    iconColor: 'text-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    priceColor: 'text-orange-600',
  },
}

const adaptPlan = (plan: any): Plan => {
  const pricingConfig = plan.pricing_config || {}
  const durationConfig = plan.duration_config || {}

  const statusMap: { [key: string]: PlanStatus } = {
    active: 'active',
    draft: 'draft',
    archived: 'archived',
  }

  const priceType: string = pricingConfig.type || pricingConfig.tipo || 'recurring'

  let periodLabel = ''
  let billingLabel = ''
  let durationLabel: string | undefined

  if (priceType === 'one-time') {
    periodLabel = 'Pagamento único'
    billingLabel = 'Pagamento único'
  } else if (priceType === 'per-session') {
    periodLabel = 'por aula'
    billingLabel = 'Por Aula'
  } else if (priceType === 'trial') {
    // Suporte para ambos os formatos: duration (inglês) e duracao (português)
    const durationData = pricingConfig.duration || pricingConfig.duracao
    const trialDurationValue = durationData?.value || durationData?.valor || 0
    const trialDurationUnit = durationData?.unit || durationData?.unidade

    const unitMap: Record<string, string> = {
      days: 'dia',
      day: 'dia',
      dia: 'dia',
      dias: 'dia',
      weeks: 'semana',
      week: 'semana',
      semana: 'semana',
      semanas: 'semana',
      months: 'mês',
      month: 'mês',
      mes: 'mês',
      mês: 'mês',
      meses: 'mês',
    }

    const pluralize = (unit: string, qty: number) => {
      if (qty === 1) return unit
      if (unit === 'mês') return 'meses'
      if (unit === 'semana') return 'semanas'
      if (unit === 'dia') return 'dias'
      return unit
    }

    if (
      trialDurationValue > 0 &&
      trialDurationUnit &&
      unitMap[trialDurationUnit]
    ) {
      periodLabel = `${trialDurationValue} ${pluralize(
        unitMap[trialDurationUnit],
        trialDurationValue,
      )} de teste`
    } else {
      periodLabel = 'Período de teste'
    }

    const trialPrice = Number(pricingConfig.valorDuranteTrial) || 0

    if (trialPrice > 0) {
      if (
        trialDurationValue > 0 &&
        trialDurationUnit &&
        unitMap[trialDurationUnit]
      ) {
        periodLabel = `${trialDurationValue} ${pluralize(
          unitMap[trialDurationUnit],
          trialDurationValue,
        )}`
      } else {
        periodLabel = 'Período de teste'
      }
    } else {
      // Para trials gratuitos, mostrar a duração se disponível
      if (
        trialDurationValue > 0 &&
        trialDurationUnit &&
        unitMap[trialDurationUnit]
      ) {
        periodLabel = `${trialDurationValue} ${pluralize(
          unitMap[trialDurationUnit],
          trialDurationValue,
        )} grátis`
      } else {
        periodLabel = 'Período de teste grátis'
      }
    }

    billingLabel = 'Teste'
  } else if (pricingConfig.frequency || pricingConfig.frequencia) {
    const frequency = pricingConfig.frequency || pricingConfig.frequencia
    const freqNumber: number = Number(pricingConfig.frequency_number || pricingConfig.numeroFrequencia) || 1
    const periodUnit =
      frequency === 'year' || frequency === 'ano'
        ? 'ano'
        : frequency === 'month' || frequency === 'mês' || frequency === 'mes'
        ? 'mês'
        : frequency

    const pluralize = (unit: string, qty: number) => {
      if (qty === 1) return unit
      if (unit === 'mês') return 'meses'
      if (unit === 'ano') return 'anos'
      return unit
    }
    periodLabel =
      freqNumber === 1
        ? periodUnit
        : `${freqNumber} ${pluralize(periodUnit, freqNumber)}`

    const billingPeriodMap: Record<string, string> = {
      '1_month': 'Mensal',
      '3_month': 'Trimestral',
      '6_month': 'Semestral',
      '12_month': 'Anual',
      '1_year': 'Anual',
    }
    const billingKey = `${freqNumber}_${frequency}`
    billingLabel =
      billingPeriodMap[billingKey] ||
      periodLabel.charAt(0).toUpperCase() + periodLabel.slice(1)
  }

  if (
    durationConfig.type === 'specific' &&
    durationConfig.start_date &&
    durationConfig.end_date
  ) {
    const startDate = new Date(durationConfig.start_date).toLocaleDateString(
      'pt-BR',
      { timeZone: 'UTC' },
    )
    const endDate = new Date(durationConfig.end_date).toLocaleDateString(
      'pt-BR',
      { timeZone: 'UTC' },
    )

    // Formata o período principal
    const formattedPeriods: string[] = [`${startDate} a ${endDate}`]

    // Verifica e formata períodos adicionais, se existirem
    const additionalPeriods =
      durationConfig.periodosAdicionais || durationConfig.additional_periods

    if (Array.isArray(additionalPeriods) && additionalPeriods.length > 0) {
      additionalPeriods.forEach((period: any) => {
        if (period && period.inicio && period.fim) {
          const inicio = new Date(period.inicio).toLocaleDateString('pt-BR', {
            timeZone: 'UTC',
          })
          const fim = new Date(period.fim).toLocaleDateString('pt-BR', {
            timeZone: 'UTC',
          })
          formattedPeriods.push(`${inicio} a ${fim}`)
        }
      })
    }

    // Junta todos períodos por quebra de linha para exibição multilinha na UI
    durationLabel = formattedPeriods.join('\n')
  } else if (durationConfig.type === 'ongoing') {
    durationLabel = 'Contínuo'
  } else if (durationConfig.type === 'limited') {
    const value: number = Number(durationConfig.value || durationConfig.duracao) || 0
    const unit: string = durationConfig.unit || durationConfig.unidadeTempo || ''

    if (value > 0 && unit) {
      const unitMap: Record<string, string> = {
        days: 'dia',
        day: 'dia',
        dias: 'dia',
        weeks: 'semana',
        week: 'semana',
        semanas: 'semana',
        months: 'mês',
        month: 'mês',
        mes: 'mês',
        mês: 'mês',
        years: 'ano',
        year: 'ano',
        anos: 'ano',
      }

      const pluralize = (translatedUnit: string, qty: number) => {
        if (qty === 1) return translatedUnit
        if (translatedUnit === 'mês') return 'meses'
        if (translatedUnit === 'ano') return 'anos'
        if (translatedUnit === 'semana') return 'semanas'
        if (translatedUnit === 'dia') return 'dias'
        return translatedUnit
      }

      const translated = unitMap[unit] || unit
      durationLabel = `${value} ${pluralize(translated, value)}`
    }
  }

  // Taxa de cancelamento (suporta ambos os formatos pt/en)
  const cancellationFeeRaw =
    durationConfig.taxaCancelamento ?? durationConfig.cancellation_fee
  const cancellationFee =
    cancellationFeeRaw !== undefined && cancellationFeeRaw !== null
      ? Number(cancellationFeeRaw)
      : null

  const price =
    priceType === 'trial'
      ? Number(pricingConfig.valorDuranteTrial || pricingConfig.amount) || null // Use null para indicar "Grátis"
      : Number(pricingConfig.amount || pricingConfig.valor || pricingConfig.custo) || 0

  // --- Taxas e limites adicionais para mostrar na UI ---
  const enrollmentFeeRaw =
    pricingConfig.taxaInscricao ?? pricingConfig.enrollment_fee
  const enrollmentFee =
    enrollmentFeeRaw !== undefined && enrollmentFeeRaw !== null
      ? Number(enrollmentFeeRaw)
      : null

  const lateFeeRaw =
    pricingConfig.taxaAtraso ?? pricingConfig.late_fee
  const lateFee =
    lateFeeRaw !== undefined && lateFeeRaw !== null
      ? Number(lateFeeRaw)
      : null

  const lateDaysRaw =
    pricingConfig.diasAtraso ?? pricingConfig.late_days
  const lateDays =
    lateDaysRaw !== undefined && lateDaysRaw !== null
      ? Number(lateDaysRaw)
      : null

  const maxPaymentsRaw =
    pricingConfig.maxPagamentos ?? pricingConfig.max_payments
  const maxPayments =
    maxPaymentsRaw !== undefined && maxPaymentsRaw !== null
      ? Number(maxPaymentsRaw)
      : null

  // Extrair modalidades do access_config
  const accessConfig = plan.access_config || {}
  const modalityIds = accessConfig.modalities || []
  const modalitiesMap = plan.modalitiesMap || new Map()

  // Mapear IDs para nomes das modalidades
  const modalities = modalityIds
    .map((id: string) => modalitiesMap.get(id))
    .filter((name: string | undefined) => name) // remover undefined

  // Extrair dados de capacidade do access_config
  const capacity = accessConfig.capacity || 'unlimited'
  const maxCapacity = accessConfig.max_capacity || null

  return {
    id: String(plan.id),
    title: String(plan.title),
    status: statusMap[plan.status] || 'draft',
    price,
    period: periodLabel,
    billing_period: billingLabel,
    students: Number(plan.students_count) || 0,
    monthly_revenue: Number(plan.monthly_revenue) || 0,
    benefits: (plan.benefits as string[]) || [],
    modalities,
    theme: priceTypeThemes[priceType] || priceTypeThemes['recurring'],
    duration: durationLabel,
    cancellation_fee: cancellationFee,
    enrollment_fee: enrollmentFee,
    late_fee: lateFee,
    late_days: lateDays,
    max_payments: maxPayments,
    capacity,
    max_capacity: maxCapacity,
  }
}

export const usePlanAdapter = (dbPlans: any[]): Plan[] => {
  const adaptedPlans = useMemo(() => {
    if (!dbPlans) return []
    return dbPlans.map(adaptPlan)
  }, [dbPlans])

  return adaptedPlans
} 