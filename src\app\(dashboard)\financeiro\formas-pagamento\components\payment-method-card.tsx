'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { useState, useTransition, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { togglePaymentMethod } from '../actions/payment-method-actions';
import { getPixSettings } from '../actions/pix-config-actions';
import { Badge } from '@/components/ui/badge';
import { PaymentMethod } from '../types';
import { cn } from '@/lib/utils';
import { PixConfigModal } from './pix-config-modal';
import { Settings, Eye, EyeOff } from 'lucide-react';

interface PaymentMethodCardProps {
  method: PaymentMethod & { slug?: string };
}

export function PaymentMethodCard({ method }: PaymentMethodCardProps) {
  const { Icon, iconClassName, name, enabled, id, fee, transactions, slug } = method;

  const [localEnabled, setLocalEnabled] = useState(enabled);
  const [isPending, startTransition] = useTransition();
  const [isPixModalOpen, setIsPixModalOpen] = useState(false);
  const [pixKey, setPixKey] = useState<string | undefined>();
  const router = useRouter();

  // Carrega configurações PIX se for método PIX
  useEffect(() => {
    if (slug === 'pix' && enabled) {
      getPixSettings().then((result) => {
        if (!result.error) {
          setPixKey(result.pixKey);
        }
      });
    }
  }, [slug, enabled]);

  // Função para atualizar a chave PIX em tempo real
  const handlePixKeyUpdate = (newPixKey: string) => {
    setPixKey(newPixKey);
  };

  async function onToggle(value: boolean) {
    setLocalEnabled(value);

    startTransition(async () => {
      const result = await togglePaymentMethod({ paymentMethodId: id, enabled: value });

      if (!result.success) {
        // Reverte estado em caso de erro
        setLocalEnabled(!value);
        console.error('[PaymentMethodCard] Falha ao atualizar forma de pagamento:', result.error);
      } else {
        // Garante que dados sejam atualizados em toda a UI
        router.refresh();
      }
    });
  }

  // Determina a cor baseada no status e tipo de método
  const getColorConfig = () => {
    if (localEnabled) {
      // Cores específicas para cada método quando ativo
      if (slug === 'pix') {
        return {
          header: 'from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent',
          iconBg: 'bg-blue-100 dark:bg-blue-900/30',
          iconColor: 'text-blue-600 dark:text-blue-400'
        };
      } else if (slug === 'cartao-credito') {
        return {
          header: 'from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent',
          iconBg: 'bg-emerald-100 dark:bg-emerald-900/30',
          iconColor: 'text-emerald-600 dark:text-emerald-400'
        };
      } else if (slug === 'cartao-debito') {
        return {
          header: 'from-purple-50/50 to-transparent dark:from-purple-900/20 dark:to-transparent',
          iconBg: 'bg-purple-100 dark:bg-purple-900/30',
          iconColor: 'text-purple-600 dark:text-purple-400'
        };
      } else {
        return {
          header: 'from-green-50/50 to-transparent dark:from-green-900/20 dark:to-transparent',
          iconBg: 'bg-green-100 dark:bg-green-900/30',
          iconColor: 'text-green-600 dark:text-green-400'
        };
      }
    } else {
      // Cor neutra quando inativo
      return {
        header: 'from-gray-50/50 to-transparent dark:from-gray-900/20 dark:to-transparent',
        iconBg: 'bg-gray-100 dark:bg-gray-900/30',
        iconColor: 'text-gray-600 dark:text-gray-400'
      };
    }
  };

  const colorConfig = getColorConfig();

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className={`pb-4 bg-gradient-to-r ${colorConfig.header}`}>
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className={`p-1 ${colorConfig.iconBg} rounded-full`}>
              <Icon className={`w-4 h-4 ${colorConfig.iconColor}`} />
            </div>
            {name}
          </CardTitle>
          <Switch
            checked={localEnabled}
            disabled={isPending}
            onCheckedChange={onToggle}
          />
        </div>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Status</span>
            <Badge variant="secondary" className={localEnabled ? 'text-green-600' : ''}>
              {localEnabled ? 'Ativo' : 'Inativo'}
            </Badge>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Transações</span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{transactions}</span>
          </div>

          {/* Configurações específicas para PIX */}
          {slug === 'pix' && localEnabled && (
            <>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Chave PIX</span>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {pixKey ? '••••••••••••' : 'Não configurada'}
                </span>
              </div>
              <Button
                variant="outline"
                className="w-full mt-3"
                size="sm"
                onClick={() => setIsPixModalOpen(true)}
              >
                <Settings className="mr-2 h-4 w-4" />
                {pixKey ? 'Alterar Chave PIX' : 'Configurar PIX'}
              </Button>
            </>
          )}
        </div>
      </CardContent>

      {/* Modal de configuração PIX */}
      {slug === 'pix' && (
        <PixConfigModal
          isOpen={isPixModalOpen}
          onClose={() => setIsPixModalOpen(false)}
          currentPixKey={pixKey}
          onPixKeyUpdated={handlePixKeyUpdate}
        />
      )}
    </Card>
  );
}