/**
 * Componentes lazy para otimização de bundle
 */

import { lazy, Suspense, memo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { UsersIcon } from '@/components/icons/attendance-icons';

// Lazy loading dos componentes pesados
const QRCodeSection = lazy(() => 
  import('./QRCodeSection').then(module => ({ default: module.QRCodeSection }))
);

const AttendanceList = lazy(() => 
  import('../../../aulas/components/attendance').then(module => ({ default: module.AttendanceList }))
);

// Skeletons otimizados
const QRCodeSkeleton = memo(() => (
  <Card>
    <CardContent className="pt-6">
      <div className="flex flex-col items-center space-y-4">
        <div className="h-64 w-64 bg-muted rounded-lg animate-pulse" />
        <div className="h-4 bg-muted rounded animate-pulse w-48" />
        <div className="h-10 bg-muted rounded animate-pulse w-32" />
      </div>
    </CardContent>
  </Card>
));
QRCodeSkeleton.displayName = 'QRCodeSkeleton';

const AttendanceListSkeleton = memo(() => (
  <div className="space-y-4">
    {Array.from({ length: 5 }).map((_, i) => (
      <Card key={i}>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 bg-muted rounded-full animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
              <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
            </div>
            <div className="h-8 bg-muted rounded animate-pulse w-20" />
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
));
AttendanceListSkeleton.displayName = 'AttendanceListSkeleton';

const ReportSkeleton = memo(() => (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse w-24" />
              <div className="h-8 bg-muted rounded animate-pulse w-16" />
              <div className="h-3 bg-muted rounded animate-pulse w-32" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="h-6 bg-muted rounded animate-pulse w-48" />
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded animate-pulse" />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
));
ReportSkeleton.displayName = 'ReportSkeleton';

// Componentes lazy com Suspense
export const LazyQRCodeSection = memo<{
  classId: string;
  classGroupId: string | null;
  tenantId: string;
  isActive: boolean;
  status: 'ongoing' | 'scheduled' | 'completed';
  isVisible: boolean;
}>(({ classId, classGroupId, tenantId, isActive, status, isVisible }) => {
  // Só renderizar quando a tab estiver visível
  if (!isVisible) return null;

  return (
    <Suspense fallback={<QRCodeSkeleton />}>
      <QRCodeSection
        classId={classId}
        classGroupId={classGroupId}
        tenantId={tenantId}
        isActive={isActive}
        status={status}
        isVisible={isVisible}
      />
    </Suspense>
  );
});
LazyQRCodeSection.displayName = 'LazyQRCodeSection';

export const LazyAttendanceList = memo<{
  classId: string;
  attendanceRecords: any[];
  enrolledStudents: any[];
  canCheckIn: boolean;
  isVisible: boolean;
}>(({ classId, attendanceRecords, enrolledStudents, canCheckIn, isVisible }) => {
  // Só renderizar quando a tab estiver visível
  if (!isVisible) return null;

  return (
    <Suspense fallback={<AttendanceListSkeleton />}>
      <AttendanceList
        classId={classId}
        attendanceRecords={attendanceRecords}
        enrolledStudents={enrolledStudents}
        canCheckIn={canCheckIn}
      />
    </Suspense>
  );
});
LazyAttendanceList.displayName = 'LazyAttendanceList';

// Componente de estado vazio otimizado
export const EmptyAttendanceState = memo(() => (
  <Card>
    <CardContent className="pt-6 text-center">
      <UsersIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="text-lg font-semibold mb-2 text-foreground">Nenhuma presença registrada</h3>
      <p className="text-muted-foreground">
        Ainda não há check-ins para esta aula.
      </p>
    </CardContent>
  </Card>
));
EmptyAttendanceState.displayName = 'EmptyAttendanceState';

// Export dos skeletons para uso externo
export { QRCodeSkeleton, AttendanceListSkeleton, ReportSkeleton };
