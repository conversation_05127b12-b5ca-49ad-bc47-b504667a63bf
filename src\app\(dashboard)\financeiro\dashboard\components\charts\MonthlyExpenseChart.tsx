"use client";

/**
 * Componente de Gráfico de Evolução de Despesas Mensais - Fase 4
 * Exibe a evolução das despesas ao longo dos meses do ano
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { TrendingDown, Calendar, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getMonthlyExpenseChart, MonthlyExpenseData } from '../../actions/charts/expense-chart-actions';
import { formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface MonthlyExpenseChartProps {
  className?: string;
  year?: number;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100">
          {label}
        </p>
        <p className="text-sm text-red-600 dark:text-red-400">
          <span className="font-medium">Despesas: </span>
          {data.formattedExpense}
        </p>
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const MonthlyExpenseChart: React.FC<MonthlyExpenseChartProps> = ({
  className,
  year = new Date().getFullYear()
}) => {
  const [data, setData] = useState<MonthlyExpenseData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // CARREGAMENTO DE DADOS
  // ============================================================================

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getMonthlyExpenseChart(year);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        setData(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [year]);

  // ============================================================================
  // CÁLCULOS AUXILIARES
  // ============================================================================

  const totalExpense = data.reduce((sum, item) => sum + item.expense, 0);
  const averageExpense = data.length > 0 ? totalExpense / data.length : 0;
  const maxExpense = Math.max(...data.map(item => item.expense));
  const minExpense = Math.min(...data.filter(item => item.expense > 0).map(item => item.expense));

  // ============================================================================
  // ESTADOS DE LOADING E ERRO
  // ============================================================================

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-red-500" />
            Evolução de Despesas {year}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-red-500" />
            Evolução de Despesas {year}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data.length || totalExpense === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-red-500" />
            Evolução de Despesas {year}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <TrendingDown className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Nenhuma despesa encontrada em {year}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // ============================================================================
  // RENDERIZAÇÃO
  // ============================================================================

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Calendar className="h-4 w-4 text-red-500" />
          Evolução de Despesas {year}
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Tendência de gastos mensais ao longo do ano
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-80 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                className="text-xs"
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                className="text-xs"
                tickFormatter={(value) => formatCurrency(value, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="expense" 
                stroke="#ef4444" 
                strokeWidth={3}
                dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-xs text-gray-500 dark:text-gray-400">Total</div>
            <div className="text-sm font-medium text-red-600 dark:text-red-400">
              {formatCurrency(totalExpense)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 dark:text-gray-400">Média</div>
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formatCurrency(averageExpense)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 dark:text-gray-400">Maior</div>
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formatCurrency(maxExpense)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 dark:text-gray-400">Menor</div>
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {minExpense > 0 ? formatCurrency(minExpense) : 'R$ 0,00'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
