---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
# Instruções do Object Calisthenics

Object Calisthenics é um conjunto de regras que visa melhorar a qualidade do código orientado a objetos, promovendo legibilidade, manutenibilidade e design robusto. O objetivo é incentivar a criação de objetos coesos e com responsabilidades bem definidas.

## 1. Apenas Um Nível de Indentação por Método
- **Motivação:** Simplifica a lógica e facilita o entendimento.
- **Como aplicar:**
  - Divida métodos longos em métodos menores.
  - Utilize early returns para evitar blocos profundos.

## 2. Não Use a Palavra-Chave "else"
- **Motivação:** Reduz a complexidade condicional.
- **Como aplicar:**
  - Prefira guard clauses (early return) para tratar condições especiais.

## 3. Envolva Todos os Primitivos e Strings em Classes
- **Motivação:** Adiciona semântica e encapsula regras de negócio.
- **Como aplicar:**
  - Crie value objects para representar conceitos do domínio.

## 4. Use Apenas Um Ponto por Linha
- **Motivação:** Impede o encadeamento excessivo de chamadas.
- **Como aplicar:**
  - Cada método deve ser chamado em seu próprio contexto.

## 5. Não Abrevie
- **Motivação:** Melhora a legibilidade e entendimento do código.
- **Como aplicar:**
  - Use nomes completos e significativos para variáveis, métodos e classes.

## 6. Mantenha Entidades Pequenas
- **Motivação:** Facilita a compreensão e o gerenciamento do código.
- **Como aplicar:**
  - Limite o tamanho de classes e métodos.
  - Extraia funcionalidades para novas classes quando necessário.

## 7. Não Use Muitos Objetos em uma Coleção
- **Motivação:** Evita complexidade ao manipular coleções.
- **Como aplicar:**
  - Prefira agrupar objetos de forma lógica.
  - Utilize coleções especializadas quando possível.

## 8. Não Use Getters, Setters ou Propriedades
- **Motivação:** Encoraja o encapsulamento e reduz o acoplamento.
- **Como aplicar:**
  - Prefira métodos que expressem intenção ao invés de expor dados.

## 9. Coleções devem ser Primeira Classe
- **Motivação:** Melhora a semântica e organização do código.
- **Como aplicar:**
  - Crie classes específicas para manipular coleções.

## 10. Não Use Classes com Mais de Duas Variáveis de Instância
- **Motivação:** Garante que as classes tenham responsabilidades pequenas e coesas.
- **Como aplicar:**
  - Extraia objetos quando houver mais de duas variáveis.

## 11. Não Use Classes Base
- **Motivação:** Evita herança excessiva e promove a composição.
- **Como aplicar:**
  - Prefira composição sobre herança.

## 12. Não Use Tipos de Dados Nativos
- **Motivação:** Promove a criação de tipos semânticos específicos.
- **Como aplicar:**
  - Crie value objects para representar dados primitivos.

---

### Como Usar Estas Regras
- Ao seguir essas instruções, deve:
  - Priorizar clareza e simplicidade no código.
  - Garantir que cada classe e método tenha uma responsabilidade única.
  - Refatorar consistentemente para manter o código limpo e modular.
