import { createTenantServerClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'

// Interface para o retorno da função RPC get_payment_by_id
interface PaymentRPCResult {
  success: boolean
  error?: string
  data?: {
    id: string
    tenant_id: string
    student_id: string
    membership_id: string
    amount: number
    currency: string
    status: string
    payment_type: string
    description: string
    due_date: string
    paid_at: string | null
    created_at: string
    updated_at: string | null
    metadata: any
    reference_id: string | null
    billing_cycle: string
    attempt_count: number
    last_attempt_at: string | null
    student: {
      id: string
      user_id: string
      name: string
      email: string
    }
    membership: {
      id: string
      student_id: string
      plan_id: string
      plan_title: string
    } | null
  }
}

/**
 * Validações de segurança para o sistema de checkout
 */
export class CheckoutSecurityValidator {
  
  /**
   * Valida se o usuário pode acessar um pagamento específico
   */
  static async validatePaymentAccess(paymentId: string): Promise<{
    success: boolean
    error?: string
    paymentData?: any
  }> {
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser) {
        return {
          success: false,
          error: 'Usuário não autenticado'
        }
      }

      const supabase = await createTenantServerClient()

      // Usar função RPC que já inclui validação de acesso
      const { data: rpcResult, error: rpcError } = await supabase
        .rpc('get_payment_by_id' as any, { p_payment_id: paymentId }) as { data: PaymentRPCResult | null, error: any }

      if (rpcError) {
        console.error('Erro na função RPC get_payment_by_id:', rpcError)
        return {
          success: false,
          error: 'Erro interno de validação'
        }
      }

      if (!rpcResult || !rpcResult.success) {
        return {
          success: false,
          error: rpcResult?.error || 'Pagamento não encontrado'
        }
      }

      return {
        success: true,
        paymentData: rpcResult.data!
      }

    } catch (error) {
      console.error('Erro na validação de acesso ao pagamento:', error)
      return {
        success: false,
        error: 'Erro interno de validação'
      }
    }
  }

  /**
   * Valida se um pagamento pode ser processado
   */
  static validatePaymentStatus(status: string): {
    canProcess: boolean
    error?: string
  } {
    switch (status) {
      case 'pending':
        return { canProcess: true }
      
      case 'paid':
        return {
          canProcess: false,
          error: 'Este pagamento já foi realizado'
        }
      
      case 'cancelled':
        return {
          canProcess: false,
          error: 'Este pagamento foi cancelado'
        }
      
      case 'awaiting_confirmation':
        return {
          canProcess: false,
          error: 'Este pagamento já está aguardando confirmação'
        }
      
      case 'overdue':
        return {
          canProcess: false,
          error: 'Este pagamento está vencido e não pode ser processado'
        }
      
      default:
        return {
          canProcess: false,
          error: 'Status de pagamento inválido'
        }
    }
  }

  /**
   * Valida se o valor do pagamento está dentro dos limites aceitáveis
   */
  static validatePaymentAmount(amount: number): {
    isValid: boolean
    error?: string
  } {
    const MIN_AMOUNT = 0.01 // R$ 0,01
    const MAX_AMOUNT = 50000 // R$ 50.000,00

    if (amount < MIN_AMOUNT) {
      return {
        isValid: false,
        error: 'Valor do pagamento muito baixo'
      }
    }

    if (amount > MAX_AMOUNT) {
      return {
        isValid: false,
        error: 'Valor do pagamento excede o limite máximo'
      }
    }

    return { isValid: true }
  }

  /**
   * Valida se a data de vencimento é válida
   */
  static validateDueDate(dueDateString: string | null): {
    isValid: boolean
    error?: string
    dueDate?: Date
  } {
    if (!dueDateString) {
      return { isValid: true } // Vencimento opcional
    }

    try {
      const dueDate = new Date(dueDateString)
      const now = new Date()
      const maxFutureDate = new Date()
      maxFutureDate.setFullYear(now.getFullYear() + 2) // Máximo 2 anos no futuro

      if (isNaN(dueDate.getTime())) {
        return {
          isValid: false,
          error: 'Data de vencimento inválida'
        }
      }

      if (dueDate > maxFutureDate) {
        return {
          isValid: false,
          error: 'Data de vencimento muito distante no futuro'
        }
      }

      return {
        isValid: true,
        dueDate
      }

    } catch (error) {
      return {
        isValid: false,
        error: 'Formato de data inválido'
      }
    }
  }

  /**
   * Validação completa de um pagamento para checkout
   */
  static async validatePaymentForCheckout(paymentId: string): Promise<{
    success: boolean
    error?: string
    paymentData?: any
  }> {
    // Validar acesso
    const accessValidation = await this.validatePaymentAccess(paymentId)
    if (!accessValidation.success) {
      return accessValidation
    }

    const paymentData = accessValidation.paymentData!

    // Validar status
    const statusValidation = this.validatePaymentStatus(paymentData.status)
    if (!statusValidation.canProcess) {
      return {
        success: false,
        error: statusValidation.error
      }
    }

    // Validar valor
    const amountValidation = this.validatePaymentAmount(paymentData.amount)
    if (!amountValidation.isValid) {
      return {
        success: false,
        error: amountValidation.error
      }
    }

    // Validar data de vencimento
    const dueDateValidation = this.validateDueDate(paymentData.due_date)
    if (!dueDateValidation.isValid) {
      return {
        success: false,
        error: dueDateValidation.error
      }
    }

    return {
      success: true,
      paymentData
    }
  }
}
