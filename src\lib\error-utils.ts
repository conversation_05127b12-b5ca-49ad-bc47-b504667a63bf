/**
 * Utilitários para formatação e tratamento de erros
 */

/**
 * Converte erros em strings legíveis para o usuário
 */
export function formatErrorMessage(errors: any): string {
  if (typeof errors === 'string') {
    return errors;
  }
  
  if (errors?._form) {
    return errors._form;
  }
  
  // Verificar se é erro de autenticação
  if (errors?.message?.includes('não autenticado') || errors?.message?.includes('Usuário não autenticado')) {
    return 'Sessão expirada. Faça login novamente para continuar.';
  }
  
  // Verificar se é erro de permissão
  if (errors?.message?.includes('não autorizado') || errors?.message?.includes('sem permissão')) {
    return 'Você não tem permissão para acessar estes dados.';
  }
  
  // Verificar se é erro de conexão/rede
  if (errors?.message?.includes('fetch') || errors?.message?.includes('network') || errors?.message?.includes('conexão')) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }
  
  // Verificar se é erro de dados não encontrados
  if (errors?.message?.includes('não encontrado') || errors?.message?.includes('not found')) {
    return 'Os dados solicitados não foram encontrados.';
  }
  
  // Se for um objeto de erros do Zod, pegar a primeira mensagem de erro
  if (typeof errors === 'object' && errors !== null) {
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey && errors[firstErrorKey]) {
      const errorValue = errors[firstErrorKey];
      if (typeof errorValue === 'string') {
        return errorValue;
      }
      if (errorValue?._errors && Array.isArray(errorValue._errors) && errorValue._errors.length > 0) {
        return errorValue._errors[0];
      }
      if (errorValue?.message) {
        return errorValue.message;
      }
    }
  }
  
  // Erro genérico mais amigável
  return 'Ocorreu um erro inesperado. Tente novamente ou entre em contato com o suporte.';
}

/**
 * Tipos de erro comuns
 */
export const ErrorTypes = {
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  NETWORK: 'network',
  NOT_FOUND: 'not_found',
  VALIDATION: 'validation',
  GENERIC: 'generic'
} as const;

/**
 * Identifica o tipo de erro
 */
export function getErrorType(error: any): keyof typeof ErrorTypes {
  if (error?.message?.includes('não autenticado') || error?.message?.includes('Usuário não autenticado')) {
    return 'AUTHENTICATION';
  }
  
  if (error?.message?.includes('não autorizado') || error?.message?.includes('sem permissão')) {
    return 'AUTHORIZATION';
  }
  
  if (error?.message?.includes('fetch') || error?.message?.includes('network') || error?.message?.includes('conexão')) {
    return 'NETWORK';
  }
  
  if (error?.message?.includes('não encontrado') || error?.message?.includes('not found')) {
    return 'NOT_FOUND';
  }
  
  if (error?._errors || (typeof error === 'object' && error !== null && !error.message)) {
    return 'VALIDATION';
  }
  
  return 'GENERIC';
}
