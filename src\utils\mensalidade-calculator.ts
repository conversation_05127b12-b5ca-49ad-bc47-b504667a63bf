import { format, parseISO, addMonths, subMonths, isBefore, isAfter, isSameDay } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export interface MensalidadeParams {
  data_pagamento: string | Date
  tipo_vencimento: 'fixo' | 'matricula'
  dia_vencimento_fixo?: number
  data_matricula?: string | Date
  eh_primeiro_pagamento?: boolean
}

export interface MensalidadeResult {
  mes_ano: string
  ciclo_inicio: Date
  ciclo_fim: Date
  eh_antecipado: boolean
  eh_atrasado: boolean
}

/**
 * Determina a qual mês corresponde uma mensalidade paga, considerando diferentes 
 * formas de configurar o vencimento da mensalidade no sistema da academia.
 */
export function determinarMensalidadePaga(params: MensalidadeParams): MensalidadeResult {
  const {
    data_pagamento,
    tipo_vencimento,
    dia_vencimento_fixo,
    data_matricula,
    eh_primeiro_pagamento = false
  } = params

  // Converter datas para objetos Date
  const dataPagamento = typeof data_pagamento === 'string' 
    ? parseISO(data_pagamento) 
    : data_pagamento

  let dataMatricula: Date | undefined
  if (data_matricula) {
    dataMatricula = typeof data_matricula === 'string' 
      ? parseISO(data_matricula) 
      : data_matricula
  }

  // Determinar o dia de vencimento
  let diaVencimento: number
  if (tipo_vencimento === 'fixo') {
    if (!dia_vencimento_fixo) {
      throw new Error('dia_vencimento_fixo é obrigatório quando tipo_vencimento é "fixo"')
    }
    diaVencimento = dia_vencimento_fixo
  } else {
    if (!dataMatricula) {
      throw new Error('data_matricula é obrigatória quando tipo_vencimento é "matricula"')
    }
    diaVencimento = dataMatricula.getDate()
  }

  // Se é o primeiro pagamento, a mensalidade corresponde ao ciclo vigente no momento do pagamento
  if (eh_primeiro_pagamento) {
    const cicloAtual = calcularCicloVigente(dataPagamento, diaVencimento)
    return {
      mes_ano: format(cicloAtual.inicio, 'MMMM/yyyy', { locale: ptBR }),
      ciclo_inicio: cicloAtual.inicio,
      ciclo_fim: cicloAtual.fim,
      eh_antecipado: false,
      eh_atrasado: false
    }
  }

  // Para pagamentos subsequentes, determinar qual ciclo o pagamento está cobrindo
  const cicloCorrespondente = determinarCicloCorrespondente(dataPagamento, diaVencimento)
  
  // Verificar se é antecipado ou atrasado
  const cicloAtual = calcularCicloVigente(dataPagamento, diaVencimento)
  const ehAntecipado = isBefore(cicloCorrespondente.inicio, cicloAtual.inicio)
  const ehAtrasado = isAfter(cicloCorrespondente.inicio, cicloAtual.inicio)

  return {
    mes_ano: format(cicloCorrespondente.inicio, 'MMMM/yyyy', { locale: ptBR }),
    ciclo_inicio: cicloCorrespondente.inicio,
    ciclo_fim: cicloCorrespondente.fim,
    eh_antecipado: ehAntecipado,
    eh_atrasado: ehAtrasado
  }
}

/**
 * Calcula o ciclo vigente (atual) para uma data específica
 */
function calcularCicloVigente(data: Date, diaVencimento: number): { inicio: Date; fim: Date } {
  const ano = data.getFullYear()
  const mes = data.getMonth()
  
  // Criar data de vencimento do mês atual
  const vencimentoMesAtual = new Date(ano, mes, diaVencimento)
  
  if (isBefore(data, vencimentoMesAtual) || isSameDay(data, vencimentoMesAtual)) {
    // Estamos antes ou no dia do vencimento do mês atual
    // O ciclo vigente é do mês anterior até este vencimento
    const inicioMesAnterior = new Date(ano, mes - 1, diaVencimento)
    const fimMesAtual = new Date(vencimentoMesAtual.getTime() - 24 * 60 * 60 * 1000) // véspera
    
    return {
      inicio: inicioMesAnterior,
      fim: fimMesAtual
    }
  } else {
    // Estamos após o vencimento do mês atual
    // O ciclo vigente é deste vencimento até o próximo
    const proximoVencimento = new Date(ano, mes + 1, diaVencimento)
    const fimProximoMes = new Date(proximoVencimento.getTime() - 24 * 60 * 60 * 1000) // véspera
    
    return {
      inicio: vencimentoMesAtual,
      fim: fimProximoMes
    }
  }
}

/**
 * Determina qual ciclo de mensalidade um pagamento está cobrindo
 * baseado na data de pagamento e no padrão de vencimentos
 */
function determinarCicloCorrespondente(dataPagamento: Date, diaVencimento: number): { inicio: Date; fim: Date } {
  const ano = dataPagamento.getFullYear()
  const mes = dataPagamento.getMonth()
  
  // Criar data de vencimento do mês do pagamento
  const vencimentoMesPagamento = new Date(ano, mes, diaVencimento)
  
  if (isBefore(dataPagamento, vencimentoMesPagamento) || isSameDay(dataPagamento, vencimentoMesPagamento)) {
    // Pagamento feito antes ou no dia do vencimento
    // Assume que está pagando o ciclo que vence neste mês
    const inicioMesAnterior = new Date(ano, mes - 1, diaVencimento)
    const fimMesAtual = new Date(vencimentoMesPagamento.getTime() - 24 * 60 * 60 * 1000)
    
    return {
      inicio: inicioMesAnterior,
      fim: fimMesAtual
    }
  } else {
    // Pagamento feito após o vencimento
    // Pode estar pagando o próximo ciclo (antecipado) ou o atual (atrasado)
    // Por padrão, assume que está pagando o próximo ciclo
    const proximoVencimento = new Date(ano, mes + 1, diaVencimento)
    const fimProximoMes = new Date(proximoVencimento.getTime() - 24 * 60 * 60 * 1000)
    
    return {
      inicio: vencimentoMesPagamento,
      fim: fimProximoMes
    }
  }
}

/**
 * Função auxiliar para determinar o mês da mensalidade baseado no due_date
 * Esta é uma versão simplificada para uso direto com dados do banco
 */
export function determinarMesMensalidadePorDueDate(
  dueDate: string | Date,
  diaVencimento: number
): string {
  const due = typeof dueDate === 'string' ? parseISO(dueDate) : dueDate
  
  // O due_date representa o final do ciclo de cobrança
  // O mês da mensalidade é o mês anterior ao vencimento
  const inicioMensalidade = subMonths(due, 1)
  
  return format(inicioMensalidade, 'MMMM', { locale: ptBR })
}

/**
 * Função para determinar o mês correto da mensalidade baseado no contexto atual
 * Considera se o usuário está em dia, pendente ou atrasado
 */
export function determinarMesAtualMensalidade(
  proximoVencimento: Date | null,
  statusMensalidade: 'em_dia' | 'pendente' | 'atrasado',
  ultimoPagamentoPago?: { dueDate: Date; paidAt: Date }
): string {
  // Se não há próximo vencimento, usar mês atual
  if (!proximoVencimento) {
    return format(new Date(), 'MMMM', { locale: ptBR })
  }

  // Para usuários em dia: mostrar o mês do próximo vencimento
  // Para usuários pendentes/atrasados: mostrar o mês do vencimento em atraso
  return format(proximoVencimento, 'MMMM', { locale: ptBR })
}
