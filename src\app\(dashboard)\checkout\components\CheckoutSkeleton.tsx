import { Card } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

export function CheckoutSkeleton() {
  return (
    <div className="space-y-6">
      {/* Layout Horizontal - Desktop e Tablet */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Coluna Esquerda - Payment Info Skeleton */}
        <div className="space-y-6">
          <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3" />
                </div>
                <div className="w-16 h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              </div>

              <Separator />

              {/* Valor em destaque */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20" />
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24" />
                </div>
              </div>

              {/* Detalhes compactos */}
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24" />
                </div>
                <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16" />
                  <div className="space-y-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-32" />
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-40" />
                  </div>
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-14" />
                  <div className="space-y-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-28" />
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20" />
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Alert Skeleton */}
          <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex gap-2">
              <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-shrink-0 mt-0.5" />
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-full" />
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-5/6" />
              </div>
            </div>
          </div>

          {/* Confirmation Button Skeleton - Mobile */}
          <div className="lg:hidden">
            <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                  <div className="flex-1">
                    <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3" />
                  </div>
                </div>
                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              </div>
            </Card>
          </div>
        </div>

        {/* Coluna Direita - QR Code Skeleton */}
        <div className="space-y-6">
          <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2 mb-2">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-40" />
              </div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto w-48" />

              <Separator />

              <div className="flex justify-center">
                <div className="w-48 h-48 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse border border-gray-200 dark:border-gray-600" />
              </div>

              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg py-2 px-4 border border-gray-200 dark:border-gray-700">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto w-32" />
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-32" />
                <div className="flex gap-2">
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-1" />
                  <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3 border border-gray-200 dark:border-gray-700">
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-12" />
                  <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20" />
                </div>
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-32" />
                </div>
              </div>
            </div>
          </Card>

          {/* Confirmation Button Skeleton - Desktop */}
          <div className="hidden lg:block">
            <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                  <div className="flex-1">
                    <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-2/3" />
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex gap-3">
                    <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-shrink-0 mt-0.5" />
                    <div className="space-y-2 flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24" />
                      <div className="space-y-1">
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-full" />
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-5/6" />
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-4/5" />
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />

                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg py-2 px-3 border border-gray-200 dark:border-gray-700">
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto w-3/4 mb-1" />
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto w-2/3" />
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
