import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { createTenantServerClient } from '@/services/supabase/server'

/**
 * Middleware para validar acesso às páginas de checkout
 */
export async function checkoutMiddleware(
  request: NextRequest,
  paymentId: string
): Promise<NextResponse | null> {
  try {
    // Verificar se o usuário está autenticado
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // Validar formato do paymentId
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(paymentId)) {
      return NextResponse.redirect(new URL('/perfil', request.url))
    }

    const supabase = await createTenantServerClient()

    // Verificar se o pagamento existe e pertence ao usuário
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .select(`
        id,
        status,
        student_id,
        memberships!inner (
          student_id
        )
      `)
      .eq('id', paymentId)
      .single()

    if (paymentError || !paymentData) {
      return NextResponse.redirect(new URL('/perfil', request.url))
    }

    // Verificar se o usuário é o dono do pagamento
    if (paymentData.student_id !== currentUser.id && 
        paymentData.memberships.student_id !== currentUser.id) {
      return NextResponse.redirect(new URL('/perfil', request.url))
    }

    // Verificar se o pagamento pode ser processado
    if (paymentData.status === 'paid') {
      return NextResponse.redirect(new URL('/perfil?message=payment_already_paid', request.url))
    }

    if (paymentData.status === 'cancelled') {
      return NextResponse.redirect(new URL('/perfil?message=payment_cancelled', request.url))
    }

    // Se chegou até aqui, o acesso é válido
    return null

  } catch (error) {
    console.error('Erro no middleware de checkout:', error)
    return NextResponse.redirect(new URL('/perfil', request.url))
  }
}

/**
 * Validações de rate limiting para ações de checkout
 */
export class CheckoutRateLimit {
  private static attempts = new Map<string, { count: number; lastAttempt: number }>()
  private static readonly MAX_ATTEMPTS = 5
  private static readonly WINDOW_MS = 15 * 60 * 1000 // 15 minutos

  static checkRateLimit(userId: string, action: 'confirm_payment' | 'generate_qr'): boolean {
    const key = `${userId}:${action}`
    const now = Date.now()
    const userAttempts = this.attempts.get(key)

    if (!userAttempts) {
      this.attempts.set(key, { count: 1, lastAttempt: now })
      return true
    }

    // Reset se passou da janela de tempo
    if (now - userAttempts.lastAttempt > this.WINDOW_MS) {
      this.attempts.set(key, { count: 1, lastAttempt: now })
      return true
    }

    // Verificar se excedeu o limite
    if (userAttempts.count >= this.MAX_ATTEMPTS) {
      return false
    }

    // Incrementar contador
    userAttempts.count++
    userAttempts.lastAttempt = now
    return true
  }

  static getRemainingAttempts(userId: string, action: 'confirm_payment' | 'generate_qr'): number {
    const key = `${userId}:${action}`
    const userAttempts = this.attempts.get(key)
    
    if (!userAttempts) return this.MAX_ATTEMPTS
    
    const now = Date.now()
    if (now - userAttempts.lastAttempt > this.WINDOW_MS) {
      return this.MAX_ATTEMPTS
    }
    
    return Math.max(0, this.MAX_ATTEMPTS - userAttempts.count)
  }

  static getTimeUntilReset(userId: string, action: 'confirm_payment' | 'generate_qr'): number {
    const key = `${userId}:${action}`
    const userAttempts = this.attempts.get(key)
    
    if (!userAttempts) return 0
    
    const timeElapsed = Date.now() - userAttempts.lastAttempt
    return Math.max(0, this.WINDOW_MS - timeElapsed)
  }
}
