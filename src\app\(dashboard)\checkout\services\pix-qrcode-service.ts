import QRCode from 'qrcode'

export interface PixPaymentData {
  pixKey: string
  amount: number
  description: string
  merchantName?: string
  merchantCity?: string
  txId?: string
}

export interface PixQRCodeResult {
  qrCode: string
  pixKey: string
  amount: number
  description: string
  brCode: string
}

/**
 * Gera um QR Code PIX seguindo o padrão EMV QR Code
 * Baseado na especificação do Banco Central do Brasil
 */
export class PixQRCodeService {
  /**
   * Gera QR Code PIX completo
   */
  static async generatePixQRCode(data: PixPaymentData): Promise<PixQRCodeResult> {
    try {
      const brCode = this.generateBRCode(data)
      const qrCodeDataURL = await QRCode.toDataURL(brCode, {
        errorCorrectionLevel: 'M',
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 256
      })

      return {
        qrCode: qrCodeDataURL,
        pixKey: data.pixKey,
        amount: data.amount,
        description: data.description,
        brCode
      }
    } catch (error) {
      console.error('Erro ao gerar QR Code PIX:', error)
      throw new Error('Falha ao gerar QR Code PIX')
    }
  }

  /**
   * Gera o BR Code (string do PIX) seguindo padrão EMV
   */
  private static generateBRCode(data: PixPaymentData): string {
    const {
      pixKey,
      amount,
      description,
      merchantName = 'Academia',
      merchantCity = 'Brasil',
      txId = this.generateTxId()
    } = data

    // Payload Format Indicator
    let brCode = this.formatTLV('00', '01')

    // Point of Initiation Method (12 = static, 11 = dynamic)
    brCode += this.formatTLV('01', '12')

    // Merchant Account Information (PIX)
    const pixData = this.formatTLV('00', 'BR.GOV.BCB.PIX') + this.formatTLV('01', pixKey)
    brCode += this.formatTLV('26', pixData)

    // Merchant Category Code
    brCode += this.formatTLV('52', '0000')

    // Transaction Currency (986 = BRL)
    brCode += this.formatTLV('53', '986')

    // Transaction Amount
    if (amount > 0) {
      brCode += this.formatTLV('54', amount.toFixed(2))
    }

    // Country Code
    brCode += this.formatTLV('58', 'BR')

    // Merchant Name
    brCode += this.formatTLV('59', merchantName.substring(0, 25))

    // Merchant City
    brCode += this.formatTLV('60', merchantCity.substring(0, 15))

    // Additional Data Field Template
    if (description || txId) {
      let additionalData = ''
      if (txId) {
        additionalData += this.formatTLV('05', txId.substring(0, 25))
      }
      if (description) {
        additionalData += this.formatTLV('02', description.substring(0, 72))
      }
      brCode += this.formatTLV('62', additionalData)
    }

    // CRC16 (será calculado e adicionado)
    brCode += '6304'

    // Calcular e adicionar CRC16
    const crc = this.calculateCRC16(brCode)
    brCode += crc

    return brCode
  }

  /**
   * Formata um campo no padrão TLV (Tag-Length-Value)
   */
  private static formatTLV(tag: string, value: string): string {
    const length = value.length.toString().padStart(2, '0')
    return tag + length + value
  }

  /**
   * Gera um ID de transação único
   */
  private static generateTxId(): string {
    return Math.random().toString(36).substring(2, 15).toUpperCase()
  }

  /**
   * Calcula CRC16 para validação do BR Code
   */
  private static calculateCRC16(data: string): string {
    const polynomial = 0x1021
    let crc = 0xFFFF

    for (let i = 0; i < data.length; i++) {
      crc ^= (data.charCodeAt(i) << 8)

      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ polynomial
        } else {
          crc = crc << 1
        }
        crc &= 0xFFFF
      }
    }

    return crc.toString(16).toUpperCase().padStart(4, '0')
  }

  /**
   * Valida se uma chave PIX tem formato válido
   */
  static validatePixKey(pixKey: string): boolean {
    if (!pixKey || typeof pixKey !== 'string') {
      return false
    }

    const key = pixKey.trim()

    // CPF (11 dígitos)
    if (/^\d{11}$/.test(key)) {
      return this.validateCPF(key)
    }

    // CNPJ (14 dígitos)
    if (/^\d{14}$/.test(key)) {
      return this.validateCNPJ(key)
    }

    // Email
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(key)) {
      return true
    }

    // Telefone (+5511999999999)
    if (/^\+55\d{10,11}$/.test(key)) {
      return true
    }

    // Chave aleatória (UUID format)
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(key)) {
      return true
    }

    return false
  }

  /**
   * Valida CPF
   */
  private static validateCPF(cpf: string): boolean {
    if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) {
      return false
    }

    let sum = 0
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i)
    }
    let remainder = (sum * 10) % 11
    if (remainder === 10 || remainder === 11) remainder = 0
    if (remainder !== parseInt(cpf.charAt(9))) return false

    sum = 0
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i)
    }
    remainder = (sum * 10) % 11
    if (remainder === 10 || remainder === 11) remainder = 0
    if (remainder !== parseInt(cpf.charAt(10))) return false

    return true
  }

  /**
   * Valida CNPJ
   */
  private static validateCNPJ(cnpj: string): boolean {
    if (cnpj.length !== 14 || /^(\d)\1{13}$/.test(cnpj)) {
      return false
    }

    let length = cnpj.length - 2
    let numbers = cnpj.substring(0, length)
    const digits = cnpj.substring(length)
    let sum = 0
    let pos = length - 7

    for (let i = length; i >= 1; i--) {
      sum += parseInt(numbers.charAt(length - i)) * pos--
      if (pos < 2) pos = 9
    }

    let result = sum % 11 < 2 ? 0 : 11 - (sum % 11)
    if (result !== parseInt(digits.charAt(0))) return false

    length = length + 1
    numbers = cnpj.substring(0, length)
    sum = 0
    pos = length - 7

    for (let i = length; i >= 1; i--) {
      sum += parseInt(numbers.charAt(length - i)) * pos--
      if (pos < 2) pos = 9
    }

    result = sum % 11 < 2 ? 0 : 11 - (sum % 11)
    if (result !== parseInt(digits.charAt(1))) return false

    return true
  }
}
