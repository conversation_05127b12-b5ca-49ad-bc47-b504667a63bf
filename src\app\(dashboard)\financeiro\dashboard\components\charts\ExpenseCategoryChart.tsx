"use client";

/**
 * Componente de Gráfico de Despesas por Categoria - Fase 4
 * Exibe a distribuição de despesas por categoria em formato de gráfico de rosca
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import { TrendingDown, Package, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getExpensesByCategoryChart, ExpenseCategoryChartData } from '../../actions/charts/expense-chart-actions';
import { getDateRangeForPeriod, formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface ExpenseCategoryChartProps {
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100">
          {data.categoryName}
        </p>
        <p className="text-sm text-red-600 dark:text-red-400">
          <span className="font-medium">Valor: </span>
          {data.formattedAmount}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Quantidade: </span>
          {data.count} despesas
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Participação: </span>
          {data.percentage.toFixed(1)}%
        </p>
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE DE LEGENDA CUSTOMIZADA
// ============================================================================

const CustomLegend: React.FC<{ data: ExpenseCategoryChartData[] }> = ({ data }) => {
  return (
    <div className="grid grid-cols-1 gap-2 mt-4">
      {data.slice(0, 5).map((item, index) => (
        <div key={item.categoryId} className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: item.categoryColor }}
            />
            <span className="text-gray-700 dark:text-gray-300 truncate">
              {item.categoryName}
            </span>
          </div>
          <div className="text-right">
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {item.formattedAmount}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {item.percentage.toFixed(1)}%
            </div>
          </div>
        </div>
      ))}
      {data.length > 5 && (
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center pt-2 border-t border-gray-200 dark:border-gray-700">
          +{data.length - 5} outras categorias
        </div>
      )}
    </div>
  );
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const ExpenseCategoryChart: React.FC<ExpenseCategoryChartProps> = ({
  className
}) => {
  const [data, setData] = useState<ExpenseCategoryChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // CARREGAMENTO DE DADOS
  // ============================================================================

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Usar período do mês atual
        const dateRange = getDateRangeForPeriod('month');
        const result = await getExpensesByCategoryChart(dateRange);

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        setData(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // ============================================================================
  // ESTADOS DE LOADING E ERRO
  // ============================================================================

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Package className="h-4 w-4 text-red-500" />
            Despesas por Categoria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Package className="h-4 w-4 text-red-500" />
            Despesas por Categoria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data.length) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Package className="h-4 w-4 text-red-500" />
            Despesas por Categoria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center">
              <TrendingDown className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Nenhuma despesa encontrada no período
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // ============================================================================
  // RENDERIZAÇÃO
  // ============================================================================

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Package className="h-4 w-4 text-red-500" />
          Despesas por Categoria
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Distribuição de gastos por categoria no mês atual
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-64 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="amount"
              >
                {data.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.categoryColor}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Legenda Customizada */}
        <CustomLegend data={data} />

        {/* Resumo */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600 dark:text-gray-400">Total de Categorias:</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">{data.length}</span>
          </div>
          <div className="flex justify-between items-center text-sm mt-1">
            <span className="text-gray-600 dark:text-gray-400">Total Gasto:</span>
            <span className="font-medium text-red-600 dark:text-red-400">
              {formatCurrency(totalAmount)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
