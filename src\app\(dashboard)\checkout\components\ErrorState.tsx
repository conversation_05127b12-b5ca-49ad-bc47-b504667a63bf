import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertCircle, RefreshCw, ArrowLeft, Home } from 'lucide-react'
import Link from 'next/link'

interface ErrorStateProps {
  title?: string
  message: string
  onRetry?: () => void
  showRetry?: boolean
  showBackToProfile?: boolean
  showBackToHome?: boolean
}

export function ErrorState({
  title = 'Erro no Checkout',
  message,
  onRetry,
  showRetry = true,
  showBackToProfile = true,
  showBackToHome = false
}: ErrorStateProps) {
  return (
    <Card className="p-8 text-center border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="flex justify-center mb-4">
        <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
          <AlertCircle className="w-8 h-8 text-gray-600 dark:text-gray-400" />
        </div>
      </div>

      <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h2>

      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
        {message}
      </p>

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {showRetry && onRetry && (
          <Button onClick={onRetry} className="gap-2 bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-100 dark:text-gray-900 dark:hover:bg-gray-200">
            <RefreshCw className="w-4 h-4" />
            Tentar Novamente
          </Button>
        )}

        {showBackToProfile && (
          <Button variant="outline" asChild className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800">
            <Link href="/perfil" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              Voltar ao Perfil
            </Link>
          </Button>
        )}

        {showBackToHome && (
          <Button variant="outline" asChild className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800">
            <Link href="/" className="gap-2">
              <Home className="w-4 h-4" />
              Início
            </Link>
          </Button>
        )}
      </div>
    </Card>
  )
}
