'use client';

import { useState } from 'react';
import { Settings } from 'lucide-react';
import { ConfigurationCard } from '../ui/ConfigurationCard';
import { ConfigurationField } from '../ui/ConfigurationField';
import { GeneralSettingsModal } from '../modals/GeneralSettingsModal';
import { type GeneralSettingsData } from '../../actions';
import { useGeneralSettings } from '../../hooks/useGeneralSettings';
import { formatCurrency, formatDueDay } from '../../utils/formatters';

export function GeneralSettingsCard() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { settings, isLoading, updateSettings } = useGeneralSettings();

  const handleEditSettings = () => {
    setIsModalOpen(true);
  };

  const handleSettingsUpdated = (updatedSettings: GeneralSettingsData) => {
    updateSettings(updatedSettings);
  };

  return (
    <>
      <ConfigurationCard
        title="Configurações Gerais"
        icon={Settings}
        iconColor="text-blue-600"
        buttonText="Editar Configurações"
        onButtonClick={handleEditSettings}
      >
        {isLoading ? (
          <>
            <div className="space-y-3">
              <div className="space-y-1">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                <div className="h-4 bg-gray-100 rounded animate-pulse w-32"></div>
              </div>
              <div className="space-y-1">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
                <div className="h-4 bg-gray-100 rounded animate-pulse w-28"></div>
              </div>
            </div>
          </>
        ) : (
          <>
            <ConfigurationField
              label="Moeda Padrão"
              value={formatCurrency(settings.currency)}
            />
            <ConfigurationField
              label="Dia de Vencimento Padrão"
              value={formatDueDay(settings.defaultDueDay)}
            />
          </>
        )}
      </ConfigurationCard>

      <GeneralSettingsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleSettingsUpdated}
      />
    </>
  );
}