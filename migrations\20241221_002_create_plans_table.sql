-- Migration: Create plans table
-- Purpose: Create the main table for storing subscription plans with versioning support
-- Affected tables: plans (new)
-- Dependencies: 001_create_enums.sql
-- Date: 2024-12-21

-- Create plans table with versioning support
create table public.plans (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid not null references public.tenants(id) on delete cascade,
  
  -- Versioning fields
  base_plan_id uuid references public.plans(id) on delete cascade,
  version integer not null default 1,
  
  -- Plan metadata
  status public.plan_status not null default 'draft',
  title text not null,
  plan_type public.plan_type not null default 'individual',
  
  -- Configuration stored as JSONB for flexibility
  pricing_config jsonb not null default '{}',
  duration_config jsonb not null default '{}',
  access_config jsonb not null default '{}',
  
  -- Additional metadata
  metadata jsonb not null default '{}',
  
  -- Audit fields
  created_at timestamptz not null default now(),
  created_by uuid references auth.users(id),
  updated_at timestamptz,
  
  -- Constraints
  constraint plans_title_not_empty check (length(trim(title)) > 0),
  constraint plans_version_positive check (version > 0),
  constraint plans_base_plan_version check (
    (base_plan_id is null and version = 1) or 
    (base_plan_id is not null and version > 1)
  )
);

-- Enable RLS
alter table public.plans enable row level security;

-- Create policies for tenant isolation
create policy "tenant_isolation_plans" on public.plans
  using (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

create policy "users_can_select_plans" on public.plans
  for select using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

create policy "admin_can_insert_plans" on public.plans
  for insert with check (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    auth.jwt() ->> 'role' in ('admin', 'owner')
  );

create policy "admin_can_update_plans" on public.plans
  for update using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    auth.jwt() ->> 'role' in ('admin', 'owner')
  );

create policy "admin_can_delete_plans" on public.plans
  for delete using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    auth.jwt() ->> 'role' in ('admin', 'owner')
  );

-- Create indexes for performance
create index idx_plans_tenant_id on public.plans(tenant_id);
create index idx_plans_tenant_status on public.plans(tenant_id, status);
create index idx_plans_base_plan_version on public.plans(base_plan_id, version);
create index idx_plans_active on public.plans(tenant_id, status) where status = 'active';

-- Index for JSONB searches (commonly used fields)
create index idx_plans_pricing_type on public.plans using gin ((pricing_config -> 'tipo'));
create index idx_plans_plan_type_status on public.plans(tenant_id, plan_type, status);

-- Create function to update updated_at timestamp
create or replace function public.update_plans_updated_at()
returns trigger
language plpgsql
security invoker
set search_path = ''
as $$
begin
  new.updated_at := now();
  return new;
end;
$$;

-- Create trigger for updated_at
create trigger update_plans_updated_at_trigger
  before update on public.plans
  for each row
  execute function public.update_plans_updated_at();

-- Add table comment
comment on table public.plans is 'Subscription plans with versioning support. Each plan can have multiple versions for historical tracking and A/B testing.';

-- Add column comments
comment on column public.plans.base_plan_id is 'References the original plan (version 1) for versioning chain. NULL for the first version.';
comment on column public.plans.version is 'Version number starting from 1. Increments for each new version of the same base plan.';
comment on column public.plans.pricing_config is 'JSON configuration for pricing including type (recurring/one-time/per-session/trial), amounts, frequencies, and fees.';
comment on column public.plans.duration_config is 'JSON configuration for plan duration including type (ongoing/limited/specific), dates, and renewal options.';
comment on column public.plans.access_config is 'JSON configuration for academy access including modalities, frequency limits, and capacity restrictions.'; 