'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'
import {
  criarPlanoSchema,
  type CriarPlanoData as PlanFormData,
} from '@/schemas/plan-schemas'
import { toast } from 'sonner'
import {
  createPlan,
  updatePlan,
} from '@/app/(dashboard)/academia/actions/plan-actions'
import { useQuery } from '@tanstack/react-query'
import { CACHE_KEYS } from '@/constants/cache-keys'
import { useTenantSlug } from '@/hooks/tenant/use-tenant'
import { listModalitiesAction } from '@/app/(dashboard)/academia/actions/list-modalities'

interface AvailableModality {
  id: string
  nome: string
}

interface PlanFormContextType {
  formData: PlanFormData
  planId?: string | null
  updateSection: (
    section: keyof Omit<PlanFormData, 'id' | 'metadata'>,
    data: any,
  ) => void
  submitForm: () => void
  isSubmitting: boolean
  submittedData: PlanFormData | null
  resetForm: () => void
  submissionErrors: any | null
  // Modalidades pré-carregadas
  modalidadesDisponiveis: AvailableModality[]
  modalidadesLoading: boolean
}

const PlanFormContext = createContext<PlanFormContextType | undefined>(undefined)

export function usePlanForm() {
  const context = useContext(PlanFormContext)
  if (!context) {
    throw new Error('usePlanForm deve ser usado dentro de um PlanFormProvider')
  }
  return context
}

interface PlanFormProviderProps {
  children: ReactNode
  initialData?: Partial<PlanFormData>
}

const defaultInitialData: PlanFormData = {
  details: {
    titulo: '',
    tipo: 'individual',
  },
  pricing: {
    tipo: 'recurring',
    valor: 0,
    moeda: 'BRL',
    frequencia: 'month',
    numeroFrequencia: 1,
  },
  duration: {
    tipo: 'ongoing',
  },
  academyAccess: {
    frequencia: 'unlimited',
    capacidade: 'unlimited',
    modalidades: [],
  },
  benefits: [],
  metadata: {},
}

export function PlanFormProvider({
  children,
  initialData,
}: PlanFormProviderProps) {
  const tenantSlug = useTenantSlug()

  const [formData, setFormData] = useState<PlanFormData>(() => {
    if (!initialData) return defaultInitialData

    // Combina dados iniciais com o padrão para garantir um objeto completo
    return {
      ...defaultInitialData,
      ...initialData,
      details: { ...defaultInitialData.details, ...initialData.details },
      pricing: { ...defaultInitialData.pricing, ...initialData.pricing },
      duration: initialData.duration || defaultInitialData.duration,
      academyAccess: {
        ...defaultInitialData.academyAccess,
        ...initialData.academyAccess,
      },
    }
  })
  const [planId, setPlanId] = useState<string | null>(
    (initialData as any)?.id || null,
  )

  // Pré-carregar modalidades para evitar delay quando o usuário abrir a seção
  const {
    data: modalidadesDisponiveis = [],
    isLoading: modalidadesLoading,
  } = useQuery<AvailableModality[]>({
    queryKey: CACHE_KEYS.MODALITIES_LIST(tenantSlug),
    enabled: !!tenantSlug, // carrega assim que o tenantSlug estiver disponível
    staleTime: 1000 * 60 * 10, // 10 minutos
    queryFn: async () => {
      const result = await listModalitiesAction()
      if (result.success && result.modalities) {
        return result.modalities.map((m) => ({ id: m.id, nome: m.name }))
      }
      return []
    }
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submittedData, setSubmittedData] = useState<PlanFormData | null>(null)
  const [submissionErrors, setSubmissionErrors] = useState<any | null>(null)

  const updateSection = (
    section: keyof Omit<PlanFormData, 'id' | 'metadata'>,
    data: any,
  ) => {
    if (submissionErrors) {
      setSubmissionErrors(null)
    }

    setFormData((prev) => {
      const newData = {
        ...prev,
        [section]: data,
      }

      // Note: No need to clean up perSession/trial properties anymore
      // as components now read directly from pricing config

      if (section === 'duration' && data?.tipo) {
        const currentDurationType = prev.duration?.tipo
        const newDurationType = data.tipo

        // Só limpa os campos se o tipo mudou E não é o carregamento inicial
        if (currentDurationType && currentDurationType !== newDurationType) {
          const baseDuration = { tipo: newDurationType }
          newData.duration = baseDuration
        }
      }

      return newData
    })
  }

  const getFilteredFormData = (data: PlanFormData): any => {
    const safeParseFloat = (val: any) =>
      val ? parseFloat(String(val).replace(',', '.')) : undefined
    const safeParseInt = (val: any) =>
      val ? parseInt(String(val), 10) : undefined

    const filtered: any = {
      details: data.details,
      academyAccess: data.academyAccess,
      duration: data.duration,
      benefits: data.benefits || [],
      metadata: {},
    }

    if (data.pricing?.tipo) {
      const pricingData = data.pricing as any
      switch (data.pricing.tipo) {
        case 'recurring':
          console.log('getFilteredFormData - pricingData:', pricingData);
          console.log('getFilteredFormData - maxPagamentos raw:', pricingData.maxPagamentos);
          console.log('getFilteredFormData - maxPagamentos parsed:', safeParseInt(pricingData.maxPagamentos));

          filtered.pricing = {
            tipo: 'recurring',
            valor: safeParseFloat(pricingData.valor),
            moeda: 'BRL',
            frequencia: pricingData.frequencia,
            numeroFrequencia: safeParseInt(pricingData.numeroFrequencia),
            maxPagamentos: safeParseInt(pricingData.maxPagamentos),
            taxaInscricao: safeParseFloat(pricingData.taxaInscricao),
            taxaAtraso: safeParseFloat(pricingData.taxaAtraso),
            diasAtraso: safeParseInt(pricingData.diasAtraso),
          }
          break
        case 'one-time':
           filtered.pricing = {
            tipo: 'one-time',
            custo: safeParseFloat(pricingData.custo),
            moeda: 'BRL',
            renovacaoAutomatica: pricingData.renovacaoAutomatica,
            taxaInscricao: safeParseFloat(pricingData.taxaInscricao),
            taxaAtraso: safeParseFloat(pricingData.taxaAtraso),
            diasAtraso: safeParseInt(pricingData.diasAtraso),
          }
          break
        case 'per-session':
          const psData =
            data.pricing?.tipo === 'per-session'
              ? data.pricing
              : (data as any).perSession
          filtered.pricing = {
            tipo: 'per-session',
            custo: safeParseFloat(psData?.custo),
            moeda: 'BRL',
            taxaInscricao: safeParseFloat(psData?.taxaInscricao),
            taxaAtraso: safeParseFloat(psData?.taxaAtraso),
            diasAtraso: safeParseInt(psData?.diasAtraso),
          }
          break
        case 'trial':
          const trialData =
            data.pricing?.tipo === 'trial' ? data.pricing : (data as any).trial
          filtered.pricing = {
            tipo: 'trial',
            duracao: {
              valor: safeParseInt(trialData?.duracao?.valor || trialData?.duracao),
              unidade: trialData?.duracao?.unidade || trialData?.unidadeTempo,
            },
            valorDuranteTrial: safeParseFloat(trialData?.valorDuranteTrial || trialData?.valorTrial),
            valorAposTrial: safeParseFloat(trialData?.valorAposTrial),
            moeda: 'BRL',
            frequenciaAposTrial: trialData.frequenciaAposTrial,
            taxaInscricao: safeParseFloat(trialData.taxaInscricao),
          }
          break
      }
    }

    return filtered
  }

  const submitForm = async () => {
    setSubmissionErrors(null)
    const dataToValidate = getFilteredFormData(formData)

    const validationResult = criarPlanoSchema.safeParse(dataToValidate)

    if (!validationResult.success) {
      const formattedErrors = validationResult.error.format()
      setSubmissionErrors(formattedErrors)
      toast.error('Preencha todos os campos corretamente')
      console.error('Erros de Validação:', formattedErrors)
      return
    }

    setIsSubmitting(true)

    try {
      const result: any = planId
        ? await updatePlan(planId, validationResult.data)
        : await createPlan(validationResult.data)

      if (result.success) {
        setSubmittedData(validationResult.data)
        if (!planId && result.data?.id) {
          setPlanId(result.data.id)
        }
      } else {
        if (result.errors) {
          setSubmissionErrors(result.errors)
        }
        toast.error(
          result.errors?._form || `Erro ao ${planId ? 'atualizar' : 'criar'} plano`,
        )
      }
    } catch (error) {
      console.error(`Erro ao ${planId ? 'atualizar' : 'criar'} plano:`, error)
      toast.error('Erro interno do servidor')
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData(defaultInitialData)
    setPlanId(null)
    setSubmittedData(null)
    setIsSubmitting(false)
    setSubmissionErrors(null)
  }

  return (
    <PlanFormContext.Provider
      value={{
        formData,
        planId,
        updateSection,
        submitForm,
        isSubmitting,
        submittedData,
        resetForm,
        submissionErrors,
        modalidadesDisponiveis,
        modalidadesLoading,
      }}
    >
      {children}
    </PlanFormContext.Provider>
  )
}