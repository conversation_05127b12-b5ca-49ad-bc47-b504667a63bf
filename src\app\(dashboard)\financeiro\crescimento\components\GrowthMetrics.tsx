'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, Calendar, DollarSign } from 'lucide-react';
import { getGrowthMetrics, GrowthMetrics as GrowthMetricsData } from '../actions/growth-actions';
import { formatCurrency } from '@/utils/format-utils';

interface GrowthMetricsProps {
  viewMode: 'year-to-date' | 'projection';
}

export function GrowthMetrics({ viewMode }: GrowthMetricsProps) {
  const [metrics, setMetrics] = useState<GrowthMetricsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      setLoading(true);
      setError(null);

      const result = await getGrowthMetrics();

      if (result.success && result.data) {
        setMetrics(result.data);
      } else {
        setError(result.error || 'Erro ao carregar métricas');
      }

      setLoading(false);
    };

    fetchMetrics();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 2 }).map((_, index) => (
          <Card key={index} className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
            <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
              <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-32 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="space-y-2">
                <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-24 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
                <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-32 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <div className="space-y-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
          <CardContent className="pt-8 pb-8">
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-red-600 dark:text-red-400 text-center font-medium">{error || 'Erro ao carregar métricas'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  return (
    <div className="flex flex-col gap-4 h-full">
      {/* Receita do período */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300 flex-1 flex flex-col">
        <CardHeader className="pb-4 bg-gradient-to-r from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className="p-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
              <TrendingUp className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
            </div>
            {viewMode === 'year-to-date'
              ? `Receita ${currentYear} (até ${new Date().toLocaleDateString('pt-BR', { month: 'short' })})`
              : `Projeção Receita ${currentYear}`
            }
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2 flex-1 flex items-center justify-center">
          <div className="text-center space-y-1">
            <p className="text-3xl font-bold text-emerald-600 dark:text-emerald-400 tracking-tight">
              {formatCurrency(viewMode === 'year-to-date' ? metrics.yearToDateRevenue : metrics.projectedYearRevenue)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {viewMode === 'year-to-date'
                ? `Janeiro a ${new Date().toLocaleDateString('pt-BR', { month: 'long' })}`
                : 'Projeção baseada na média mensal'
              }
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Receita média mensal */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300 flex-1 flex flex-col">
        <CardHeader className="pb-4 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
              <Calendar className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            Receita Média Mensal
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2 flex-1 flex items-center justify-center">
          <div className="text-center space-y-1">
            <p className="text-3xl font-bold text-blue-600 dark:text-blue-400 tracking-tight">
              {formatCurrency(
                viewMode === 'year-to-date'
                  ? metrics.yearToDateRevenue / currentMonth
                  : metrics.projectedYearRevenue / 12
              )}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {viewMode === 'year-to-date'
                ? `Baseado em ${currentMonth} meses`
                : 'Projeção para 12 meses'
              }
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Total a receber até final do ano - apenas no modo projeção */}
      {viewMode === 'projection' && (
        <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300 flex-1 flex flex-col">
          <CardHeader className="pb-4 bg-gradient-to-r from-purple-50/50 to-transparent dark:from-purple-900/20 dark:to-transparent">
            <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <div className="p-1 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <DollarSign className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              Total a Receber até Final do Ano
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2 flex-1 flex items-center justify-center">
            <div className="text-center space-y-1">
              <p className="text-3xl font-bold text-purple-600 dark:text-purple-400 tracking-tight">
                {formatCurrency(metrics.totalToReceiveUntilYearEnd)}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                Pagamentos pendentes + projeção dos planos ativos
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
