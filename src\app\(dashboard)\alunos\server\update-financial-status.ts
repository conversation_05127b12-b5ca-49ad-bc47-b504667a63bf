"use server";

import { createTenantServerClient } from "@/services/supabase/server";
import { calculateStudentFinancialStatus, calculateMultipleStudentsFinancialStatus } from "./financial-status";

/**
 * Atualiza o status financeiro de um estudante específico na tabela students
 */
export async function updateStudentFinancialStatus(studentId: string): Promise<{
    success: boolean;
    message: string;
    oldStatus?: string;
    newStatus?: string;
}> {
    try {
        const supabase = await createTenantServerClient();

        // Calcular o novo status financeiro
        const financialInfo = await calculateStudentFinancialStatus(studentId);

        // Buscar o status atual para comparação
        const { data: currentStudent, error: fetchError } = await supabase
            .from('students')
            .select('financial_status')
            .eq('id', studentId)
            .single();

        if (fetchError) {
            return {
                success: false,
                message: `Erro ao buscar estudante: ${fetchError.message}`
            };
        }

        const oldStatus = currentStudent.financial_status;
        const newStatus = financialInfo.financial_status;

        // Mapear "no_data" para um valor válido do banco
        const dbStatus = newStatus === "no_data" ? "up_to_date" : newStatus;

        // Só atualizar se o status mudou
        if (oldStatus === dbStatus) {
            return {
                success: true,
                message: "Status financeiro já está atualizado",
                oldStatus,
                newStatus: dbStatus
            };
        }

        // Atualizar o status na tabela students
        const { error: updateError } = await supabase
            .from('students')
            .update({
                financial_status: dbStatus,
                updated_at: new Date().toISOString()
            })
            .eq('id', studentId);

        if (updateError) {
            return {
                success: false,
                message: `Erro ao atualizar status: ${updateError.message}`
            };
        }

        return {
            success: true,
            message: `Status financeiro atualizado de "${oldStatus}" para "${newStatus}"`,
            oldStatus,
            newStatus
        };

    } catch (error) {
        return {
            success: false,
            message: `Erro inesperado: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        };
    }
}

/**
 * Atualiza o status financeiro de múltiplos estudantes
 */
export async function updateMultipleStudentsFinancialStatus(studentIds: string[]): Promise<{
    success: boolean;
    message: string;
    updated: number;
    errors: string[];
}> {
    const results = {
        success: true,
        message: "",
        updated: 0,
        errors: [] as string[]
    };

    try {
        const supabase = await createTenantServerClient();

        // Calcular status financeiro para todos os estudantes
        const financialStatusMap = await calculateMultipleStudentsFinancialStatus(studentIds);

        // Buscar status atuais
        const { data: currentStudents, error: fetchError } = await supabase
            .from('students')
            .select('id, financial_status')
            .in('id', studentIds);

        if (fetchError) {
            results.success = false;
            results.message = `Erro ao buscar estudantes: ${fetchError.message}`;
            return results;
        }

        // Preparar atualizações apenas para estudantes com status diferente
        const updates = [];
        for (const student of currentStudents || []) {
            const newStatus = financialStatusMap[student.id]?.financial_status;
            if (newStatus) {
                // Mapear "no_data" para um valor válido do banco
                const dbStatus = newStatus === "no_data" ? "up_to_date" : newStatus;

                if (student.financial_status !== dbStatus) {
                    updates.push({
                        id: student.id,
                        financial_status: dbStatus,
                        updated_at: new Date().toISOString()
                    });
                }
            }
        }

        if (updates.length === 0) {
            results.message = "Todos os status financeiros já estão atualizados";
            return results;
        }

        // Executar atualizações individuais (não podemos usar upsert com dados parciais)
        for (const update of updates) {
            const { error: updateError } = await supabase
                .from('students')
                .update({
                    financial_status: update.financial_status,
                    updated_at: update.updated_at
                })
                .eq('id', update.id);

            if (updateError) {
                results.errors.push(`Erro ao atualizar estudante ${update.id}: ${updateError.message}`);
            } else {
                results.updated += 1;
            }
        }

        results.success = results.errors.length === 0;
        results.message = results.success
            ? `${results.updated} estudantes atualizados com sucesso`
            : `${results.updated} estudantes atualizados, ${results.errors.length} erros encontrados`;

        return results;

    } catch (error) {
        results.success = false;
        results.message = `Erro inesperado: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
        return results;
    }
}

/**
 * Atualiza o status financeiro de todos os estudantes ativos
 */
export async function updateAllStudentsFinancialStatus(): Promise<{
    success: boolean;
    message: string;
    updated: number;
    total: number;
    errors: string[];
}> {
    try {
        const supabase = await createTenantServerClient();

        // Buscar todos os estudantes ativos
        const { data: students, error: fetchError } = await supabase
            .from('students')
            .select('id')
            .is('deleted_at', null);

        if (fetchError) {
            return {
                success: false,
                message: `Erro ao buscar estudantes: ${fetchError.message}`,
                updated: 0,
                total: 0,
                errors: [fetchError.message]
            };
        }

        const studentIds = (students || []).map(s => s.id);
        const total = studentIds.length;

        if (total === 0) {
            return {
                success: true,
                message: "Nenhum estudante encontrado",
                updated: 0,
                total: 0,
                errors: []
            };
        }

        // Atualizar em lotes para evitar sobrecarga
        const batchSize = 100;
        let totalUpdated = 0;
        const allErrors: string[] = [];

        for (let i = 0; i < studentIds.length; i += batchSize) {
            const batch = studentIds.slice(i, i + batchSize);
            const result = await updateMultipleStudentsFinancialStatus(batch);

            totalUpdated += result.updated;
            allErrors.push(...result.errors);
        }

        return {
            success: allErrors.length === 0,
            message: allErrors.length === 0
                ? `Todos os ${totalUpdated} estudantes foram atualizados com sucesso`
                : `${totalUpdated} de ${total} estudantes atualizados, ${allErrors.length} erros encontrados`,
            updated: totalUpdated,
            total,
            errors: allErrors
        };

    } catch (error) {
        return {
            success: false,
            message: `Erro inesperado: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
            updated: 0,
            total: 0,
            errors: [error instanceof Error ? error.message : 'Erro desconhecido']
        };
    }
}