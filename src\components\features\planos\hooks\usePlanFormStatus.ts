'use client'

import { usePlanForm } from '../PlanFormContext'

interface StatusInfo {
  text: string
  variant: 'default' | 'success' | 'warning' | 'info'
}

export function usePlanFormStatus() {
  const { formData } = usePlanForm()

  const getPricingStatus = (): StatusInfo => {
    const pricing = formData.pricing
    // Acessar dados extras que podem estar no formData
    const perSession = (formData as any).perSession
    const trial = (formData as any).trial

    if (!pricing?.tipo) {
      return { text: 'Selecione', variant: 'default' }
    }

    switch (pricing.tipo) {
      case 'recurring':
        if (!pricing.valor || pricing.valor === 0) {
          return { text: 'Gratuito', variant: 'success' }
        }
        const numeroFrequencia = (pricing as any).numeroFrequencia || 1
        const unidadeTempo = (pricing as any).frequencia
        let frequenciaTexto: string

        if (numeroFrequencia > 1) {
          const unidadePlural = unidadeTempo === 'month' ? 'meses' :
                                unidadeTempo === 'year' ? 'anos' :
                                unidadeTempo === 'week' ? 'semanas' : 'dias'
          frequenciaTexto = `/ ${numeroFrequencia} ${unidadePlural}`
        } else {
          const unidadeSingular = unidadeTempo === 'month' ? 'mês' :
                                  unidadeTempo === 'year' ? 'ano' :
                                  unidadeTempo === 'week' ? 'semana' : 'dia'
          frequenciaTexto = `/${unidadeSingular}`
        }
        return { text: `R$ ${pricing.valor}${frequenciaTexto}`, variant: 'info' }

      case 'one-time':
        if (!(pricing as any).custo || (pricing as any).custo === 0) {
          return { text: 'Gratuito', variant: 'success' }
        }
        return { text: `R$ ${(pricing as any).custo} (único)`, variant: 'info' }

      case 'per-session':
        // Tentar primeiro os dados do pricing, depois do perSession
        const perSessionCusto = (pricing as any).custo ?? perSession?.custo
        if (!perSessionCusto || perSessionCusto === 0) {
          return { text: 'Gratuito por aula', variant: 'success' }
        }
        return { text: `R$ ${perSessionCusto}/aula`, variant: 'warning' }

      case 'trial':
        // Tentar primeiro os dados do pricing, depois do trial
        const valorTrial = (pricing as any).valorDuranteTrial ?? trial?.valorTrial ?? trial?.valorDuranteTrial
        const isTrialGratuito = !valorTrial || valorTrial === 0

        // Buscar duração do pricing ou trial
        const duracaoObj = (pricing as any).duracao ?? trial?.duracao
        const duracaoValor = duracaoObj?.valor ?? duracaoObj ?? 1
        const trialUnidadeTempo = duracaoObj?.unidade ?? trial?.unidadeTempo ?? 'days'

        const duracaoNum = Number(duracaoValor)
        let unidade: string

        if (duracaoNum > 1) {
          unidade = trialUnidadeTempo === 'days' ? 'dias' :
                    trialUnidadeTempo === 'weeks' ? 'semanas' :
                    trialUnidadeTempo === 'months' ? 'meses' : ''
        } else {
          unidade = trialUnidadeTempo === 'days' ? 'dia' :
                    trialUnidadeTempo === 'weeks' ? 'semana' :
                    trialUnidadeTempo === 'months' ? 'mês' : ''
        }

        if (isTrialGratuito) {
          return { text: `Trial ${duracaoValor} ${unidade}`, variant: 'info' }
        }
        return { text: `Trial R$ ${valorTrial}`, variant: 'warning' }

      default:
        return { text: 'Selecione', variant: 'default' }
    }
  }

  const getDurationStatus = (): StatusInfo => {
    const duration = formData.duration

    if (!duration?.tipo) {
      return { text: 'Selecione', variant: 'default' }
    }

    switch (duration.tipo) {
      case 'ongoing':
        return { text: 'Contínuo', variant: 'warning' }

      case 'limited':
        const duracao = duration.duracao || 1
        let unidade: string
        if (duracao > 1) {
          unidade = duration.unidadeTempo === 'days' ? 'dias' :
                       duration.unidadeTempo === 'weeks' ? 'semanas' :
                       duration.unidadeTempo === 'months' ? 'meses' : 'anos'
        } else {
          unidade = duration.unidadeTempo === 'days' ? 'dia' :
                       duration.unidadeTempo === 'weeks' ? 'semana' :
                       duration.unidadeTempo === 'months' ? 'mês' : 'ano'
        }
        return { text: `${duracao} ${unidade}`, variant: 'info' }

      case 'specific':
        if (duration.dataInicio && duration.dataFim) {
          const inicio = new Date(duration.dataInicio).toLocaleDateString('pt-BR')
          const fim = new Date(duration.dataFim).toLocaleDateString('pt-BR')
          return { text: `${inicio} - ${fim}`, variant: 'info' }
        }
        return { text: 'Datas específicas', variant: 'warning' }

      default:
        return { text: 'Não configurado', variant: 'default' }
    }
  }

  const getAcademyAccessStatus = (): StatusInfo => {
    const access = formData.academyAccess

    if (!access?.frequencia) {
      return { text: 'Selecione', variant: 'default' }
    }

    if (access.frequencia === 'unlimited') {
      return { text: 'Ilimitado', variant: 'info' }
    }

    const quantidade = access.quantidade || 0
    
    // Novo formato para planos por aula: "{quantidade} por mês"
    if (access.frequencia === 'sessions') {
      const label = quantidade > 1 ? 'aulas' : 'aula'
      return { text: `${quantidade} ${label} por mês`, variant: 'warning' }
    }

    let frequenciaLabel: string;
    
    if (quantidade > 1) {
      frequenciaLabel = {
        days: 'dias',
        payment: 'por pagamento',
        week: 'por semana',
        month: 'por mês',
        year: 'por ano'
      }[access.frequencia] || ''
    } else {
      frequenciaLabel = {
        days: 'dia',
        payment: 'por pagamento',
        week: 'por semana',
        month: 'por mês',
        year: 'por ano'
      }[access.frequencia] || ''
    }

    if (access.frequencia === 'payment') {
      return { text: `${quantidade} ${frequenciaLabel}`, variant: 'warning' }
    }

    return { text: `${quantidade} ${frequenciaLabel}`, variant: 'warning' }
  }

  return {
    pricingStatus: getPricingStatus(),
    durationStatus: getDurationStatus(),
    academyAccessStatus: getAcademyAccessStatus()
  }
}