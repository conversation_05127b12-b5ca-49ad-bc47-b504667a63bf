# Migração: Subscriptions → Memberships & Plans

## Visão Geral

Esta migração introduz um novo sistema de gestão de planos e matrículas que substitui o sistema legado de `subscriptions`. O novo sistema oferece maior flexibilidade, versionamento de planos, e configurações avançadas para academias de artes marciais.

## Objetivos da Migração

1. **Versionamento de Planos**: Permitir múltiplas versões de um plano para histórico e A/B testing
2. **Configurações Flexíveis**: Suporte a diferentes tipos de preços (recorrente, único, por aula, trial)
3. **Controle de Acesso**: Configurações granulares de acesso às modalidades
4. **Auditoria**: Log completo de mudanças de status para compliance
5. **Multi-tenancy**: Configuração por tenant para múltiplas matrículas

## Arquitetura do Novo Sistema

### Tabelas Principais

#### `plans`
- **Propósito**: Armazenar configurações de planos com versionamento
- **Características**:
  - Versionamento através de `base_plan_id` e `version`
  - Configurações em JSONB para flexibilidade
  - Status: `draft`, `active`, `archived`
  - Isolamento por tenant

#### `memberships` 
- **Propósito**: Matrículas dos alunos em planos específicos
- **Características**:
  - Referência imutável ao plano (para histórico)
  - Status: `active`, `paused`, `canceled`, `expired`
  - Datas de início, fim e próxima cobrança
  - Metadados para extensibilidade

#### `membership_status_logs`
- **Propósito**: Auditoria de mudanças de status
- **Características**:
  - Log automático via triggers
  - Rastreamento de usuário e motivo
  - Compliance e debugging

## Scripts de Migração

### Ordem de Execução

1. **001_enums.sql** - Criar tipos enumerados
2. **002_plans.sql** - Criar tabela de planos
3. **003_memberships.sql** - Criar tabela de matrículas
4. **004_tenant_settings.sql** - Configurações de tenant
5. **005_payments_migration.sql** - Migrar referências de pagamentos
6. **006_enrollment_pauses_migration.sql** - Migrar pausas de matrícula
7. **007_status_logs.sql** - Criar auditoria
8. **008_archive_subscriptions.sql** - Arquivar tabela legada
9. **009_data_migration.sql** - Migrar dados (execução manual)

### Checklist de Execução

- [x] **Executar scripts 001-008 em sequência**
- [ ] **Testar scripts em ambiente de desenvolvimento**
- [x] **Executar migração de dados em transação de teste**
- [x] **Validar resultados da migração**
- [ ] **Aplicar em produção**

## Benefícios do Versionamento

### 1. Preservação de Contexto Histórico
```sql
-- Cada matrícula mantém referência ao plano exato usado na assinatura
SELECT m.*, p.title, p.pricing_config 
FROM memberships m 
JOIN plans p ON m.plan_id = p.id 
WHERE m.student_id = 'uuid-do-aluno';
```

### 2. Auditoria e Compliance
- Snapshots imutáveis para LGPD/GDPR
- Prova de "quais regras valiam" em qualquer data
- Histórico completo de mudanças

### 3. Evolução sem Breaking Changes
```sql
-- Criar nova versão do plano com preço atualizado
INSERT INTO plans (base_plan_id, version, title, pricing_config, ...)
SELECT id, 2, title, '{"valor": "250.00", ...}', ...
FROM plans WHERE id = 'plano-original';
```

### 4. Experimentação Controlada
- A/B tests com versões diferentes
- Promoções temporárias
- Rollback instantâneo reativando versão anterior

## Configurações por Tenant

### Múltiplas Matrículas
```sql
-- Permitir múltiplas matrículas ativas por aluno
UPDATE tenants 
SET settings = settings || '{"allow_multiple_memberships": true}'
WHERE id = 'tenant-uuid';
```

### Validação Automática
- Trigger `check_single_membership()` impede múltiplas matrículas quando não permitido
- Configurável por tenant via `settings.allow_multiple_memberships`

## Tipos de Configuração de Planos

### Pricing Config (JSONB)
```json
{
  "tipo": "recurring",
  "valor": "200.00",
  "frequencia": "month",
  "numeroFrequencia": "1",
  "taxaInscricao": "50.00"
}
```

### Duration Config (JSONB)
```json
{
  "tipo": "limited",
  "duracao": 12,
  "unidadeTempo": "months",
  "opcaoRenovacao": "auto-renew"
}
```

### Access Config (JSONB)
```json
{
  "frequencia": "unlimited",
  "capacidade": "unlimited",
  "modalidades": ["jiujitsu_adulto", "muay_thai"],
  "todasSessoes": true
}
```

## Validação da Migração

### Contagem de Registros
```sql
SELECT * FROM validate_subscription_migration();
```

### Verificação de Integridade
```sql
-- Verificar se todas as matrículas têm planos válidos
SELECT m.id, m.student_id, p.title 
FROM memberships m 
LEFT JOIN plans p ON m.plan_id = p.id 
WHERE p.id IS NULL;
```

### Log de Auditoria
```sql
-- Verificar logs de status
SELECT msl.*, m.student_id, p.title
FROM membership_status_logs msl
JOIN memberships m ON msl.membership_id = m.id
JOIN plans p ON m.plan_id = p.id
ORDER BY msl.changed_at DESC;
```

## Limpeza Pós-Migração

### Após Período de Carência (6-12 meses)

1. **Validar migração completa**
2. **Criar backup final da tabela legacy**
3. **Remover dependências antigas**
4. **Dropar tabela `subscriptions_legacy`**

```sql
-- Após validação completa
DROP TABLE IF EXISTS public.subscriptions_legacy CASCADE;
DROP VIEW IF EXISTS public.subscriptions_legacy_view;
DROP FUNCTION IF EXISTS public.get_legacy_subscription_count();
```

## Rollback (Se Necessário)

### Cenário de Emergência
1. **Parar aplicação**
2. **Restaurar backup**
3. **Reverter migrações** (ordem inversa)
4. **Validar integridade**
5. **Reiniciar aplicação**

### Rollback Parcial
```sql
-- Reverter para usar subscriptions_legacy temporariamente
ALTER TABLE public.subscriptions_legacy RENAME TO subscriptions;
-- Ajustar aplicação para usar tabela original
```

## Monitoramento Pós-Migração

### Métricas Importantes
- Contagem de planos ativos por tenant
- Distribuição de status de memberships
- Performance de queries com JSONB
- Logs de erro em triggers

### Queries de Monitoramento
```sql
-- Memberships por status
SELECT tenant_id, status, count(*) 
FROM memberships 
GROUP BY tenant_id, status;

-- Planos mais utilizados
SELECT p.title, count(m.id) as total_memberships
FROM plans p 
LEFT JOIN memberships m ON p.id = m.plan_id 
GROUP BY p.id, p.title 
ORDER BY total_memberships DESC;
```

## Suporte e Troubleshooting

### Problemas Comuns

1. **Múltiplas matrículas ativas**: Verificar configuração do tenant
2. **Dados JSONB inválidos**: Validar estrutura das configurações
3. **Performance lenta**: Verificar índices JSONB e queries
4. **Erros de trigger**: Verificar logs de auditoria

### Contato
Para questões sobre esta migração, consulte a documentação técnica ou abra uma issue no repositório do projeto.

---

**⚠️ IMPORTANTE**: Esta migração é irreversível após limpeza dos dados legados. Sempre mantenha backups atualizados e teste em ambiente não-produtivo primeiro. 