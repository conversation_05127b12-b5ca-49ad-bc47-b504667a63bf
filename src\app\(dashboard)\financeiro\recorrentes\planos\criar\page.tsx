import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { PlanFormProvider } from '@/components/features/planos'
import { CriarPlanoClient } from './CriarPlanoClient'

export const metadata: Metadata = {
  title: 'Criar Plano - Recorrentes',
  description: 'Criar novo plano de assinatura para a academia'
}

export default function CriarPlanoPage() {
  return (
    <PlanFormProvider>
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Button asChild variant="ghost" size="sm">
            <Link href="/financeiro/recorrentes/planos">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar aos Planos
            </Link>
          </Button>
        </div>
      </div>

      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Criar Novo Plano</h1>
        <p className="text-muted-foreground">
          Configure um novo plano de assinatura para sua academia.
        </p>
      </div>

      <CriarPlanoClient />
    </PlanFormProvider>
  )
}