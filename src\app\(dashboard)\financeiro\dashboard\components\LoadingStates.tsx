/**
 * Componentes de Loading States para o Dashboard Financeiro
 * Skeletons e estados de carregamento para diferentes seções
 */

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// ============================================================================
// SKELETON BASE
// ============================================================================

const Skeleton = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700",
        className
      )}
      {...props}
    />
  );
};

// ============================================================================
// LOADING PARA KPIs
// ============================================================================

export const KPICardSkeleton = () => (
  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
    <CardHeader className="pb-4">
      <Skeleton className="h-4 w-24" />
    </CardHeader>
    <CardContent className="pt-0">
      <div className="space-y-3">
        <Skeleton className="h-8 w-32" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-16" />
        </div>
        <Skeleton className="h-3 w-20" />
      </div>
    </CardContent>
  </Card>
);

export const KPIsSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
    {Array.from({ length: 6 }).map((_, index) => (
      <KPICardSkeleton key={index} />
    ))}
  </div>
);

// ============================================================================
// LOADING PARA GRÁFICOS
// ============================================================================

export const ChartSkeleton = ({ 
  height = "h-64", 
  title,
  className 
}: { 
  height?: string; 
  title?: string;
  className?: string;
}) => (
  <Card className={cn("shadow-lg", className)}>
    <CardHeader className="pb-4">
      {title ? (
        <div className="text-lg font-semibold">{title}</div>
      ) : (
        <Skeleton className="h-6 w-48" />
      )}
    </CardHeader>
    <CardContent>
      <div className={cn("w-full rounded-lg", height)}>
        <Skeleton className="w-full h-full" />
      </div>
      <div className="mt-4 space-y-2">
        <div className="flex justify-between">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-20" />
        </div>
        <div className="flex justify-between">
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
    </CardContent>
  </Card>
);

// ============================================================================
// LOADING PARA TABELAS
// ============================================================================

export const TableSkeleton = ({ 
  rows = 5, 
  columns = 4,
  title,
  className 
}: { 
  rows?: number; 
  columns?: number;
  title?: string;
  className?: string;
}) => (
  <Card className={cn("shadow-lg", className)}>
    <CardHeader className="pb-4">
      {title ? (
        <div className="text-lg font-semibold">{title}</div>
      ) : (
        <Skeleton className="h-6 w-40" />
      )}
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        {/* Header da tabela */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} className="h-4 w-full" />
          ))}
        </div>
        
        {/* Linhas da tabela */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div 
            key={rowIndex} 
            className="grid gap-4" 
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} className="h-4 w-full" />
            ))}
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

// ============================================================================
// LOADING PARA SEÇÕES ESPECÍFICAS
// ============================================================================

export const RevenueSectionSkeleton = () => (
  <div className="space-y-6">
    <ChartSkeleton title="Receitas" height="h-80" />
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <ChartSkeleton height="h-48" />
      <TableSkeleton rows={3} columns={3} />
    </div>
  </div>
);

export const ExpenseSectionSkeleton = () => (
  <div className="space-y-6">
    <ChartSkeleton title="Despesas" height="h-80" />
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <ChartSkeleton height="h-48" />
      <TableSkeleton rows={4} columns={3} />
    </div>
  </div>
);

export const CashFlowSectionSkeleton = () => (
  <div className="space-y-6">
    <ChartSkeleton title="Fluxo de Caixa" height="h-80" />
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card className="shadow-lg">
        <CardHeader>
          <Skeleton className="h-5 w-32" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </CardContent>
      </Card>
      <Card className="shadow-lg">
        <CardHeader>
          <Skeleton className="h-5 w-28" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </CardContent>
      </Card>
      <Card className="shadow-lg">
        <CardHeader>
          <Skeleton className="h-5 w-24" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </CardContent>
      </Card>
    </div>
  </div>
);

// ============================================================================
// LOADING COMPLETO DO DASHBOARD
// ============================================================================

export const DashboardSkeleton = () => (
  <div className="space-y-8">
    {/* Filtros */}
    <Card className="shadow-lg">
      <CardHeader>
        <Skeleton className="h-6 w-48" />
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Skeleton className="h-4 w-16 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="flex-1">
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-8 w-full" />
          </div>
          <div>
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      </CardContent>
    </Card>

    {/* KPIs */}
    <KPIsSkeleton />

    {/* Seções principais */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-1">
        <RevenueSectionSkeleton />
      </div>
      <div className="lg:col-span-1">
        <ExpenseSectionSkeleton />
      </div>
      <div className="lg:col-span-1">
        <CashFlowSectionSkeleton />
      </div>
    </div>

    {/* Seção inferior */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <ChartSkeleton title="Análises Avançadas" height="h-64" />
      <TableSkeleton title="Transações Recentes" rows={6} columns={4} />
    </div>
  </div>
);

// ============================================================================
// LOADING STATES ESPECÍFICOS
// ============================================================================

export const LoadingStates = {
  KPI: KPICardSkeleton,
  KPIs: KPIsSkeleton,
  Chart: ChartSkeleton,
  Table: TableSkeleton,
  Revenue: RevenueSectionSkeleton,
  Expense: ExpenseSectionSkeleton,
  CashFlow: CashFlowSectionSkeleton,
  Dashboard: DashboardSkeleton,
  Skeleton
};

export default LoadingStates;
