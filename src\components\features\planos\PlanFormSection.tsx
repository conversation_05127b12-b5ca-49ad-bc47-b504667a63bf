'use client'

import React from 'react'
import { AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion'
import { cn } from '@/lib/utils'

interface PlanFormSectionProps {
  value: string
  children: React.ReactNode
  className?: string
}

interface PlanFormSectionHeaderProps {
  icon: React.ReactNode
  title: string
  description: string
  iconColor?: 'blue' | 'green' | 'orange' | 'purple'
  status?: React.ReactNode
}

interface PlanFormSectionContentProps {
  children: React.ReactNode
  className?: string
}

interface PlanFormSectionStatusProps {
  children: React.ReactNode
  variant?: 'default' | 'success' | 'warning' | 'info'
}

const iconColorClasses = {
  blue: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400',
  green: 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400',
  orange: 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400',
  purple: 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
}

const statusVariants = {
  default: 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100',
  success: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200',
  warning: 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200',
  info: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
}

export function PlanFormSection({ value, children, className }: PlanFormSectionProps) {
  return (
    <AccordionItem value={value} className={cn('border rounded-lg px-4', className)}>
      {children}
    </AccordionItem>
  )
}

export function PlanFormSectionHeader({
  icon,
  title,
  description,
  iconColor = 'blue',
  status
}: PlanFormSectionHeaderProps) {
  return (
    <AccordionTrigger className="hover:no-underline">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-3">
          <div className={cn('p-2 rounded-lg', iconColorClasses[iconColor])}>
            {icon}
          </div>
          <div className="text-left">
            <h3 className="font-semibold">{title}</h3>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </div>
        {status && (
          <div className="mr-4">
            {status}
          </div>
        )}
      </div>
    </AccordionTrigger>
  )
}

export function PlanFormSectionContent({ children, className }: PlanFormSectionContentProps) {
  return (
    <AccordionContent className={cn('pt-4', className)}>
      {children}
    </AccordionContent>
  )
}

export function PlanFormSectionStatus({ children, variant = 'default' }: PlanFormSectionStatusProps) {
  return (
    <span className={cn(
      'px-3 py-1 rounded-md text-sm font-medium',
      statusVariants[variant]
    )}>
      {children}
    </span>
  )
}
