/**
 * Ícones otimizados para páginas de presença
 * Imports específicos para reduzir bundle size
 */

import { memo } from 'react';
import type { LucideProps } from 'lucide-react';

// Import específico de cada ícone para tree-shaking otimizado
import { 
  ArrowLeft,
  Users, 
  QrCode, 
  FileText 
} from 'lucide-react';

// Componentes memoizados para evitar re-renders desnecessários
export const ArrowLeftIcon = memo<LucideProps>((props) => <ArrowLeft {...props} />);
ArrowLeftIcon.displayName = 'ArrowLeftIcon';

export const UsersIcon = memo<LucideProps>((props) => <Users {...props} />);
UsersIcon.displayName = 'UsersIcon';

export const QrCodeIcon = memo<LucideProps>((props) => <QrCode {...props} />);
QrCodeIcon.displayName = 'QrCodeIcon';

export const FileTextIcon = memo<LucideProps>((props) => <FileText {...props} />);
FileTextIcon.displayName = 'FileTextIcon';

// Tamanhos padrão para consistência
export const iconSizes = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-5 w-5',
  xl: 'h-6 w-6'
} as const;

// Props padrão para ícones de navegação
export const navigationIconProps: LucideProps = {
  className: iconSizes.md
};

// Props padrão para ícones de status
export const statusIconProps: LucideProps = {
  className: iconSizes.md
};
