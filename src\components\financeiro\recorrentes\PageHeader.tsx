'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Plus, Package } from 'lucide-react';

interface PageHeaderProps {
  title: string;
  description: string;
}

export function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
        <div className="flex space-x-3">
          <Button asChild variant="outline">
            <Link href="/financeiro/recorrentes/planos">
              <Package className="h-4 w-4 mr-2" />
              Gerenciar Planos
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
