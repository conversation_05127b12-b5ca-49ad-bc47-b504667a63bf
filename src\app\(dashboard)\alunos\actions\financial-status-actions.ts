"use server";

import { 
  updateStudentFinancialStatus, 
  updateMultipleStudentsFinancialStatus, 
  updateAllStudentsFinancialStatus 
} from "../server/update-financial-status";

/**
 * Action para atualizar o status financeiro de um estudante
 */
export async function updateStudentFinancialStatusAction(studentId: string) {
  return await updateStudentFinancialStatus(studentId);
}

/**
 * Action para atualizar o status financeiro de múltiplos estudantes
 */
export async function updateMultipleStudentsFinancialStatusAction(studentIds: string[]) {
  return await updateMultipleStudentsFinancialStatus(studentIds);
}

/**
 * Action para atualizar o status financeiro de todos os estudantes
 */
export async function updateAllStudentsFinancialStatusAction() {
  return await updateAllStudentsFinancialStatus();
}