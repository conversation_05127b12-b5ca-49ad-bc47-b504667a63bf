'use client'

import { MensalidadesTabProps } from "./types"
import { usePayments, useStudentPlan } from "./hooks"
import {
  PaymentStatus,
  PaymentStats,
  PaymentHistory,
  UpcomingPayments,
  PaymentInfo,
  StudentPlan
} from "./components"
import { RealtimeIndicator } from "./components/RealtimeIndicator"
import { UpdateHighlight } from "./components/UpdateHighlight"

export function MensalidadesTab({ userId }: MensalidadesTabProps) {
  const {
    historicoPagamentos,
    proximosPagamentos,
    pagamentoInfo,
    valorMensalidade,
    loading,
    error,
    refetch,
    // Real-time properties
    isPolling,
    lastUpdate,
    connectionStatus
  } = usePayments(userId)

  const {
    currentPlan,
    loading: planLoading
  } = useStudentPlan(userId)

  if (error) {
    return (
      <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
        <div className="text-center py-8">
          <p className="text-red-600 dark:text-red-400">
            Erro ao carregar dados de pagamento: {error}
          </p>
        </div>
      </div>
    )
  }

  // Determine real-time status for indicator
  const getRealtimeStatus = () => {
    if (connectionStatus === 'error') return 'error'
    if (isPolling) return 'updating'
    if (connectionStatus === 'disconnected') return 'idle'
    return 'idle'
  }

  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {/* Real-time Status Indicator */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Recorrencias
        </h2>
        <RealtimeIndicator
          status={getRealtimeStatus()}
          lastUpdate={lastUpdate || undefined}
          className="ml-auto"
        />
      </div>

      {/* Plano do Aluno */}
      <UpdateHighlight isUpdated={!!lastUpdate} highlightColor="blue">
        <StudentPlan
          userId={userId}
          loading={loading || planLoading}
          onPlanUpdate={refetch}
        />
      </UpdateHighlight>

      {/* Status do Pagamento Atual */}
      <UpdateHighlight isUpdated={!!lastUpdate} highlightColor="green">
        <PaymentStatus
          pagamentoInfo={pagamentoInfo}
          valorMensalidade={valorMensalidade}
          loading={loading || planLoading}
          currentPlan={currentPlan}
          userId={userId}
          onUpdate={refetch}
        />
      </UpdateHighlight>

      {/* Grid de Estatísticas */}
      <UpdateHighlight isUpdated={!!lastUpdate} highlightColor="blue">
        <PaymentStats
          pagamentoInfo={pagamentoInfo}
          historicoPagamentos={historicoPagamentos}
          loading={loading}
        />
      </UpdateHighlight>

      {/* Histórico de Pagamentos */}
      <UpdateHighlight isUpdated={!!lastUpdate} highlightColor="yellow">
        <PaymentHistory
          historicoPagamentos={historicoPagamentos}
          loading={loading}
          onUpdate={refetch}
        />
      </UpdateHighlight>

      {/* Próximos Pagamentos */}
      <UpdateHighlight isUpdated={!!lastUpdate} highlightColor="blue">
        <UpcomingPayments
          proximosPagamentos={proximosPagamentos}
          loading={loading}
          onUpdate={refetch}
        />
      </UpdateHighlight>

      {/* Informações de Pagamento */}
      {/* <PaymentInfo pagamentoInfo={pagamentoInfo} /> */}
    </div>
  )
}
