'use client'

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip'
import {
  Edit3,
  Trash2,
  Calendar,
  DollarSign,
  Users,
  Play,
  Pause,
  Clock,
  Copy,
  UserCheck,
} from 'lucide-react'
import { GiKimono } from "react-icons/gi";
import { Plan, PlanStatus } from '../types'
import {
  publishPlan,
  pausePlan,
  duplicatePlan,
} from '@/app/(dashboard)/academia/actions/plan-actions'
import React, { useTransition } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { PriceDisplay } from './PriceDisplay'

// Componente wrapper para tooltips
interface TooltipWrapperProps {
  children: React.ReactNode
  content: string
  side?: 'top' | 'right' | 'bottom' | 'left'
}

function TooltipWrapper({ children, content, side = 'top' }: TooltipWrapperProps) {
  return (
    <TooltipRoot>
      <TooltipTrigger asChild>
        {children}
      </TooltipTrigger>
      <TooltipContent side={side}>
        <p>{content}</p>
      </TooltipContent>
    </TooltipRoot>
  )
}

interface PlanCardProps {
  plan: Plan
}

export function PlanCard({ plan }: PlanCardProps) {
  const {
    id,
    title,
    status,
    price,
    period,
    billing_period,
    students,
    monthly_revenue,
    annual_revenue,
    benefits,
    modalities,
    theme,
    discount,
    duration,
    capacity,
    max_capacity,
  } = plan
  const Icon = theme.icon
  const router = useRouter()
  const [isPending, startTransition] = useTransition()

  const revenue = annual_revenue
    ? new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(annual_revenue)
    : monthly_revenue
      ? new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(monthly_revenue)
      : 'N/A'
  const revenueLabel = annual_revenue ? 'Receita Anual' : 'Receita Mensal'

  const getFormattedDuration = (durationStr?: string) => {
    if (!durationStr) {
      return null
    }

    try {
      const durationData = JSON.parse(durationStr)
      if (durationData && durationData.type === 'limited') {
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const { value, unit } = durationData
        const unitTranslations: { [key: string]: string } = {
          days: value === 1 ? 'dia' : 'dias',
          weeks: value === 1 ? 'semana' : 'semanas',
          months: value === 1 ? 'mês' : 'meses',
          years: value === 1 ? 'ano' : 'anos',
        }
        return `${value} ${unitTranslations[unit] || unit}`
      }
      return durationStr
    } catch (e) {
      return durationStr
    }
  }

  const formattedDuration = getFormattedDuration(duration)

  const formattedCancellationFee = (fee?: number | null) => {
    if (fee === undefined || fee === null || fee === 0) return null
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(fee)
  }
  const cancellationFeeLabel = formattedCancellationFee(plan.cancellation_fee)

  const formattedCurrency = (value?: number | null) => {
    if (value === undefined || value === null || value === 0) return null
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value)
  }

  const enrollmentFeeLabel = formattedCurrency(plan.enrollment_fee)

  const lateFeeLabel = formattedCurrency(plan.late_fee)

  const combinedLateFeeLabel =
    lateFeeLabel && plan.late_days !== undefined && plan.late_days !== null && plan.late_days > 0
      ? `${lateFeeLabel} quando ${plan.late_days} dias de atraso`
      : lateFeeLabel

  const maxPaymentsLabel =
    plan.max_payments !== undefined && plan.max_payments !== null
      ? `${plan.max_payments}`
      : null

  const statusBadgeMap: Record<PlanStatus, React.ReactNode> = {
    active: <Badge variant="secondary">Ativo</Badge>,
    draft: <Badge variant="default">Rascunho</Badge>,
    archived: <Badge variant="destructive">Arquivado</Badge>,
    paused: <Badge variant="outline">Pausado</Badge>,
  }

  const handlePublishPlan = () => {
    startTransition(async () => {
      const result = await publishPlan({ planoId: id })
      
      if (result.success) {
        toast.success('Plano publicado com sucesso!')
        router.refresh()
      } else {
        toast.error(result.errors?._form || 'Erro ao publicar o plano')
      }
    })
  }

  const handlePausePlan = () => {
    startTransition(async () => {
      const result = await pausePlan({ planoId: id })

      if (result.success) {
        // Usar a mensagem personalizada retornada pela função
        const message = (result.data as any)?.message || 'Plano pausado com sucesso!'
        toast.success(message)
        router.refresh()
      } else {
        toast.error(result.errors?._form || 'Erro ao pausar o plano')
      }
    })
  }

  const handleDuplicatePlan = () => {
    startTransition(async () => {
      const result = await duplicatePlan({ planoId: id })
      
      if (result.success) {
        toast.success('Plano duplicado com sucesso! Nova versão criada como rascunho.')
        router.refresh()
      } else {
        toast.error(result.errors?._form || 'Erro ao duplicar o plano')
      }
    })
  }

  const handleEdit = () => {
    if (status === 'draft') {
      router.push(`/financeiro/recorrentes/planos/${id}/editar`)
    } else {
      startTransition(async () => {
        const result = await duplicatePlan({ planoId: id })
        if (result.success && result.data) {
          const newPlanId = (result.data as any).plan_id;
          if (newPlanId) {
            toast.success('Nova versão criada para edição.')
            router.push(`/financeiro/recorrentes/planos/${newPlanId}/editar`)
          } else {
            toast.error('Não foi possível obter o ID da nova versão do plano.')
          }
        } else {
          toast.error(result.errors?._form || 'Erro ao criar nova versão do plano para edição.')
        }
      })
    }
  }

  const getStatusTooltip = (status: PlanStatus) => {
    const tooltips = {
      active: 'Plano ativo e disponível para novas matrículas',
      draft: 'Plano em rascunho - ainda não está disponível para matrículas',
      archived: 'Plano arquivado - não está mais disponível',
      paused: 'Plano pausado - temporariamente indisponível para novas matrículas'
    }
    return tooltips[status]
  }

  return (
    <TooltipProvider>
      <Card className="flex flex-col border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
        <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-gray-100 dark:bg-gray-800/50 rounded-full">
                <Icon className={`h-4 w-4 ${theme.iconColor}`} />
              </div>
              <CardTitle className="text-lg font-semibold text-gray-700 dark:text-gray-300">{title}</CardTitle>
            </div>
            <TooltipWrapper content={getStatusTooltip(status)} side="left">
              {statusBadgeMap[status]}
            </TooltipWrapper>
          </div>
        </CardHeader>
      <CardContent className="mt-6 flex flex-col flex-1 space-y-4">
        <PriceDisplay
          price={price}
          period={period}
          discount={discount}
          theme={theme}
        />

        <div className="space-y-3">
          <TooltipWrapper content="Número total de alunos matriculados neste plano" side="right">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center text-muted-foreground">
                <Users className="h-4 w-4 mr-2" />
                Alunos Ativos
              </span>
              <span className="font-medium">{students}</span>
            </div>
          </TooltipWrapper>
          <TooltipWrapper content={`${revenueLabel.toLowerCase()} gerada por este plano`} side="right">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center text-muted-foreground">
                <DollarSign className="h-4 w-4 mr-2" />
                {revenueLabel}
              </span>
              <span className="font-medium">{revenue}</span>
            </div>
          </TooltipWrapper>
          <TooltipWrapper content="Frequência de cobrança do plano" side="right">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center text-muted-foreground">
                <Calendar className="h-4 w-4 mr-2" />
                Periodicidade
              </span>
              <span className="font-medium">{billing_period}</span>
            </div>
          </TooltipWrapper>
          {formattedDuration && (
            <TooltipWrapper content="Tempo de duração do plano de assinatura" side="right">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <Clock className="h-4 w-4 mr-2" />
                  Duração
                </span>
                <span className="font-medium whitespace-pre-line">{formattedDuration}</span>
              </div>
            </TooltipWrapper>
          )}

          {cancellationFeeLabel && (
            <TooltipWrapper content="Taxa cobrada quando o aluno cancela o plano" side="right">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Taxa de Cancelamento
                </span>
                <span className="font-medium">{cancellationFeeLabel}</span>
              </div>
            </TooltipWrapper>
          )}

          {enrollmentFeeLabel && (
            <TooltipWrapper content="Taxa única cobrada no momento da matrícula" side="right">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Taxa de Inscrição
                </span>
                <span className="font-medium">{enrollmentFeeLabel}</span>
              </div>
            </TooltipWrapper>
          )}

          {combinedLateFeeLabel && (
            <TooltipWrapper content="Taxa adicional aplicada quando o pagamento está em atraso" side="right">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Taxa de Atraso
                </span>
                <span className="font-medium whitespace-pre-line text-right">{combinedLateFeeLabel}</span>
              </div>
            </TooltipWrapper>
          )}

          {maxPaymentsLabel && (
            <TooltipWrapper content="Número máximo de parcelas permitidas para este plano" side="right">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <Calendar className="h-4 w-4 mr-2" />
                  Máx. Pagamentos
                </span>
                <span className="font-medium">{maxPaymentsLabel}</span>
              </div>
            </TooltipWrapper>
          )}

          {modalities && modalities.length > 0 && (
            <TooltipWrapper content="Modalidades incluídas neste plano" side="right">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <GiKimono className="h-4 w-4 mr-2" />
                  Modalidades
                </span>
                <span className="font-medium text-right">
                  {modalities.join(', ')}
                </span>
              </div>
            </TooltipWrapper>
          )}

          {capacity && (
            <TooltipWrapper
              content={
                capacity === 'unlimited'
                  ? "Este plano permite um número ilimitado de alunos"
                  : `Este plano está limitado a ${max_capacity || 'N/A'} alunos`
              }
              side="right"
            >
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center text-muted-foreground">
                  <UserCheck className="h-4 w-4 mr-2" />
                  Capacidade
                </span>
                <span className="font-medium">
                  {capacity === 'unlimited' ? 'Ilimitada' : `${max_capacity || 'N/A'} alunos`}
                </span>
              </div>
            </TooltipWrapper>
          )}
        </div>

        <div className="flex-1 pt-3 border-t">
          <p className="text-sm text-muted-foreground mb-2">Benefícios:</p>
          <ul className="text-sm space-y-1">
            {benefits.map((benefit, index) => (
              <li key={index}>• {benefit}</li>
            ))}
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col gap-2 pt-4">
        <div className="grid grid-cols-2 gap-2 w-full">
          <TooltipWrapper
            content={status === 'draft' ? 'Editar este plano rascunho' : 'Criar uma nova versão editável deste plano'}
            side="top"
          >
            <Button variant="outline" size="sm" onClick={handleEdit} disabled={isPending}>
              <Edit3 className="h-4 w-4 mr-2" />
              {status === 'draft' ? 'Editar Rascunho' : 'Criar Nova Versão'}
            </Button>
          </TooltipWrapper>
          <TooltipWrapper content="Criar uma cópia deste plano como rascunho" side="top">
            <Button variant="outline" size="sm" onClick={handleDuplicatePlan} disabled={isPending}>
              <Copy className="h-4 w-4 mr-2" />
              Duplicar
            </Button>
          </TooltipWrapper>
        </div>
        <div className="grid grid-cols-2 gap-2 w-full">
          {status === 'draft' ? (
            <TooltipWrapper content="Tornar este plano disponível para novas matrículas" side="top">
              <Button size="sm" onClick={handlePublishPlan} disabled={isPending}>
                <Play className="h-4 w-4 mr-2" />
                Publicar
              </Button>
            </TooltipWrapper>
          ) : status === 'active' ? (
            <TooltipWrapper content="Pausar temporariamente este plano (alunos atuais mantêm acesso)" side="top">
              <Button variant="secondary" size="sm" onClick={handlePausePlan} disabled={isPending}>
                <Pause className="h-4 w-4 mr-2" />
                Pausar
              </Button>
            </TooltipWrapper>
          ) : status === 'paused' ? (
            <TooltipWrapper content="Reativar este plano para novas matrículas" side="top">
              <Button size="sm" onClick={handlePublishPlan} disabled={isPending}>
                <Play className="h-4 w-4 mr-2" />
                Reativar
              </Button>
            </TooltipWrapper>
          ) : (
            <div></div>
          )}
          {status !== 'active' && (
            <TooltipWrapper content="Excluir permanentemente este plano" side="top">
              <Button
                variant="outline"
                size="sm"
                className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                disabled={isPending}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Excluir
              </Button>
            </TooltipWrapper>
          )}
        </div>
      </CardFooter>
    </Card>
    </TooltipProvider>
  )
}