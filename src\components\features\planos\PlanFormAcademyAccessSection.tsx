'use client'

import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { configuracaoAcessoFormSchema as academyAccessSchema } from '@/schemas/plan-schemas'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Lock, Unlock } from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'
import { Skeleton } from '@/components/ui/skeleton'

type AcademyAccessFormValues = z.infer<typeof academyAccessSchema>

interface PlanFormAcademyAccessSectionProps {
  defaultValues?: Partial<AcademyAccessFormValues>
}

// Somente opções necessárias: Por Aula e Ilimitado
const frequenciaOpcoes = [
  { value: 'sessions', label: 'Por Aula' },
  { value: 'unlimited', label: 'Ilimitado' }
]

// Componente de skeleton para as modalidades
function ModalitiesSkeleton() {
  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium text-foreground">
        MODALIDADES
      </Label>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 p-3 rounded-lg border border-border">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4 rounded" />
            <Skeleton className="h-4 w-24" />
          </div>
        ))}
      </div>
      <Skeleton className="h-4 w-48" />
    </div>
  )
}

export function PlanFormAcademyAccessSection({
  defaultValues
}: PlanFormAcademyAccessSectionProps) {
  const {
    formData,
    updateSection,
    submissionErrors,
    modalidadesDisponiveis,
    modalidadesLoading
  } = usePlanForm()
  const [isClient, setIsClient] = useState(false)
  
  const {
    register,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<AcademyAccessFormValues>({
    resolver: zodResolver(academyAccessSchema),
    defaultValues: {
      frequencia: formData.academyAccess?.frequencia || defaultValues?.frequencia || 'unlimited',
      quantidade: formData.academyAccess?.quantidade || defaultValues?.quantidade || 3,
      capacidade: formData.academyAccess?.capacidade || defaultValues?.capacidade || 'unlimited',
      capacidadeMaxima: formData.academyAccess?.capacidadeMaxima || defaultValues?.capacidadeMaxima,
      modalidades: formData.academyAccess?.modalidades || defaultValues?.modalidades || [],
      // todasSessoes removido do frontend
    }
  })

  // Controlar hidratação
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (formData.academyAccess && isClient) {
      reset({
        frequencia: formData.academyAccess.frequencia,
        quantidade: formData.academyAccess.quantidade || 3,
        capacidade: formData.academyAccess.capacidade,
        capacidadeMaxima: formData.academyAccess.capacidadeMaxima,
        modalidades: formData.academyAccess.modalidades,
      })
    }
  }, [isClient, reset]) // Executar quando o cliente estiver pronto

  // Forçar re-renderização quando submissionErrors mudarem para garantir feedback visual imediato
  useEffect(() => {
    if (submissionErrors?.academyAccess?.modalidades) {
      // Força uma re-renderização do componente para mostrar o erro visualmente
      // quando o usuário navegar para esta seção devido a um erro de validação
    }
  }, [submissionErrors])

  const selectedFrequencia = watch('frequencia')
  const selectedCapacidade = watch('capacidade')
  const selectedModalidades = watch('modalidades')

  // Limpar capacidadeMaxima quando capacidade for unlimited
  useEffect(() => {
    if (selectedCapacidade === 'unlimited') {
      setValue('capacidadeMaxima', undefined)
    }
  }, [selectedCapacidade, setValue])

  // Sincronizar mudanças com o context
  useEffect(() => {
    const subscription = watch((value) => {
      // Remover configuracoes adicionais antes de enviar ao contexto
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { ...sanitized } = value as AcademyAccessFormValues
      updateSection('academyAccess', sanitized as AcademyAccessFormValues)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection])



  const handleModalidadeChange = (modalidadeId: string, checked: boolean) => {
    const currentModalidades = selectedModalidades || []
    if (checked) {
      setValue('modalidades', [...currentModalidades, modalidadeId])
    } else {
      setValue('modalidades', currentModalidades.filter(id => id !== modalidadeId))
    }
  }

  const getQuantidadeLabel = () => {
    switch (selectedFrequencia) {
      case 'sessions':
        return 'por mês'
      case 'unlimited':
        return 'Ilimitado'
      default:
        return ''
    }
  }

  const modalidadesError = submissionErrors?.academyAccess?.modalidades?._errors?.[0]

  // Evitar problemas de hidratação
  if (!isClient) {
    return <div>Carregando...</div>
  }

  return (
    <div className="space-y-8">
      {/* Frequência de Acesso */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label className="text-sm font-medium text-foreground">
            FREQUÊNCIA DE ACESSO
          </Label>
          <Select 
            value={selectedFrequencia} 
            onValueChange={(value) => setValue('frequencia', value as any)}
          >
            <SelectTrigger className={cn(
              'w-full',
              errors.frequencia && 'border-red-500 focus:border-red-500'
            )}>
              <SelectValue placeholder="Selecione a frequência" />
            </SelectTrigger>
            <SelectContent>
              {frequenciaOpcoes.map((opcao) => (
                <SelectItem key={opcao.value} value={opcao.value}>
                  {opcao.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.frequencia && (
            <p className="text-sm text-red-600">{errors.frequencia.message}</p>
          )}
        </div>

        {selectedFrequencia !== 'unlimited' && (
          <div className="space-y-3">
            <Label htmlFor="quantidade" className="text-sm font-medium text-foreground">
              QUANTIDADE
            </Label>
            <div className="relative">
              <Input
                id="quantidade"
                type="number"
                min="1"
                placeholder="3"
                {...register('quantidade', { valueAsNumber: true })}
                className={cn(
                  'w-full pr-24',
                  errors.quantidade && 'border-red-500 focus:border-red-500'
                )}
              />
              <div className="absolute inset-y-0 right-3 flex items-center text-sm text-muted-foreground">
                {getQuantidadeLabel()}
              </div>
            </div>
            {errors.quantidade && (
              <p className="text-sm text-red-600">{errors.quantidade.message}</p>
            )}
          </div>
        )}
      </div>

      {/* Capacidade */}
      <div className="space-y-4">
        <Label className="text-sm font-medium text-foreground">
          CAPACIDADE
        </Label>
        <div className="flex gap-3">
          <Button
            type="button"
            variant={selectedCapacidade === 'unlimited' ? 'default' : 'outline'}
            size="sm"
            onClick={() => {
              setValue('capacidade', 'unlimited')
              // Quando capacidade é ilimitada, definir capacidadeMaxima como undefined
              setValue('capacidadeMaxima', undefined)
            }}
            className={cn(
              'flex items-center gap-2 px-4 py-2',
              selectedCapacidade === 'unlimited'
                ? 'bg-emerald-100 dark:bg-emerald-900/30 border-emerald-300 dark:border-emerald-600 text-emerald-700 dark:text-emerald-300 hover:bg-emerald-200 dark:hover:bg-emerald-900/50'
                : 'border-input text-foreground hover:bg-accent hover:text-accent-foreground'
            )}
          >
            <Unlock className="h-4 w-4" />
            Ilimitada
          </Button>
          <Button
            type="button"
            variant={selectedCapacidade === 'limited' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setValue('capacidade', 'limited')}
            className={cn(
              'flex items-center gap-2 px-4 py-2',
              selectedCapacidade === 'limited'
                ? 'bg-orange-100 dark:bg-orange-900/30 border-orange-300 dark:border-orange-600 text-orange-700 dark:text-orange-300 hover:bg-orange-200 dark:hover:bg-orange-900/50'
                : 'border-input text-foreground hover:bg-accent hover:text-accent-foreground'
            )}
          >
            <Lock className="h-4 w-4" />
            Limitada
          </Button>
        </div>
        {errors.capacidade && (
          <p className="text-sm text-red-600">{errors.capacidade.message}</p>
        )}

        {/* Input para capacidade máxima quando limitada */}
        {selectedCapacidade === 'limited' && (
          <div className="ml-4 max-w-sm space-y-2">
            <Label htmlFor="capacidadeMaxima" className="text-sm font-medium text-foreground">
              NÚMERO MÁXIMO *
            </Label>
            <Input
              id="capacidadeMaxima"
              type="number"
              min="1"
              placeholder="20"
              {...register('capacidadeMaxima', {
                setValueAs: (value) => {
                  // Se o valor está vazio, retornar null
                  if (value === '' || value === null || value === undefined) return null;

                  // Converter para número
                  const parsed = Number(value);
                  return isNaN(parsed) ? null : parsed;
                }
              })}
              disabled={selectedCapacidade !== 'limited'}
              className={cn(
                'w-full',
                selectedCapacidade !== 'limited' && 'bg-muted text-muted-foreground',
                errors.capacidadeMaxima && 'border-red-500 focus:border-red-500'
              )}
            />
            {errors.capacidadeMaxima && (
              <p className="text-sm text-red-600">{errors.capacidadeMaxima.message}</p>
            )}
          </div>
        )}
      </div>

      {/* Modalidades */}
      {modalidadesLoading ? (
        <ModalitiesSkeleton />
      ) : (
        <div className="space-y-4">
          <Label className="text-sm font-medium text-foreground">
            MODALIDADES
          </Label>
          <div className={cn(
            "grid grid-cols-1 md:grid-cols-2 gap-3 p-3 rounded-lg border",
            modalidadesError || errors.modalidades
              ? "border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/10"
              : "border-border"
          )}>
            {modalidadesDisponiveis.map((modalidade) => (
              <div key={modalidade.id} className="flex items-center space-x-2">
                <Checkbox
                  id={modalidade.id}
                  checked={selectedModalidades?.includes(modalidade.id) || false}
                  onCheckedChange={(checked) =>
                    handleModalidadeChange(modalidade.id, checked as boolean)
                  }
                  className={cn(
                    modalidadesError || errors.modalidades
                      ? "border-red-300 dark:border-red-600"
                      : ""
                  )}
                />
                <Label
                  htmlFor={modalidade.id}
                  className={cn(
                    "text-sm cursor-pointer",
                    modalidadesError || errors.modalidades
                      ? "text-red-700 dark:text-red-300"
                      : ""
                  )}
                >
                  {modalidade.nome}
                </Label>
              </div>
            ))}
          </div>
          {/* Texto removido pois opção "Todas as Sessões" não existe mais */}
          {modalidadesError && (
            <p className="text-sm text-red-600 font-medium">{modalidadesError}</p>
          )}
          {errors.modalidades && (
            <p className="text-sm text-red-600 font-medium">{errors.modalidades.message}</p>
          )}
        </div>
      )}

      {/* Configurações Adicionais removidas */}

      {/* Resumo da Configuração */}
      <div className="p-4 bg-muted rounded-lg border-l-4 border-blue-500 dark:border-blue-400">
        <h4 className="text-sm font-medium text-foreground mb-2">Resumo da Configuração</h4>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>
            <strong>Acesso:</strong> {selectedFrequencia === 'unlimited' ? 'Ilimitado' : `${watch('quantidade')} ${getQuantidadeLabel()}`}
          </p>
          <p>
            <strong>Capacidade:</strong> {selectedCapacidade === 'unlimited' ? 'Ilimitada' : `Limitada a ${watch('capacidadeMaxima') || 0} pessoas`}
          </p>
          <p>
            <strong>Modalidades:</strong> {
              selectedModalidades?.length > 0 
                ? modalidadesDisponiveis
                    .filter(m => selectedModalidades.includes(m.id))
                    .map(m => m.nome)
                    .join(', ')
                : 'Nenhuma selecionada'
            }
          </p>
        </div>
      </div>
    </div>
  )
} 