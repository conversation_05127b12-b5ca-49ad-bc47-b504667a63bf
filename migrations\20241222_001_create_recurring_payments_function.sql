-- Migration: Create function to generate recurring payments
-- Purpose: Create multiple recurring payment records when a membership is created
-- Date: 2024-12-22

CREATE OR REPLACE FUNCTION create_recurring_payments(
  p_membership_id UUID,
  p_payments_count INTEGER DEFAULT 3
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
DECLARE
  v_tenant_id UUID;
  v_student_id UUID;
  v_plan_title TEXT;
  v_plan_amount NUMERIC;
  v_pricing_type TEXT;
  v_frequency TEXT;
  v_currency TEXT;
  v_next_billing_date DATE;
  v_current_date DATE;
  v_payment_id UUID;
  v_created_payments JSONB[] := '{}';
  v_i INTEGER;
  v_result JSONB;
BEGIN
  -- Validações básicas
  IF p_membership_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'ID da matrícula é obrigatório'
    );
  END IF;

  IF p_payments_count IS NULL OR p_payments_count <= 0 THEN
    p_payments_count := 3; -- Default: criar 3 pagamentos
  END IF;

  -- Buscar informações da membership e plano
  SELECT 
    m.tenant_id,
    m.student_id,
    m.next_billing_date,
    p.title,
    p.pricing_config->>'type',
    COALESCE((p.pricing_config->>'amount')::NUMERIC, 0),
    COALESCE(p.pricing_config->>'frequency', 'monthly'),
    COALESCE(p.pricing_config->>'currency', 'BRL')
  INTO v_tenant_id, v_student_id, v_next_billing_date, v_plan_title, v_pricing_type, v_plan_amount, v_frequency, v_currency
  FROM public.memberships m
  JOIN public.plans p ON p.id = m.plan_id
  WHERE m.id = p_membership_id;

  -- Verificar se a membership foi encontrada
  IF v_tenant_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Matrícula não encontrada'
    );
  END IF;

  -- Verificar se é um plano recorrente
  IF v_pricing_type != 'recurring' THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Esta função é apenas para planos recorrentes'
    );
  END IF;

  -- Verificar se já existem pagamentos para esta membership
  IF EXISTS (
    SELECT 1 FROM public.payments 
    WHERE membership_id = p_membership_id 
      AND payment_type = 'recurring'
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Já existem pagamentos recorrentes para esta matrícula'
    );
  END IF;

  -- Usar next_billing_date como ponto de partida, ou data atual se não definida
  v_current_date := COALESCE(v_next_billing_date, CURRENT_DATE);

  -- Criar os pagamentos recorrentes
  FOR v_i IN 0..(p_payments_count - 1) LOOP
    -- Calcular a data de vencimento baseada na frequência
    CASE v_frequency
      WHEN 'weekly' THEN
        v_current_date := v_current_date + (v_i * INTERVAL '1 week');
      WHEN 'monthly' THEN
        v_current_date := v_current_date + (v_i * INTERVAL '1 month');
      WHEN 'yearly' THEN
        v_current_date := v_current_date + (v_i * INTERVAL '1 year');
      ELSE
        -- Default para monthly
        v_current_date := v_current_date + (v_i * INTERVAL '1 month');
    END CASE;

    -- Criar o pagamento
    INSERT INTO public.payments (
      id,
      tenant_id,
      student_id,
      membership_id,
      amount,
      currency,
      status,
      payment_type,
      description,
      due_date,
      billing_cycle,
      metadata,
      created_at
    ) VALUES (
      gen_random_uuid(),
      v_tenant_id,
      v_student_id,
      p_membership_id,
      v_plan_amount,
      v_currency,
      'pending',
      'recurring',
      'Mensalidade - ' || v_plan_title,
      v_current_date,
      v_frequency,
      jsonb_build_object(
        'membership_id', p_membership_id,
        'plan_title', v_plan_title,
        'billing_cycle', v_frequency,
        'payment_sequence', v_i + 1,
        'created_at', NOW()
      ),
      NOW()
    ) RETURNING id INTO v_payment_id;

    -- Adicionar à lista de pagamentos criados
    v_created_payments := array_append(v_created_payments, jsonb_build_object(
      'payment_id', v_payment_id,
      'due_date', v_current_date,
      'amount', v_plan_amount,
      'sequence', v_i + 1
    ));

    -- Reset da data para o próximo cálculo
    v_current_date := COALESCE(v_next_billing_date, CURRENT_DATE);
  END LOOP;

  -- Retornar resultado
  RETURN jsonb_build_object(
    'success', true,
    'data', jsonb_build_object(
      'membership_id', p_membership_id,
      'payments_created', array_length(v_created_payments, 1),
      'total_amount', v_plan_amount * p_payments_count,
      'frequency', v_frequency,
      'plan_title', v_plan_title,
      'payments', v_created_payments
    )
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Erro interno: ' || SQLERRM
    );
END;
$$;

-- Adicionar comentário na função
COMMENT ON FUNCTION create_recurring_payments(UUID, INTEGER) IS 'Cria múltiplos pagamentos recorrentes para uma matrícula específica';
