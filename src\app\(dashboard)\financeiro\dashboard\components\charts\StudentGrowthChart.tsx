"use client";

/**
 * Gráfico de Crescimento de Alunos
 * Exibe a evolução do número de alunos ativos ao longo do tempo
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, ComposedChart } from 'recharts';
import { TrendingUp, Users, Calendar, Activity } from 'lucide-react';
import { cn } from '@/lib/utils';

import { formatNumber } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface StudentGrowthData {
  month: string;
  activeStudents: number;
  newStudents: number;
  churnedStudents: number;
  totalStudents: number;
}

interface StudentGrowthChartProps {
  data?: StudentGrowthData[];
  loading?: boolean;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          {label}
        </p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            <span className="font-medium">{entry.name}: </span>
            {formatNumber(entry.value)}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// ============================================================================
// DADOS MOCK PARA DEMONSTRAÇÃO
// ============================================================================

const mockData: StudentGrowthData[] = [
  { month: 'Jan', activeStudents: 45, newStudents: 8, churnedStudents: 2, totalStudents: 47 },
  { month: 'Fev', activeStudents: 52, newStudents: 9, churnedStudents: 2, totalStudents: 54 },
  { month: 'Mar', activeStudents: 58, newStudents: 7, churnedStudents: 1, totalStudents: 60 },
  { month: 'Abr', activeStudents: 61, newStudents: 5, churnedStudents: 2, totalStudents: 64 },
  { month: 'Mai', activeStudents: 64, newStudents: 6, churnedStudents: 3, totalStudents: 67 },
  { month: 'Jun', activeStudents: 62, newStudents: 4, churnedStudents: 6, totalStudents: 65 },
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const StudentGrowthChart: React.FC<StudentGrowthChartProps> = ({
  data = mockData,
  loading = false,
  className = ''
}) => {
  const [chartData, setChartData] = useState<StudentGrowthData[]>([]);

  useEffect(() => {
    if (data) {
      setChartData(data);
    }
  }, [data]);

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Evolução de Alunos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const latestData = chartData[chartData.length - 1];
  const previousData = chartData[chartData.length - 2];
  const growth = latestData && previousData 
    ? ((latestData.activeStudents - previousData.activeStudents) / previousData.activeStudents) * 100
    : 0;

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Evolução de Alunos
          </div>
          <div className="flex items-center gap-2 text-sm">
            {growth > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
            )}
            <span className={cn(
              "font-medium",
              growth > 0 ? "text-green-600" : "text-red-600"
            )}>
              {growth > 0 ? '+' : ''}{growth.toFixed(1)}%
            </span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <defs>
                <linearGradient id="activeStudentsGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="totalStudentsGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.2}/>
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="month"
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <YAxis
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />

              <Area
                type="monotone"
                dataKey="totalStudents"
                stroke="#10b981"
                strokeWidth={2}
                fill="url(#totalStudentsGradient)"
                name="Total de Alunos"
              />
              <Area
                type="monotone"
                dataKey="activeStudents"
                stroke="#3b82f6"
                strokeWidth={3}
                fill="url(#activeStudentsGradient)"
                name="Alunos Ativos"
              />
              <Line
                type="monotone"
                dataKey="churnedStudents"
                stroke="#ef4444"
                strokeWidth={2}
                dot={{ fill: "#ef4444", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#ef4444", strokeWidth: 2 }}
                name="Cancelamentos"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        {/* Estatísticas Resumidas */}
        <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">Alunos Ativos</div>
            <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {latestData ? formatNumber(latestData.activeStudents) : '-'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">Novos Alunos</div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              {latestData ? formatNumber(latestData.newStudents) : '-'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">Cancelamentos</div>
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              {latestData ? formatNumber(latestData.churnedStudents) : '-'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
