'use server';

import { createClient } from '@/services/supabase/server';
import { revalidatePath } from 'next/cache';
import { nowInBrasilia, utcToBrasilia } from '@/utils/timezone-utils';

/**
 * Calcula o período atual baseado na frequência do plano
 */
function getCurrentPeriodRange(frequency: string, frequencyNumber: number) {
  // Usar a função utilitária para obter a data atual no timezone do Brasil
  const brazilNow = nowInBrasilia();

  if (frequency === 'month') {
    if (frequencyNumber === 1) {
      // Mensal - período atual é o mês atual
      const startOfMonth = new Date(brazilNow.getFullYear(), brazilNow.getMonth(), 1);
      const endOfMonth = new Date(brazilNow.getFullYear(), brazilNow.getMonth() + 1, 0);
      return { start: startOfMonth, end: endOfMonth };
    } else {
      // Trimestral ou outros - últimos N meses
      const startDate = new Date(brazilNow.getFullYear(), brazilNow.getMonth() - (frequencyNumber - 1), 1);
      const endDate = new Date(brazilNow.getFullYear(), brazilNow.getMonth() + 1, 0);
      return { start: startDate, end: endDate };
    }
  } else if (frequency === 'year') {
    // Anual - período atual é o ano atual
    const startOfYear = new Date(brazilNow.getFullYear(), 0, 1);
    const endOfYear = new Date(brazilNow.getFullYear(), 11, 31);
    return { start: startOfYear, end: endOfYear };
  }

  // Default para mensal
  const startOfMonth = new Date(brazilNow.getFullYear(), brazilNow.getMonth(), 1);
  const endOfMonth = new Date(brazilNow.getFullYear(), brazilNow.getMonth() + 1, 0);
  return { start: startOfMonth, end: endOfMonth };
}

export interface AssinaturaRecorrente {
  id: string;
  membership_id: string;
  student_id: string; // Este é o user_id da tabela users, não o id da tabela students
  student_name: string;
  student_email: string;
  student_avatar?: string;
  plan_title: string;
  plan_type: 'individual' | 'family' | 'corporate';
  pricing_type?: 'recurring' | 'one-time' | 'per-session' | 'trial';
  amount: number;
  frequency: string;
  frequency_number: number;
  status: 'active' | 'paused' | 'canceled' | 'expired';
  start_date: string;
  end_date?: string;
  next_billing_date?: string;
  branch_name?: string;
  created_at: string;
  last_payment_status?: 'pending' | 'paid' | 'failed' | 'overdue';
  last_payment_date?: string;
  last_payment_paid_at?: string;
  last_payment_overdue_date?: string;
  next_payment_due?: string;
  failed_attempts?: number;
}

export interface AssinaturasResult {
  success: boolean;
  data?: AssinaturaRecorrente[];
  errors?: {
    _form?: string;
  };
}

export async function getAssinaturasRecorrentes(): Promise<AssinaturasResult> {
  try {
    const supabase = await createClient();

    // Buscar todas as assinaturas ativas e pausadas
    const { data: memberships, error: membershipError } = await supabase
      .from('memberships')
      .select(`
        id,
        status,
        start_date,
        end_date,
        next_billing_date,
        created_at,
        student_id,
        plan_id
      `)
      .in('status', ['active', 'paused'])
      .order('created_at', { ascending: false });

    if (membershipError) {
      console.error('Erro ao buscar memberships:', membershipError);
      return {
        success: false,
        errors: {
          _form: 'Erro ao buscar assinaturas'
        }
      };
    }

    if (!memberships || memberships.length === 0) {
      return {
        success: true,
        data: []
      };
    }

    // Buscar planos recorrentes e únicos
    const planIds = Array.from(new Set(memberships.map((m: any) => m.plan_id)));
    const { data: plans, error: plansError } = await supabase
      .from('plans')
      .select('id, title, plan_type, pricing_config')
      .in('id', planIds)
      .or('pricing_config->>type.eq.recurring,pricing_config->>type.eq.one-time,pricing_config->>tipo.eq.recurring,pricing_config->>tipo.eq.one-time');

    if (plansError) {
      console.error('Erro ao buscar planos:', plansError);
      return {
        success: false,
        errors: {
          _form: 'Erro ao buscar planos'
        }
      };
    }

    // Filtrar apenas memberships com planos recorrentes ou únicos
    const validPlanIds = new Set(plans?.map((p: any) => p.id) || []);
    const validMemberships = memberships.filter((m: any) => validPlanIds.has(m.plan_id));

    if (validMemberships.length === 0) {
      return {
        success: true,
        data: []
      };
    }

    // Buscar dados dos estudantes
    const studentIds = Array.from(new Set(validMemberships.map((m: any) => m.student_id)));
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select(`
        id,
        user_id,
        branch_id,
        users!students_user_id_fkey (
          first_name,
          last_name,
          email,
          avatar_url
        ),
        branches (
          name
        )
      `)
      .in('id', studentIds);

    if (studentsError) {
      console.error('Erro ao buscar estudantes:', studentsError);
      return {
        success: false,
        errors: {
          _form: 'Erro ao buscar dados dos estudantes'
        }
      };
    }

    if (membershipError) {
      console.error('Erro ao buscar assinaturas:', membershipError);
      return {
        success: false,
        errors: {
          _form: 'Erro ao buscar assinaturas recorrentes'
        }
      };
    }

    // Buscar informações de pagamentos para cada membership
    const membershipIds = validMemberships.map((m: any) => m.id);

    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('membership_id, status, due_date, paid_at, attempt_count, overdue_date')
      .in('membership_id', membershipIds)
      .order('due_date', { ascending: false });

    if (paymentsError) {
      console.error('Erro ao buscar pagamentos:', paymentsError);
    }

    // Criar mapas para facilitar o lookup
    const plansMap = new Map(plans?.map((p: any) => [p.id, p]) || []);
    const studentsMap = new Map(students?.map((s: any) => [s.id, s]) || []);

    // Processar dados para o formato esperado
    const assinaturas: AssinaturaRecorrente[] = validMemberships.map((membership: any) => {
      // Buscar dados relacionados usando os mapas
      const plan = plansMap.get(membership.plan_id);
      const studentData = studentsMap.get(membership.student_id);

      // Extrair dados do estudante (lidando com arrays do Supabase)
      const user = Array.isArray(studentData?.users) ? studentData.users[0] : studentData?.users;
      const branch = Array.isArray(studentData?.branches) ? studentData.branches[0] : studentData?.branches;

      // Extrair configurações do plano
      const pricingConfig = plan?.pricing_config as any;
      const planType = pricingConfig?.type || pricingConfig?.tipo || 'recurring';

      // Para planos únicos e recorrentes, tentar todos os campos possíveis e converter para número
      const rawAmount = pricingConfig?.amount || pricingConfig?.valor || pricingConfig?.custo || pricingConfig?.cost || 0;
      const amount = typeof rawAmount === 'string' ? parseFloat(rawAmount) : Number(rawAmount) || 0;

      const frequency = pricingConfig?.frequency || pricingConfig?.frequencia || 'month';
      const frequencyNumber = pricingConfig?.frequency_number || pricingConfig?.numeroFrequencia || 1;

      // Para planos únicos, não calcular período atual
      const currentPeriod = planType === 'one-time'
        ? null
        : getCurrentPeriodRange(frequency, frequencyNumber);

      // Encontrar pagamentos desta membership
      const membershipPayments = payments?.filter((p: any) => p.membership_id === membership.id) || [];

      // Buscar pagamento do período atual (apenas para planos recorrentes)
      let currentPeriodPayment = null;
      if (currentPeriod && planType === 'recurring') {
        currentPeriodPayment = membershipPayments.find((p: any) => {
          // Converter a data de vencimento para timezone do Brasil para comparação correta
          const dueDate = utcToBrasilia(new Date(p.due_date));
          return dueDate >= currentPeriod.start && dueDate <= currentPeriod.end;
        });
      }

      // Se não há pagamento do período atual, buscar o mais recente
      const lastPayment = currentPeriodPayment || membershipPayments[0];

      // Contar tentativas falhadas
      const failedAttempts = membershipPayments.filter((p: any) => p.status === 'failed').length;

      // Determinar status do último pagamento
      let lastPaymentStatus: 'pending' | 'paid' | 'failed' | 'overdue' | undefined;
      if (lastPayment) {
        if (lastPayment.status === 'paid') {
          lastPaymentStatus = 'paid';
        } else if (lastPayment.status === 'failed') {
          lastPaymentStatus = 'failed';
        } else if (lastPayment.status === 'overdue') {
          lastPaymentStatus = 'overdue';
        } else if (lastPayment.status === 'pending') {
          // Só considerar vencido se o status no banco for 'overdue' ou se tiver overdue_date preenchido
          if (lastPayment.overdue_date) {
            lastPaymentStatus = 'overdue';
          } else {
            lastPaymentStatus = 'pending';
          }
        }
      }

      // Determinar próximo vencimento baseado no tipo de plano
      let nextPaymentDue: string | undefined;

      if (planType === 'one-time') {
        // Para planos únicos, se há pagamento pendente, mostrar a data de vencimento
        // Se já foi pago, não há próximo vencimento
        if (lastPayment && lastPayment.status !== 'paid') {
          nextPaymentDue = lastPayment.due_date;
        }
        // Se não há pagamento ou já foi pago, nextPaymentDue fica undefined
      } else {
        // Lógica para planos recorrentes
        if (currentPeriodPayment) {
          if (currentPeriodPayment.status === 'paid') {
            // Se o pagamento do período atual está pago, calcular próximo período
            const nextDueDate = utcToBrasilia(new Date(currentPeriodPayment.due_date));
            if (frequency === 'month') {
              nextDueDate.setMonth(nextDueDate.getMonth() + frequencyNumber);
            } else if (frequency === 'year') {
              nextDueDate.setFullYear(nextDueDate.getFullYear() + frequencyNumber);
            }
            nextPaymentDue = nextDueDate.toISOString().split('T')[0];
          } else {
            // Se não está pago, o próximo vencimento é a própria due_date
            nextPaymentDue = currentPeriodPayment.due_date;
          }
        } else {
          // Se não há pagamento do período atual, usar next_billing_date da membership
          nextPaymentDue = membership.next_billing_date;
        }
      }

      return {
        id: `${membership.id}-${studentData?.id}`,
        membership_id: membership.id,
        student_id: studentData?.user_id || '',
        student_name: `${user?.first_name || ''} ${user?.last_name || ''}`.trim(),
        student_email: user?.email || '',
        student_avatar: user?.avatar_url,
        plan_title: plan?.title || '',
        plan_type: plan?.plan_type || 'individual',
        pricing_type: planType,
        amount,
        frequency,
        frequency_number: frequencyNumber,
        status: membership.status,
        start_date: membership.start_date,
        end_date: membership.end_date,
        next_billing_date: membership.next_billing_date,
        branch_name: branch?.name,
        created_at: membership.created_at,
        last_payment_status: lastPaymentStatus,
        last_payment_date: lastPayment?.paid_at,
        last_payment_paid_at: lastPayment?.paid_at,
        last_payment_overdue_date: lastPayment?.overdue_date,
        next_payment_due: nextPaymentDue,
        failed_attempts: failedAttempts > 0 ? failedAttempts : undefined,
      };
    });

    // Revalidar a página para garantir dados atualizados
    revalidatePath('/financeiro/recorrentes');

    return {
      success: true,
      data: assinaturas
    };

  } catch (error) {
    console.error('Erro ao buscar assinaturas recorrentes:', error);
    return {
      success: false,
      errors: {
        _form: 'Erro interno do servidor'
      }
    };
  }
}

export async function pausarAssinatura(membershipId: string): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = await createClient();

    const { error } = await supabase
      .from('memberships')
      .update({ 
        status: 'paused',
        updated_at: new Date().toISOString()
      })
      .eq('id', membershipId);

    if (error) {
      console.error('Erro ao pausar assinatura:', error);
      return {
        success: false,
        message: 'Erro ao pausar assinatura'
      };
    }

    revalidatePath('/financeiro/recorrentes');
    
    return {
      success: true,
      message: 'Assinatura pausada com sucesso'
    };

  } catch (error) {
    console.error('Erro ao pausar assinatura:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
}

export async function retomarAssinatura(membershipId: string): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = await createClient();

    const { error } = await supabase
      .from('memberships')
      .update({ 
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('id', membershipId);

    if (error) {
      console.error('Erro ao retomar assinatura:', error);
      return {
        success: false,
        message: 'Erro ao retomar assinatura'
      };
    }

    revalidatePath('/financeiro/recorrentes');
    
    return {
      success: true,
      message: 'Assinatura retomada com sucesso'
    };

  } catch (error) {
    console.error('Erro ao retomar assinatura:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
}

export async function tentarCobrancaNovamente(membershipId: string): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = await createClient();

    // Buscar pagamentos pendentes ou falhados desta membership
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('id, status, attempt_count')
      .eq('membership_id', membershipId)
      .in('status', ['pending', 'failed'])
      .order('due_date', { ascending: true })
      .limit(1);

    if (paymentsError || !payments || payments.length === 0) {
      return {
        success: false,
        message: 'Nenhum pagamento pendente encontrado'
      };
    }

    const payment = payments[0];
    
    // Atualizar o pagamento para tentar novamente
    const { error: updateError } = await supabase
      .from('payments')
      .update({
        status: 'pending',
        attempt_count: (payment.attempt_count || 0) + 1,
        last_attempt_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', payment.id);

    if (updateError) {
      console.error('Erro ao atualizar pagamento:', updateError);
      return {
        success: false,
        message: 'Erro ao processar nova tentativa de cobrança'
      };
    }

    revalidatePath('/financeiro/recorrentes');
    
    return {
      success: true,
      message: 'Nova tentativa de cobrança iniciada'
    };

  } catch (error) {
    console.error('Erro ao tentar cobrança novamente:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
}

export async function cancelarAssinatura(membershipId: string): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = await createClient();

    const { error } = await supabase
      .from('memberships')
      .update({ 
        status: 'canceled',
        updated_at: new Date().toISOString()
      })
      .eq('id', membershipId);

    if (error) {
      console.error('Erro ao cancelar assinatura:', error);
      return {
        success: false,
        message: 'Erro ao cancelar assinatura'
      };
    }

    revalidatePath('/financeiro/recorrentes');
    
    return {
      success: true,
      message: 'Assinatura cancelada com sucesso'
    };

  } catch (error) {
    console.error('Erro ao cancelar assinatura:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
}

// Ações em massa
export async function executarAcaoEmMassa(
  action: string, 
  userIds: string[]
): Promise<{ success: boolean; message: string; results?: any[] }> {
  try {
    const supabase = await createClient();

    // Buscar memberships dos usuários selecionados
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select('id, user_id')
      .in('user_id', userIds);

    if (studentsError || !students) {
      return {
        success: false,
        message: 'Erro ao buscar dados dos estudantes'
      };
    }

    const studentIds = students.map(s => s.id);

    // Buscar memberships ativas/pausadas destes estudantes
    const { data: memberships, error: membershipsError } = await supabase
      .from('memberships')
      .select('id, student_id, status')
      .in('student_id', studentIds)
      .in('status', ['active', 'paused']);

    if (membershipsError || !memberships) {
      return {
        success: false,
        message: 'Erro ao buscar assinaturas'
      };
    }

    if (memberships.length === 0) {
      return {
        success: false,
        message: 'Nenhuma assinatura ativa ou pausada encontrada para os usuários selecionados'
      };
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Executar ação baseada no tipo
    switch (action) {
      case 'pause-all':
        for (const membership of memberships) {
          if (membership.status === 'active') {
            const result = await pausarAssinatura(membership.id);
            results.push({ membershipId: membership.id, ...result });
            if (result.success) successCount++;
            else errorCount++;
          }
        }
        break;

      case 'resume-all':
        for (const membership of memberships) {
          if (membership.status === 'paused') {
            const result = await retomarAssinatura(membership.id);
            results.push({ membershipId: membership.id, ...result });
            if (result.success) successCount++;
            else errorCount++;
          }
        }
        break;

      case 'cancel-all':
        for (const membership of memberships) {
          const result = await cancelarAssinatura(membership.id);
          results.push({ membershipId: membership.id, ...result });
          if (result.success) successCount++;
          else errorCount++;
        }
        break;

      case 'retry-payments':
        for (const membership of memberships) {
          const result = await tentarCobrancaNovamente(membership.id);
          results.push({ membershipId: membership.id, ...result });
          if (result.success) successCount++;
          else errorCount++;
        }
        break;

      default:
        return {
          success: false,
          message: 'Ação não reconhecida'
        };
    }

    // Preparar mensagem de resultado
    let message = '';
    if (successCount > 0 && errorCount === 0) {
      message = `Ação executada com sucesso em ${successCount} assinatura${successCount > 1 ? 's' : ''}`;
    } else if (successCount > 0 && errorCount > 0) {
      message = `Ação executada em ${successCount} assinatura${successCount > 1 ? 's' : ''}, ${errorCount} falharam`;
    } else {
      message = `Falha ao executar ação em todas as assinaturas`;
    }

    return {
      success: successCount > 0,
      message,
      results
    };

  } catch (error) {
    console.error('Erro ao executar ação em massa:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
}
