import { NextResponse } from 'next/server'
import { getTenantData } from '@/services/tenant/tenant-service'
import { getSupabaseConfig } from '@/config/supabase'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/services/supabase/types/database.types'

export async function GET(
  _request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params

  if (!slug) {
    return NextResponse.json({}, { status: 400 })
  }

  try {
    // Dados de tema, cores, logo, etc.
    const tenantData = await getTenantData(slug)
    if (!tenantData) {
      return NextResponse.json({}, { status: 404 })
    }

    // ID do tenant
    const config = getSupabaseConfig()
    const supabase = createClient<Database>(config.url, config.anonKey)
    const { data: idRow } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', slug)
      .single()

    return NextResponse.json({
      id: idRow?.id ?? null,
      name: tenantData.name,
      primary_color: tenantData.primary_color,
      secondary_color: tenantData.secondary_color,
      logo_url: tenantData.logo_url,
      description: tenantData.description ?? null
    })
  } catch (error) {
    console.error('Erro na API tenant root:', error)
    return NextResponse.json({}, { status: 500 })
  }
}