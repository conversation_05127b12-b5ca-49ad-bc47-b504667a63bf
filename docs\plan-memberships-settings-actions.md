# Server Actions - Sistema de Memberships e Planos

Este documento explica como usar as server actions criadas para o novo sistema de memberships e planos.

## 📋 Visão Geral

As server actions foram organizadas em três categorias principais:

1. **Plan Actions** - Gerenciamento de planos
2. **Membership Actions** - Gerenciamento de matrículas
3. **Settings Actions** - Configurações do tenant

## 🏗️ Estrutura de Arquivos

```
actions/
├── plan-actions.ts              # Actions de planos
├── membership-actions.ts        # Actions de matrículas
├── settings-actions.ts          # Actions de configurações
├── index.ts                     # Exportações centralizadas
└── README.md                    # Esta documentação
```

## 🔧 Plan Actions

### `createPlan(data)`
Cria um novo plano em status `draft`.

```typescript
import { createPlan } from '@/app/(dashboard)/academia/actions';

const result = await createPlan({
  title: 'Plano Premium',
  planType: 'individual',
  pricingConfig: {
    type: 'recurring',
    amount: 200,
    currency: 'BRL',
    frequencyUnit: 'month',
    frequencyCount: 1,
  },
  durationConfig: {
    type: 'ongoing',
    autoRenew: true,
  },
  accessConfig: {
    frequency: 'unlimited',
    capacity: 'unlimited',
    modalities: ['jiu-jitsu', 'muay-thai'],
  },
});

if (result.success) {
  console.log('Plano criado:', result.data);
} else {
  console.error('Erros:', result.errors);
}
```

### `publishPlan(data)`
Publica uma versão do plano, tornando-a ativa.

```typescript
await publishPlan({ planId: 'uuid-do-plano' });
```

### `duplicatePlan(data)`
Cria uma nova versão de um plano existente.

```typescript
await duplicatePlan({ planId: 'uuid-do-plano' });
```

### `getActivePlans()`
Lista todos os planos ativos do tenant.

```typescript
const result = await getActivePlans();
if (result.success) {
  const plans = result.data;
}
```

### `validatePlanConfig(data)`
Valida uma configuração de plano antes de salvar.

```typescript
const validation = await validatePlanConfig({
  pricingConfig: { /* config */ },
  durationConfig: { /* config */ },
  accessConfig: { /* config */ },
});
```

## 👥 Membership Actions

### `createMembership(data)`
Cria uma nova matrícula para um estudante.

```typescript
import { createMembership } from '@/app/(dashboard)/academia/actions';

const result = await createMembership({
  studentId: 'uuid-do-estudante',
  planId: 'uuid-do-plano',
  startDate: '2024-01-15', // Opcional, padrão é hoje
  metadata: { source: 'web' }, // Opcional
});
```

### `updateMembershipStatus(data)`
Atualiza o status de uma matrícula.

```typescript
await updateMembershipStatus({
  membershipId: 'uuid-da-matricula',
  newStatus: 'paused',
  reason: 'Solicitação do aluno',
});
```

### `pauseMembership(data)`
Pausa uma matrícula específica.

```typescript
await pauseMembership({
  membershipId: 'uuid-da-matricula',
  reason: 'Viagem de trabalho',
  metadata: { pauseType: 'temporary' },
});
```

### `getStudentActiveMemberships(data)`
Busca matrículas ativas de um estudante.

```typescript
const result = await getStudentActiveMemberships({
  studentId: 'uuid-do-estudante',
});
```

### `calculatePrice(data)`
Calcula o preço de um plano em uma data específica.

```typescript
const result = await calculatePrice({
  planId: 'uuid-do-plano',
  calculationDate: '2024-01-15', // Opcional
});
```

### `processMembershipBilling(data)`
Processa a cobrança de uma matrícula.

```typescript
await processMembershipBilling({
  membershipId: 'uuid-da-matricula',
});
```

## 📊 Analytics Actions

### `getMembershipOverview(data)`
Busca visão geral das matrículas em um período.

```typescript
const result = await getMembershipOverview({
  startDate: '2024-01-01',
  endDate: '2024-12-31',
});
```

### `getMembershipStatistics(data)`
Busca estatísticas detalhadas das matrículas.

```typescript
const result = await getMembershipStatistics({
  startDate: '2024-01-01',
  endDate: '2024-12-31',
});
```

### `getPlanUsageAnalytics()`
Busca analytics de uso dos planos.

```typescript
const result = await getPlanUsageAnalytics();
```

## ⚙️ Settings Actions

### `setTenantSetting(data)`
Define uma configuração específica do tenant.

```typescript
await setTenantSetting({
  settingKey: 'custom_setting',
  settingValue: { enabled: true, value: 'teste' },
});
```

### `setMultipleMembershipsAllowed(data)`
Configura se múltiplas matrículas são permitidas.

```typescript
await setMultipleMembershipsAllowed({
  allowMultipleMemberships: true,
});
```

### `getTenantSettings()`
Busca todas as configurações do tenant.

```typescript
const result = await getTenantSettings();
```

## 🛡️ Validação e Tratamento de Erros

Todas as actions seguem o mesmo padrão de retorno:

```typescript
interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}
```

### Exemplo de Tratamento

```typescript
const result = await createPlan(planData);

if (result.success) {
  // Sucesso
  console.log('Dados:', result.data);
  
  // Redirecionar ou atualizar UI
  router.push('/academia/planos');
} else {
  // Erro
  if (result.errors?._form) {
    // Erro geral do formulário
    toast.error(result.errors._form);
  } else {
    // Erros específicos de campos
    Object.entries(result.errors || {}).forEach(([field, message]) => {
      console.error(`Campo ${field}: ${message}`);
    });
  }
}
```

## 🎯 Uso em Componentes React

### Com useFormStatus (formulários)

```typescript
'use client';

import { useFormStatus } from 'react-dom';
import { createPlan } from '@/app/(dashboard)/academia/actions';

function PlanForm() {
  const { pending } = useFormStatus();
  
  async function handleSubmit(formData: FormData) {
    const planData = {
      title: formData.get('title'),
      // ... outros campos
    };
    
    const result = await createPlan(planData);
    
    if (!result.success) {
      // Tratar erros
    }
  }
  
  return (
    <form action={handleSubmit}>
      {/* campos do formulário */}
      <button type="submit" disabled={pending}>
        {pending ? 'Criando...' : 'Criar Plano'}
      </button>
    </form>
  );
}
```

### Com useState (controle manual)

```typescript
'use client';

import { useState } from 'react';
import { createMembership } from '@/app/(dashboard)/academia/actions';

function CreateMembershipButton({ studentId, planId }) {
  const [isLoading, setIsLoading] = useState(false);
  
  const handleCreateMembership = async () => {
    setIsLoading(true);
    
    try {
      const result = await createMembership({
        studentId,
        planId,
      });
      
      if (result.success) {
        toast.success('Matrícula criada com sucesso!');
      } else {
        toast.error(result.errors?._form || 'Erro ao criar matrícula');
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <button 
      onClick={handleCreateMembership}
      disabled={isLoading}
    >
      {isLoading ? 'Criando...' : 'Criar Matrícula'}
    </button>
  );
}
```

## 🔄 Cache e Revalidação

As actions automaticamente revalidam os caches das páginas relacionadas:

- **Plan actions**: Revalidam `/academia`
- **Membership actions**: Revalidam `/alunos` e `/academia`
- **Settings actions**: Revalidam `/academia/configuracoes`
- **Billing actions**: Revalidam `/financeiro`

## 🚀 Próximos Passos

1. **Implementar no Frontend**: Integrar essas actions nos componentes React
2. **Testes**: Criar testes unitários para cada action
3. **Documentação de API**: Documentar retornos das funções RPC
4. **Monitoramento**: Adicionar logs e métricas de performance

## 📞 Suporte

Para dúvidas sobre as server actions ou problemas na implementação, consulte:

1. Documentação das funções RPC em `/migrations/README_RPC_FUNCTIONS_IMPLEMENTED.md`
2. Documentação da migração em `/docs/migracao_memberships.md`
3. Issues no repositório do projeto 