'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import type { 
  NotificationFilters as NotificationFiltersType,
  NotificationType,
  NotificationCategory,
  NotificationPriority
} from '@/services/notifications/types/notification-types';

interface NotificationFiltersProps {
  filters: NotificationFiltersType;
  onFiltersChange: (filters: NotificationFiltersType) => void;
}

const notificationTypes: { value: NotificationType; label: string }[] = [
  { value: 'payment', label: 'Pagamento' },
  { value: 'class', label: 'Aula' },
  { value: 'system', label: 'Sistema' },
  { value: 'enrollment', label: 'Matrícula' },
  { value: 'event', label: 'Evento' },
];

const notificationCategories: { value: NotificationCategory; label: string }[] = [
  { value: 'reminder', label: 'Lembrete' },
  { value: 'alert', label: 'Alerta' },
  { value: 'info', label: 'Informação' },
  { value: 'success', label: 'Sucesso' },
  { value: 'error', label: 'Erro' },
];

const notificationPriorities: { value: NotificationPriority; label: string }[] = [
  { value: 'urgent', label: 'Urgente' },
  { value: 'high', label: 'Alta' },
  { value: 'medium', label: 'Média' },
  { value: 'low', label: 'Baixa' },
];

export function NotificationFilters({ filters, onFiltersChange }: NotificationFiltersProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState(filters.search || '');

  const updateFilter = (key: keyof NotificationFiltersType, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const addToArrayFilter = (key: keyof NotificationFiltersType, value: string) => {
    const currentArray = (filters[key] as string[]) || [];
    if (!currentArray.includes(value)) {
      updateFilter(key, [...currentArray, value]);
    }
  };

  const removeFromArrayFilter = (key: keyof NotificationFiltersType, value: string) => {
    const currentArray = (filters[key] as string[]) || [];
    updateFilter(key, currentArray.filter(item => item !== value));
  };

  const clearFilters = () => {
    setSearchTerm('');
    onFiltersChange({});
  };

  const handleSearch = () => {
    updateFilter('search', searchTerm);
  };

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof NotificationFiltersType];
    return value && (Array.isArray(value) ? value.length > 0 : true);
  });

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Barra de busca e toggle de filtros */}
          <div className="flex items-center gap-2">
            <div className="flex-1 flex gap-2">
              <Input
                placeholder="Buscar notificações..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1"
              />
              <Button onClick={handleSearch}>
                Buscar
              </Button>
            </div>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={cn(showFilters && 'bg-primary/10')}
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filtros
            </Button>

            {hasActiveFilters && (
              <Button variant="outline" onClick={clearFilters}>
                <XMarkIcon className="h-4 w-4 mr-2" />
                Limpar
              </Button>
            )}
          </div>

          {/* Filtros ativos */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2">
              {filters.type?.map((type) => (
                <Badge key={type} variant="secondary" className="gap-1">
                  {notificationTypes.find(t => t.value === type)?.label}
                  <button
                    onClick={() => removeFromArrayFilter('type', type)}
                    className="ml-1 hover:bg-black/20 rounded-full p-0.5"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              
              {filters.category?.map((category) => (
                <Badge key={category} variant="secondary" className="gap-1">
                  {notificationCategories.find(c => c.value === category)?.label}
                  <button
                    onClick={() => removeFromArrayFilter('category', category)}
                    className="ml-1 hover:bg-black/20 rounded-full p-0.5"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              
              {filters.priority?.map((priority) => (
                <Badge key={priority} variant="secondary" className="gap-1">
                  {notificationPriorities.find(p => p.value === priority)?.label}
                  <button
                    onClick={() => removeFromArrayFilter('priority', priority)}
                    className="ml-1 hover:bg-black/20 rounded-full p-0.5"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </Badge>
              ))}

              {filters.dateFrom && (
                <Badge variant="secondary" className="gap-1">
                  A partir de: {format(new Date(filters.dateFrom), 'dd/MM/yyyy')}
                  <button
                    onClick={() => updateFilter('dateFrom', undefined)}
                    className="ml-1 hover:bg-black/20 rounded-full p-0.5"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </Badge>
              )}

              {filters.dateTo && (
                <Badge variant="secondary" className="gap-1">
                  Até: {format(new Date(filters.dateTo), 'dd/MM/yyyy')}
                  <button
                    onClick={() => updateFilter('dateTo', undefined)}
                    className="ml-1 hover:bg-black/20 rounded-full p-0.5"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </Badge>
              )}
            </div>
          )}

          {/* Painel de filtros expandido */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
              {/* Tipo */}
              <div className="space-y-2">
                <Label>Tipo</Label>
                <Select onValueChange={(value) => addToArrayFilter('type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecionar tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Categoria */}
              <div className="space-y-2">
                <Label>Categoria</Label>
                <Select onValueChange={(value) => addToArrayFilter('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecionar categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Prioridade */}
              <div className="space-y-2">
                <Label>Prioridade</Label>
                <Select onValueChange={(value) => addToArrayFilter('priority', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecionar prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationPriorities.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Data */}
              <div className="space-y-2">
                <Label>Período</Label>
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="flex-1">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        {filters.dateFrom ? format(new Date(filters.dateFrom), 'dd/MM') : 'De'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={filters.dateFrom ? new Date(filters.dateFrom) : undefined}
                        onSelect={(date) => updateFilter('dateFrom', date?.toISOString())}
                      />
                    </PopoverContent>
                  </Popover>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="flex-1">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        {filters.dateTo ? format(new Date(filters.dateTo), 'dd/MM') : 'Até'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={filters.dateTo ? new Date(filters.dateTo) : undefined}
                        onSelect={(date) => updateFilter('dateTo', date?.toISOString())}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
