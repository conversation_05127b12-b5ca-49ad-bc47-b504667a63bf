'use client';

import { useState } from 'react';
import { DollarSign } from 'lucide-react';
import { ConfigurationCard } from '../ui/ConfigurationCard';
import { ConfigurationField } from '../ui/ConfigurationField';
import { ReportsModal } from '../modals/ReportsModal';

export function ReportsCard() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleConfigureReports = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <ConfigurationCard
        title="Relatórios"
        icon={DollarSign}
        iconColor="text-purple-600"
        buttonText="Configurar Relatórios"
        onButtonClick={handleConfigureReports}
      >
        <ConfigurationField 
          label="Relatórios Automáticos" 
          value="Mensal via email" 
        />
        <ConfigurationField 
          label="Formato de Exportação" 
          value="PDF e Excel" 
        />
      </ConfigurationCard>

      <ReportsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}