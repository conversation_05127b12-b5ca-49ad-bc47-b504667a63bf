/**
 * Serviço Principal de Cobranças
 * Baseado no documento: docs/planejamento-sistema-cobrancas.md
 */

import { createClient } from '@/services/supabase/server'
import {
  Payment,
  PaymentMetrics,
  PaymentRPCResult,
  CreateManualPaymentData,
  CreateGraduationFeeData,
  CreateSignupFeeData,
  CreateCancellationFeeData,
  UpdatePaymentStatusData,
  GetPaymentsByStudentData,
  GetPaymentsByMembershipData,
  ProcessOverduePaymentsData,
  UpdatePaymentData
} from './payment-types'

/**
 * Classe principal do serviço de cobranças
 * Implementa as funções definidas na FASE 3 do documento
 */
export class PaymentService {
  private supabase: any

  constructor(supabaseClient?: any) {
    this.supabase = supabaseClient
  }

  /**
   * Inicializa o cliente Supabase se não foi fornecido
   */
  private async getSupabaseClient() {
    if (!this.supabase) {
      this.supabase = await createClient()
    }
    return this.supabase
  }

  /**
   * Criar cobrança manual
   * Implementa a função RPC create_manual_payment do documento
   */
  async createManualPayment(data: CreateManualPaymentData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      const { data: result, error } = await supabase.rpc('create_manual_payment', {
        p_student_id: data.alunoId,
        p_amount: data.valor,
        p_description: data.descricao,
        p_due_date: data.dataVencimento || new Date().toISOString().split('T')[0],
        p_metadata: data.metadata || {}
      })

      if (error) {
        return {
          success: false,
          error: `Erro ao criar cobrança manual: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: result.payment_id,
        message: 'Cobrança manual criada com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Criar taxa de graduação
   * Implementa a função RPC create_graduation_fee_payment do documento
   */
  async createGraduationFeePayment(data: CreateGraduationFeeData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      const { data: result, error } = await supabase.rpc('create_graduation_fee_payment', {
        p_student_id: data.alunoId,
        p_belt_level_id: data.beltLevelId,
        p_graduation_id: data.graduationId
      })

      if (error) {
        return {
          success: false,
          error: `Erro ao criar taxa de graduação: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: result.payment_id,
        message: 'Taxa de graduação criada com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Criar taxa de inscrição
   * Implementa a função RPC create_signup_fee_payment do documento
   */
  async createSignupFeePayment(data: CreateSignupFeeData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      const { data: result, error } = await supabase.rpc('create_signup_fee_payment', {
        p_membership_id: data.membershipId
      })

      if (error) {
        return {
          success: false,
          error: `Erro ao criar taxa de inscrição: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: result.payment_id,
        message: 'Taxa de inscrição criada com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Criar pagamento inicial
   * Implementa a função RPC create_initial_payment para planos one-time
   */
  async createInitialPayment(data: CreateSignupFeeData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      const { data: result, error } = await supabase.rpc('create_initial_payment', {
        p_membership_id: data.membershipId
      })

      if (error) {
        return {
          success: false,
          error: `Erro ao criar pagamento inicial: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: result.data?.payment_id,
        message: 'Pagamento inicial criado com sucesso',
        data: result.data
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Criar múltiplos pagamentos recorrentes
   * Implementa a função RPC create_recurring_payments
   */
  async createRecurringPayments(data: { membershipId: string; paymentsCount?: number }): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      const { data: result, error } = await supabase.rpc('create_recurring_payments', {
        p_membership_id: data.membershipId,
        p_payments_count: data.paymentsCount || 1
      })

      if (error) {
        return {
          success: false,
          error: `Erro ao criar pagamentos recorrentes: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: result.data?.payments?.[0]?.payment_id,
        message: 'Pagamentos recorrentes criados com sucesso',
        data: result.data
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Criar taxa de cancelamento
   * Busca o plano associado à membership e cria cobrança baseada na taxa configurada
   */
  async createCancellationFeePayment(data: CreateCancellationFeeData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      // Buscar a membership e o plano associado
      const { data: membership, error: membershipError } = await supabase
        .from('memberships')
        .select(`
          id,
          student_id,
          status,
          plans!inner(
            id,
            title,
            duration_config
          )
        `)
        .eq('id', data.membershipId)
        .single()

      if (membershipError) {
        return {
          success: false,
          error: `Erro ao buscar matrícula: ${membershipError.message}`
        }
      }

      if (!membership) {
        return {
          success: false,
          error: 'Matrícula não encontrada'
        }
      }

      // Verificar se a membership está ativa
      if (membership.status !== 'active') {
        return {
          success: false,
          error: 'Não é possível cobrar taxa de cancelamento de uma matrícula que não está ativa'
        }
      }

      // Extrair a taxa de cancelamento da configuração do plano
      const durationConfig = membership.plans.duration_config as any
      const taxaCancelamento = durationConfig?.taxaCancelamento

      if (!taxaCancelamento || taxaCancelamento <= 0) {
        return {
          success: false,
          error: 'Este plano não possui taxa de cancelamento configurada'
        }
      }

      // Criar o pagamento da taxa de cancelamento
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .insert({
          student_id: membership.student_id,
          membership_id: data.membershipId,
          amount: taxaCancelamento,
          currency: 'BRL',
          status: 'pending',
          payment_type: 'cancellation_fee',
          description: `Taxa de cancelamento - ${membership.plans.title}`,
          due_date: new Date().toISOString().split('T')[0],
          metadata: {
            plan_id: membership.plans.id,
            plan_title: membership.plans.title,
            cancellation_reason: data.motivo || 'Cancelamento solicitado',
            original_cancellation_fee: taxaCancelamento
          },
          attempt_count: 0
        })
        .select()
        .single()

      if (paymentError) {
        return {
          success: false,
          error: `Erro ao criar cobrança da taxa de cancelamento: ${paymentError.message}`
        }
      }

      return {
        success: true,
        payment_id: payment.id,
        message: `Taxa de cancelamento de R$ ${taxaCancelamento.toFixed(2).replace('.', ',')} criada com sucesso`,
        data: {
          payment_id: payment.id,
          amount: taxaCancelamento,
          plan_title: membership.plans.title
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Processar pagamentos em atraso
   * Implementa a função RPC process_overdue_payments do documento
   */
  async processOverduePayments(data?: ProcessOverduePaymentsData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      const { data: result, error } = await supabase.rpc('process_overdue_payments', {
        p_tenant_id: data?.tenantId || null
      })

      if (error) {
        return {
          success: false,
          error: `Erro ao processar pagamentos em atraso: ${error.message}`
        }
      }

      return {
        success: true,
        message: 'Pagamentos em atraso processados com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Confirmar pagamento
   * Atualiza status de 'awaiting_confirmation' para 'paid' com timestamp
   */
  async confirmPayment(data: UpdatePaymentStatusData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      // Primeiro verificar se o pagamento está com status awaiting_confirmation
      const { data: payment, error: fetchError } = await supabase
        .from('payments')
        .select('status')
        .eq('id', data.paymentId)
        .single()

      if (fetchError) {
        return {
          success: false,
          error: `Erro ao buscar pagamento: ${fetchError.message}`
        }
      }

      if (payment.status !== 'awaiting_confirmation') {
        return {
          success: false,
          error: 'Apenas pagamentos com status "aguardando confirmação" podem ser confirmados'
        }
      }

      const { data: result, error } = await supabase
        .from('payments')
        .update({
          status: 'paid',
          paid_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          ...(data.motivo && {
            metadata: {
              confirmation_reason: data.motivo,
              confirmed_at: new Date().toISOString()
            }
          })
        })
        .eq('id', data.paymentId)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: `Erro ao confirmar pagamento: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: data.paymentId,
        message: 'Pagamento confirmado com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Atualizar status de pagamento
   */
  async updatePaymentStatus(data: UpdatePaymentStatusData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      const { data: result, error } = await supabase
        .from('payments')
        .update({
          status: data.novoStatus,
          updated_at: new Date().toISOString(),
          ...(data.novoStatus === 'paid' && { paid_at: new Date().toISOString() }),
          ...(data.motivo && {
            metadata: {
              status_change_reason: data.motivo
            }
          })
        })
        .eq('id', data.paymentId)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: `Erro ao atualizar status do pagamento: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: data.paymentId,
        message: 'Status do pagamento atualizado com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Atualizar detalhes completos de um pagamento (valor, status, datas, método, descrição)
   */
  async updatePaymentDetails(data: UpdatePaymentData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()

      const updateFields: Record<string, any> = {
        updated_at: new Date().toISOString()
      }

      if (data.amount !== undefined) updateFields.amount = data.amount
      if (data.status !== undefined) updateFields.status = data.status
      
      // Se o status não for 'paid', limpar os campos relacionados ao pagamento
      if (data.status && data.status !== 'paid') {
        updateFields.payment_method = null
        updateFields.paid_at = null
      } else {
        // Caso contrário, usar os valores fornecidos
        if (data.payment_method !== undefined) updateFields.payment_method = data.payment_method
        if (data.paid_at !== undefined) updateFields.paid_at = data.paid_at
      }
      
      if (data.due_date !== undefined) updateFields.due_date = data.due_date
      if (data.description !== undefined) updateFields.description = data.description

      const { data: result, error } = await supabase
        .from('payments')
        .update(updateFields)
        .eq('id', data.paymentId)
        .select()
        .single()

      if (error) {
        return {
          success: false,
          error: `Erro ao atualizar pagamento: ${error.message}`
        }
      }

      return {
        success: true,
        payment_id: data.paymentId,
        message: 'Pagamento atualizado com sucesso',
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Buscar pagamentos por estudante
   */
  async getPaymentsByStudent(data: GetPaymentsByStudentData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      const { data: payments, error } = await supabase
        .from('payments')
        .select(`
          *,
          students!inner(
            user_id,
            users!students_user_id_fkey(
              full_name,
              email
            )
          ),
          memberships(
            id,
            plan_id,
            plans(title)
          )
        `)
        .eq('student_id', data.studentId)
        .order('created_at', { ascending: false })
        .range(data.offset || 0, (data.offset || 0) + (data.limit || 10) - 1)

      if (error) {
        return {
          success: false,
          error: `Erro ao buscar pagamentos do estudante: ${error.message}`
        }
      }

      return {
        success: true,
        message: 'Pagamentos encontrados com sucesso',
        data: payments
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Buscar pagamentos por membership
   */
  async getPaymentsByMembership(data: GetPaymentsByMembershipData): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      const { data: payments, error } = await supabase
        .from('payments')
        .select('*')
        .eq('membership_id', data.membershipId)
        .order('created_at', { ascending: false })
        .range(data.offset || 0, (data.offset || 0) + (data.limit || 10) - 1)

      if (error) {
        return {
          success: false,
          error: `Erro ao buscar pagamentos da matrícula: ${error.message}`
        }
      }

      return {
        success: true,
        message: 'Pagamentos encontrados com sucesso',
        data: payments
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Buscar todos os pagamentos com paginação e filtros
   */
  async getAllPayments(data?: {
    tenantId?: string
    limit?: number
    offset?: number
    status?: string | string[]
    paymentType?: string | string[]
    paymentMethod?: string | string[]
    studentId?: string
    startDate?: string
    endDate?: string
    minAmount?: number
    maxAmount?: number
    searchText?: string
  }): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      const limit = data?.limit || 20
      const offset = data?.offset || 0

      let query = supabase
        .from('payments')
        .select(`
          *,
          students!inner(
            id,
            user_id,
            users!students_user_id_fkey!inner(
              full_name,
              email
            )
          ),
          memberships(
            id,
            plan_id,
            plans(title)
          )
        `)

      // Aplicar filtros
      if (data?.tenantId) {
        query = query.eq('tenant_id', data.tenantId)
      }

      if (data?.status) {
        if (Array.isArray(data.status)) {
          query = query.in('status', data.status)
        } else {
          query = query.eq('status', data.status)
        }
      }

      // Aplicar busca por texto usando ILIKE no nome do usuário
      if (data?.searchText && data.searchText.trim()) {
        const searchQuery = data.searchText.trim()

        // Usar filter para aplicar ILIKE no nome do usuário
        // Sintaxe alternativa para joins aninhados
        query = query.filter('students.users.full_name', 'ilike', `%${searchQuery}%`)
      }

      if (data?.paymentType) {
        if (Array.isArray(data.paymentType)) {
          query = query.in('payment_type', data.paymentType)
        } else {
          query = query.eq('payment_type', data.paymentType)
        }
      }

      if (data?.paymentMethod) {
        if (Array.isArray(data.paymentMethod)) {
          query = query.in('payment_method', data.paymentMethod)
        } else {
          query = query.eq('payment_method', data.paymentMethod)
        }
      }

      if (data?.studentId) {
        query = query.eq('student_id', data.studentId)
      }

      if (data?.startDate) {
        query = query.gte('created_at', data.startDate)
      }

      if (data?.endDate) {
        query = query.lte('created_at', data.endDate)
      }

      if (data?.minAmount) {
        query = query.gte('amount', data.minAmount)
      }

      if (data?.maxAmount) {
        query = query.lte('amount', data.maxAmount)
      }

      // Ordenação e paginação - buscar um item a mais para verificar se há mais dados
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit) // Buscar limit + 1 itens para verificar hasMore

      const { data: payments, error } = await query

      if (error) {
        return {
          success: false,
          error: `Erro ao buscar pagamentos: ${error.message}`
        }
      }

      // Verificar se há mais dados disponíveis
      const hasMore = payments && payments.length > limit
      const actualPayments = hasMore ? payments.slice(0, limit) : payments || []

      return {
        success: true,
        message: 'Pagamentos encontrados com sucesso',
        data: actualPayments,
        hasMore
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Obter métricas de pagamentos
   */
  async getPaymentMetrics(tenantId?: string, startDate?: string, endDate?: string): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      let query = supabase
        .from('payments')
        .select('amount, status, payment_type, created_at, due_date, paid_at')

      if (tenantId) {
        query = query.eq('tenant_id', tenantId)
      }

      // Aplicar filtros de data se fornecidos
      if (startDate) {
        query = query.gte('created_at', startDate)
      }

      if (endDate) {
        query = query.lte('created_at', endDate)
      }

      const { data: payments, error } = await query

      if (error) {
        return {
          success: false,
          error: `Erro ao buscar métricas de pagamentos: ${error.message}`
        }
      }

      // Calcular métricas
      const totalPayments = payments?.length || 0
      const paidPayments = payments?.filter((p: any) => p.status === 'paid').length || 0
      const pendingPayments = payments?.filter((p: any) => p.status === 'pending').length || 0
      const overdueStatusPayments = payments?.filter((p: any) => p.status === 'overdue').length || 0

      const paidAmounts = payments?.filter((p: any) => p.status === 'paid').map((p: any) => parseFloat(p.amount)) || []
      const totalRevenue = paidAmounts.reduce((sum: number, amount: number) => sum + amount, 0)
      const averagePayment = paidAmounts.length > 0 ? totalRevenue / paidAmounts.length : 0

      // Pagamentos em atraso (apenas com overdue_date preenchido)
      const overduePendingPayments = payments?.filter((p: any) =>
        p.status === 'pending' && p.overdue_date
      ).length || 0

      // Total de pagamentos vencidos (status overdue + pending com overdue_date)
      const totalOverduePayments = overdueStatusPayments + overduePendingPayments

      const metrics: PaymentMetrics = {
        totalPayments,
        paidPayments,
        pendingPayments,
        failedPayments: overdueStatusPayments, // Manter compatibilidade com interface
        totalRevenue,
        averagePayment,
        overduePayments: totalOverduePayments,
        monthlyRevenue: totalRevenue // Para compatibilidade, usar totalRevenue quando filtrado por mês
      }

      return {
        success: true,
        message: 'Métricas calculadas com sucesso',
        data: metrics
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }

  /**
   * Obter métricas de pagamentos do mês atual
   * Filtra pelos pagamentos que vencem no mês atual (due_date)
   */
  async getMonthlyPaymentMetrics(tenantId?: string): Promise<PaymentRPCResult> {
    try {
      const supabase = await this.getSupabaseClient()
      
      // Calcular primeiro e último dia do mês atual
      const now = new Date()
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

      const startDate = firstDayOfMonth.toISOString().split('T')[0] // YYYY-MM-DD
      const endDate = lastDayOfMonth.toISOString().split('T')[0] // YYYY-MM-DD

      // Buscar pagamentos que vencem no mês atual
      let query = supabase
        .from('payments')
        .select('amount, status, payment_type, created_at, due_date, paid_at')
        .gte('due_date', startDate)
        .lte('due_date', endDate)

      if (tenantId) {
        query = query.eq('tenant_id', tenantId)
      }

      const { data: payments, error } = await query

      if (error) {
        return {
          success: false,
          error: `Erro ao buscar métricas mensais de pagamentos: ${error.message}`
        }
      }

      // Calcular métricas
      const totalPayments = payments?.length || 0
      const paidPayments = payments?.filter((p: any) => p.status === 'paid').length || 0
      const pendingPayments = payments?.filter((p: any) => p.status === 'pending').length || 0
      const overdueStatusPayments = payments?.filter((p: any) => p.status === 'overdue').length || 0

      const paidAmounts = payments?.filter((p: any) => p.status === 'paid').map((p: any) => parseFloat(p.amount)) || []
      const totalRevenue = paidAmounts.reduce((sum: number, amount: number) => sum + amount, 0)
      const averagePayment = paidAmounts.length > 0 ? totalRevenue / paidAmounts.length : 0

      // Pagamentos em atraso (apenas com overdue_date preenchido)
      const overduePendingPayments = payments?.filter((p: any) =>
        p.status === 'pending' && p.overdue_date
      ).length || 0

      // Total de pagamentos vencidos (status overdue + pending com overdue_date)
      const totalOverduePayments = overdueStatusPayments + overduePendingPayments

      const metrics: PaymentMetrics = {
        totalPayments,
        paidPayments,
        pendingPayments,
        failedPayments: overdueStatusPayments,
        totalRevenue,
        averagePayment,
        overduePayments: totalOverduePayments,
        monthlyRevenue: totalRevenue
      }

      return {
        success: true,
        message: 'Métricas mensais calculadas com sucesso',
        data: metrics
      }
    } catch (error) {
      return {
        success: false,
        error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }
    }
  }
}

// Instância singleton do serviço
export const paymentService = new PaymentService()
