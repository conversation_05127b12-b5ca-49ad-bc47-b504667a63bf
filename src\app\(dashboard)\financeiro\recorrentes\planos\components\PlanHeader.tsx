import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft, Plus } from 'lucide-react'

export function PlanHeader() {
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-4 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href="/financeiro/recorrentes">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">Planos de Assinatura</h1>
          <p className="text-muted-foreground">Configure e gerencie os planos de assinatura oferecidos pela academia.</p>
        </div>
        <Button asChild>
          <Link href="/financeiro/recorrentes/planos/criar">
            <Plus className="h-4 w-4 mr-2" />
            Novo Plano
          </Link>
        </Button>
      </div>
    </div>
  )
} 