# 🧪 Exemplo de Teste da Integração Sistema de Cobranças

## 📋 Cenário de Teste

Este documento demonstra como testar a integração entre o sistema de cobranças e a criação de matrículas.

## 🎯 Pré-requisitos

1. Plano configurado com taxa de inscrição
2. Estudante sem matrículas ativas
3. Sistema de cobrança funcionando

## 🔧 Configuração do Teste

### 1. Configurar Plano com Taxa de Inscrição

```sql
-- Atualizar plano existente para incluir taxa de inscrição
UPDATE plans 
SET pricing_config = pricing_config || '{"taxaInscricao": 50}'::jsonb
WHERE id = 'f0a96c45-3f07-46db-82c8-1a792377d3bf'
AND tenant_id = 'b284acce-e30f-47a5-981a-408c4bd6dae6';
```

### 2. Verificar Estudante Disponível

```sql
-- <PERSON><PERSON> estudante sem matrículas ativas
SELECT s.id, u.first_name, u.last_name, s.tenant_id
FROM students s
JOIN users u ON u.id = s.user_id
WHERE s.tenant_id = 'b284acce-e30f-47a5-981a-408c4bd6dae6'
AND NOT EXISTS (
  SELECT 1 FROM memberships m 
  WHERE m.student_id = s.id 
  AND m.status = 'active'
)
LIMIT 1;
```

## 🚀 Executar Teste

### Opção 1: Teste via RPC (Banco de Dados)

```sql
-- Testar função RPC create_membership
SELECT create_membership(
  'bfb1fdd4-2fcc-4859-be6c-bc9e4f9a6f5a'::uuid,  -- student_id
  'f0a96c45-3f07-46db-82c8-1a792377d3bf'::uuid,  -- plan_id
  CURRENT_DATE,                                    -- start_date
  '{}'::jsonb                                      -- metadata
) as result;
```

### Opção 2: Teste via API (Frontend)

```typescript
// Exemplo de chamada da API
const response = await fetch('/api/memberships', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    alunoId: 'bfb1fdd4-2fcc-4859-be6c-bc9e4f9a6f5a',
    planoId: 'f0a96c45-3f07-46db-82c8-1a792377d3bf',
    dataInicio: '2025-07-14'
  })
});

const result = await response.json();
console.log('Resultado:', result);
```

## ✅ Resultados Esperados

### 1. Matrícula Criada com Sucesso

```json
{
  "success": true,
  "data": {
    "membership_id": "uuid-da-matricula",
    "end_date": null,
    "next_billing_date": "2025-08-14",
    "message": "Matrícula criada com sucesso",
    "signup_fee": {
      "created": true,
      "payment_id": "uuid-do-pagamento",
      "amount": 50,
      "message": "Taxa de inscrição criada com sucesso"
    }
  }
}
```

### 2. Pagamento da Taxa de Inscrição Criado

```sql
-- Verificar se o pagamento foi criado
SELECT 
  id,
  amount,
  status,
  payment_type,
  description,
  due_date,
  membership_id
FROM payments 
WHERE membership_id = 'uuid-da-matricula'
AND payment_type = 'signup_fee';
```

**Resultado esperado:**
```
id: uuid-do-pagamento
amount: 50.00
status: pending
payment_type: signup_fee
description: Taxa de Inscrição - Plano Faixa Preta
due_date: 2025-07-14
membership_id: uuid-da-matricula
```

## 🔍 Verificações de Validação

### 1. Verificar Logs da Aplicação

```bash
# Logs esperados no console
"Tentando criar taxa de inscrição para membership: uuid-da-matricula"
"Taxa de inscrição criada com sucesso: {dados-do-pagamento}"
```

### 2. Verificar Integridade dos Dados

```sql
-- Verificar se a matrícula foi criada corretamente
SELECT 
  m.id,
  m.status,
  m.start_date,
  m.next_billing_date,
  p.title as plan_title,
  p.pricing_config->>'taxaInscricao' as signup_fee_config
FROM memberships m
JOIN plans p ON p.id = m.plan_id
WHERE m.id = 'uuid-da-matricula';

-- Verificar se o pagamento está vinculado corretamente
SELECT 
  pay.id,
  pay.amount,
  pay.payment_type,
  pay.description,
  pay.membership_id,
  s.id as student_id,
  u.first_name || ' ' || u.last_name as student_name
FROM payments pay
JOIN students s ON s.id = pay.student_id
JOIN users u ON u.id = s.user_id
WHERE pay.membership_id = 'uuid-da-matricula';
```

## 🧪 Cenários de Teste Adicionais

### 1. Plano sem Taxa de Inscrição

```sql
-- Testar com plano que não tem taxa de inscrição
UPDATE plans 
SET pricing_config = pricing_config - 'taxaInscricao'
WHERE id = 'outro-plano-id';

-- Criar matrícula - deve retornar sucesso sem criar taxa
SELECT create_membership(
  'student-id'::uuid,
  'outro-plano-id'::uuid,
  CURRENT_DATE,
  '{}'::jsonb
);
```

**Resultado esperado:**
```json
{
  "signup_fee": {
    "created": false,
    "reason": "Taxa de inscrição não configurada ou erro na criação"
  }
}
```

### 2. Taxa de Inscrição Zero

```sql
-- Testar com taxa de inscrição = 0
UPDATE plans 
SET pricing_config = pricing_config || '{"taxaInscricao": 0}'::jsonb
WHERE id = 'plano-id';
```

**Resultado esperado:** Não deve criar pagamento, mas deve retornar sucesso.

### 3. Estudante com Matrícula Ativa (se permitido)

```sql
-- Verificar comportamento com múltiplas matrículas
-- (depende da configuração allow_multiple_memberships)
```

## 📊 Métricas de Sucesso

- ✅ Matrícula criada com status 'active'
- ✅ Taxa de inscrição criada com valor correto
- ✅ Pagamento vinculado à matrícula correta
- ✅ Status do pagamento = 'pending'
- ✅ Data de vencimento = data atual
- ✅ Descrição informativa
- ✅ Logs informativos no console
- ✅ Revalidação de caches executada

## 🔧 Limpeza após Teste

```sql
-- Limpar dados de teste
DELETE FROM payments WHERE membership_id = 'uuid-da-matricula';
DELETE FROM memberships WHERE id = 'uuid-da-matricula';

-- Restaurar configuração original do plano se necessário
UPDATE plans 
SET pricing_config = pricing_config - 'taxaInscricao'
WHERE id = 'f0a96c45-3f07-46db-82c8-1a792377d3bf';
```

## 🎯 Conclusão

A integração está funcionando corretamente quando:

1. **Matrículas são criadas com sucesso** independente da configuração da taxa
2. **Taxas de inscrição são criadas automaticamente** quando configuradas
3. **Erros na criação da taxa não impedem** a criação da matrícula
4. **Informações detalhadas são retornadas** sobre o resultado da operação
5. **Logs informativos são gerados** para facilitar debugging

A integração garante que o sistema de cobrança seja ativado automaticamente sempre que uma nova matrícula for criada, proporcionando uma experiência fluida para os usuários e administradores.
