/**
 * Templates padr<PERSON> do sistema
 * Define templates base para cada tipo de notificação
 */

import type { NotificationType, NotificationChannel } from '../types/notification-types';

export interface DefaultTemplate {
  type: NotificationType;
  channel: NotificationChannel;
  name: string;
  subject_template?: string;
  body_template: string;
  description: string;
}

export const DEFAULT_TEMPLATES: DefaultTemplate[] = [
  // Templates de Payment (Cobrança)
  {
    type: 'payment',
    channel: 'in_app',
    name: '<PERSON>mbre<PERSON> de Pagamento - In-App',
    body_template: `Ol<PERSON> {{studentName}}! 👋

Lembramos que você possui uma mensalidade pendente:

💰 **Valor:** R$ {{amount}}
📅 **Vencimento:** {{dueDate}}
📋 **Plano:** {{planName}}

Mantenha suas atividades em dia na {{academyName}}!`,
    description: 'Template padrão para lembretes de pagamento dentro da plataforma'
  },
  {
    type: 'payment',
    channel: 'email',
    name: 'Lembre<PERSON> de Pagamento - Email',
    subject_template: '{{academyName}} - Lembrete de Mensalidade',
    body_template: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Lembrete de Pagamento</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <!-- Header -->
        <div style="text-align: center; padding: 20px 0; border-bottom: 2px solid {{primaryColor}};">
            {{#if academyLogo}}
            <img src="{{academyLogo}}" alt="{{academyName}}" style="max-height: 80px; max-width: 200px;">
            {{/if}}
            <h1 style="color: {{primaryColor}}; margin: 10px 0;">{{academyName}}</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 0;">
            <h2 style="color: {{primaryColor}};">Olá {{studentName}}!</h2>
            
            <p>Esperamos que você esteja bem e aproveitando suas atividades na nossa academia.</p>
            
            <p>Lembramos que você possui uma mensalidade pendente com os seguintes detalhes:</p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>💰 Valor:</strong> R$ {{amount}}</p>
                <p><strong>📅 Data de Vencimento:</strong> {{dueDate}}</p>
                <p><strong>📋 Plano:</strong> {{planName}}</p>
            </div>
            
            <p>Para manter suas atividades em dia, realize o pagamento o quanto antes.</p>
            
            <p>Em caso de dúvidas, entre em contato conosco.</p>
        </div>
        
        <!-- Footer -->
        <div style="border-top: 2px solid {{primaryColor}}; padding: 20px; text-align: center; font-size: 12px; color: #666;">
            <p><strong>{{academyName}}</strong></p>
            <p>Este é um e-mail automático, não responda.</p>
        </div>
    </div>
</body>
</html>`,
    description: 'Template padrão para lembretes de pagamento por email'
  },
  {
    type: 'payment',
    channel: 'whatsapp',
    name: 'Lembrete de Pagamento - WhatsApp',
    body_template: `🥋 *{{academyName}}*

Olá {{studentName}}! 👋

Lembramos que você possui uma mensalidade pendente:

💰 *Valor:* R$ {{amount}}
📅 *Vencimento:* {{dueDate}}
📋 *Plano:* {{planName}}

Mantenha suas atividades em dia! 🚀

Em caso de dúvidas, entre em contato conosco.`,
    description: 'Template padrão para lembretes de pagamento via WhatsApp'
  },

  // Templates de Class (Aula)
  {
    type: 'class',
    channel: 'in_app',
    name: 'Lembrete de Aula - In-App',
    body_template: `Sua aula está chegando! 🥋

📚 **Aula:** {{className}}
👨‍🏫 **Instrutor:** {{instructorName}}
📅 **Data:** {{classDate}}
⏰ **Horário:** {{classTime}}

Não se esqueça de trazer seu equipamento!

{{academyName}}`,
    description: 'Template padrão para lembretes de aula dentro da plataforma'
  },
  {
    type: 'class',
    channel: 'email',
    name: 'Lembrete de Aula - Email',
    subject_template: '{{academyName}} - Lembrete de Aula: {{className}}',
    body_template: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Lembrete de Aula</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <!-- Header -->
        <div style="text-align: center; padding: 20px 0; border-bottom: 2px solid {{primaryColor}};">
            {{#if academyLogo}}
            <img src="{{academyLogo}}" alt="{{academyName}}" style="max-height: 80px; max-width: 200px;">
            {{/if}}
            <h1 style="color: {{primaryColor}}; margin: 10px 0;">{{academyName}}</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 0;">
            <h2 style="color: {{primaryColor}};">Sua aula está chegando! 🥋</h2>
            
            <p>Olá! Lembramos que você tem uma aula agendada:</p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>📚 Aula:</strong> {{className}}</p>
                <p><strong>👨‍🏫 Instrutor:</strong> {{instructorName}}</p>
                <p><strong>📅 Data:</strong> {{classDate}}</p>
                <p><strong>⏰ Horário:</strong> {{classTime}}</p>
            </div>
            
            <p>Não se esqueça de trazer seu equipamento e chegar com alguns minutos de antecedência!</p>
            
            <p>Nos vemos na aula! 💪</p>
        </div>
        
        <!-- Footer -->
        <div style="border-top: 2px solid {{primaryColor}}; padding: 20px; text-align: center; font-size: 12px; color: #666;">
            <p><strong>{{academyName}}</strong></p>
            <p>Este é um e-mail automático, não responda.</p>
        </div>
    </div>
</body>
</html>`,
    description: 'Template padrão para lembretes de aula por email'
  },

  // Templates de Enrollment (Matrícula)
  {
    type: 'enrollment',
    channel: 'in_app',
    name: 'Boas-vindas - In-App',
    body_template: `Bem-vindo(a) à {{academyName}}! 🎉

Olá {{studentName}}, é um prazer tê-lo(a) conosco!

📋 **Plano:** {{planName}}
📅 **Início:** {{startDate}}

Estamos ansiosos para acompanhar sua jornada de evolução!

Qualquer dúvida, estamos aqui para ajudar. 💪`,
    description: 'Template padrão de boas-vindas para novos alunos'
  },
  {
    type: 'enrollment',
    channel: 'email',
    name: 'Boas-vindas - Email',
    subject_template: 'Bem-vindo(a) à {{academyName}}! 🎉',
    body_template: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Bem-vindo</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <!-- Header -->
        <div style="text-align: center; padding: 20px 0; border-bottom: 2px solid {{primaryColor}};">
            {{#if academyLogo}}
            <img src="{{academyLogo}}" alt="{{academyName}}" style="max-height: 80px; max-width: 200px;">
            {{/if}}
            <h1 style="color: {{primaryColor}}; margin: 10px 0;">{{academyName}}</h1>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px 0;">
            <h2 style="color: {{primaryColor}};">Bem-vindo(a)! 🎉</h2>
            
            <p>Olá {{studentName}},</p>
            
            <p>É com grande alegria que damos as boas-vindas à nossa academia!</p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>📋 Plano Contratado:</strong> {{planName}}</p>
                <p><strong>📅 Data de Início:</strong> {{startDate}}</p>
            </div>
            
            <p>Estamos ansiosos para acompanhar sua jornada de evolução e crescimento!</p>
            
            <p>Nossa equipe está sempre disponível para ajudá-lo(a) em qualquer dúvida ou necessidade.</p>
            
            <p>Seja bem-vindo(a) à família {{academyName}}! 💪</p>
        </div>
        
        <!-- Footer -->
        <div style="border-top: 2px solid {{primaryColor}}; padding: 20px; text-align: center; font-size: 12px; color: #666;">
            <p><strong>{{academyName}}</strong></p>
            <p>Este é um e-mail automático, não responda.</p>
        </div>
    </div>
</body>
</html>`,
    description: 'Template padrão de boas-vindas por email'
  },

  // Templates de System (Sistema)
  {
    type: 'system',
    channel: 'in_app',
    name: 'Notificação do Sistema - In-App',
    body_template: `🔔 **{{academyName}}**

Olá {{userName}},

{{message}}

Obrigado pela atenção!`,
    description: 'Template padrão para notificações do sistema'
  },

  // Templates de Event (Evento)
  {
    type: 'event',
    channel: 'in_app',
    name: 'Convite para Evento - In-App',
    body_template: `🎉 **Evento na {{academyName}}**

📅 **{{eventName}}**
📍 **Local:** {{eventLocation}}
📆 **Data:** {{eventDate}}

{{eventDescription}}

Não perca essa oportunidade!`,
    description: 'Template padrão para convites de eventos'
  }
];

/**
 * Função para inserir templates padrão no banco de dados
 */
export async function insertDefaultTemplates() {
  // Esta função será implementada quando necessário
  // Por enquanto, os templates ficam disponíveis para referência
  console.log('Templates padrão definidos:', DEFAULT_TEMPLATES.length);
}
