"use client";

/**
 * Componente Principal do Dashboard Financeiro
 * Orquestra todos os dados e componentes do dashboard
 */

import { useState, useEffect, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { 
  FinancialDashboardProps,
  DashboardFilters,
  DashboardData,
  LoadingStates as LoadingStatesType
} from '../types/dashboard-types';
import {
  getDateRangeForPeriod,
  getPreviousPeriodRange,
  debounce
} from '../utils/dashboard-utils';
import { fetchDashboardData, fetchDashboardKPIs } from '../api/dashboard-client-api';

import { DashboardFilters as DashboardFiltersComponent } from './DashboardFilters';
import { DashboardLayout } from './DashboardLayout';
import { LoadingStates } from './LoadingStates';

// ============================================================================
// CONFIGURAÇÕES PADRÃO
// ============================================================================

const DEFAULT_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutos
const DEFAULT_PERIOD = 'month';

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const FinancialDashboard: React.FC<FinancialDashboardProps> = ({
  initialFilters,
  refreshInterval = DEFAULT_REFRESH_INTERVAL,
  className
}) => {
  // ============================================================================
  // ESTADO
  // ============================================================================

  const [filters, setFilters] = useState<DashboardFilters>(() => {
    const defaultRange = getDateRangeForPeriod(DEFAULT_PERIOD);
    return initialFilters || {
      dateRange: defaultRange,
      refreshInterval
    };
  });

  const [data, setData] = useState<DashboardData | null>(null);
  const [loadingStates, setLoadingStates] = useState<LoadingStatesType>({
    kpis: false,
    revenue: false,
    expenses: false,
    cashFlow: false,
    students: false,
    overall: false
  });
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // FUNÇÕES DE CARREGAMENTO
  // ============================================================================

  const loadDashboardData = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoadingStates(prev => ({ ...prev, overall: true }));
      }
      setError(null);

      const currentRange = filters.dateRange;
      
      // Garantir que as datas são objetos Date válidos
      const validDateRange = {
        ...currentRange,
        startDate: currentRange.startDate instanceof Date ? 
                   currentRange.startDate : 
                   new Date(currentRange.startDate),
        endDate: currentRange.endDate instanceof Date ? 
                 currentRange.endDate : 
                 new Date(currentRange.endDate)
      };
      
      const previousRange = getPreviousPeriodRange(validDateRange);

      // Para MVP da Fase 1, carregar apenas KPIs de forma otimizada
      const result = await fetchDashboardKPIs(validDateRange, previousRange);

      if (!result.success) {
        throw new Error(result.error || 'Erro ao carregar dados do dashboard');
      }

      // Criar dados mock para as outras seções (serão implementadas nas próximas fases)
      const dashboardData: DashboardData = {
        kpis: result.data!,
        revenueMetrics: {
          totalRevenue: result.data!.totalRevenue.current,
          paidPayments: 0,
          pendingPayments: 0,
          overduePayments: 0,
          averagePayment: 0,
          monthlyRecurringRevenue: 0,
          revenueGrowth: result.data!.totalRevenue.growth
        },
        expenseMetrics: {
          totalExpenses: result.data!.totalExpenses.current,
          paidExpenses: 0,
          pendingExpenses: 0,
          overdueExpenses: 0,
          averageExpense: 0,
          expenseGrowth: result.data!.totalExpenses.growth,
          expensesByCategory: []
        },
        cashFlowMetrics: {
          currentBalance: result.data!.netProfit.current,
          totalInflows: result.data!.totalRevenue.current,
          totalOutflows: result.data!.totalExpenses.current,
          netCashFlow: result.data!.netProfit.current,
          projectedBalance: result.data!.netProfit.current,
          cashFlowTrend: result.data!.netProfit.trend
        },
        studentMetrics: {
          totalStudents: result.data!.activeStudents.current,
          activeStudents: result.data!.activeStudents.current,
          newStudents: 0,
          churnedStudents: 0,
          retentionRate: 100,
          churnRate: 0,
          averageLifetimeValue: result.data!.averageRevenuePerUser.current
        },
        lastUpdated: new Date()
      };

      setData(dashboardData);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao carregar dashboard:', err);
    } finally {
      if (showLoading) {
        setLoadingStates(prev => ({ ...prev, overall: false }));
      }
    }
  }, [filters.dateRange]);

  // Debounce para evitar muitas chamadas durante mudanças de filtro
  const debouncedLoadData = useCallback(
    debounce(() => loadDashboardData(true), 500),
    [loadDashboardData]
  );

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleFiltersChange = useCallback((newFilters: DashboardFilters) => {
    // Garantir que as datas são objetos Date válidos antes de atualizar o estado
    const validatedFilters = {
      ...newFilters,
      dateRange: {
        ...newFilters.dateRange,
        startDate: newFilters.dateRange.startDate instanceof Date ? 
                   newFilters.dateRange.startDate : 
                   new Date(newFilters.dateRange.startDate),
        endDate: newFilters.dateRange.endDate instanceof Date ? 
                 newFilters.dateRange.endDate : 
                 new Date(newFilters.dateRange.endDate)
      }
    };
    
    setFilters(validatedFilters);
    // Carregar dados automaticamente quando filtros mudarem
    debouncedLoadData();
  }, [debouncedLoadData]);

  const handleRefresh = useCallback(() => {
    loadDashboardData(true);
  }, [loadDashboardData]);

  // ============================================================================
  // EFEITOS
  // ============================================================================

  // Carregar dados iniciais
  useEffect(() => {
    loadDashboardData(true);
  }, []); // Executar apenas uma vez na montagem

  // Auto-refresh se configurado
  useEffect(() => {
    if (!filters.refreshInterval) return;

    const interval = setInterval(() => {
      loadDashboardData(false); // Refresh silencioso
    }, filters.refreshInterval);

    return () => clearInterval(interval);
  }, [filters.refreshInterval, loadDashboardData]);

  // ============================================================================
  // RENDER
  // ============================================================================

  const isLoading = loadingStates.overall;
  const hasData = data !== null;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filtros */}
      <DashboardFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        loading={isLoading}
      />

      {/* Erro Global */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Conteúdo Principal */}
      {isLoading && !hasData ? (
        <LoadingStates.Dashboard />
      ) : hasData ? (
        <DashboardLayout
          kpis={data.kpis}
          data={data}
          loading={isLoading}
          error={error}
        />
      ) : (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-gray-400 dark:text-gray-500 mb-4">
              <AlertCircle className="h-12 w-12 mx-auto" />
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Nenhum dado disponível para o período selecionado
            </p>
          </div>
        </div>
      )}

      {/* Informações de Debug (apenas em desenvolvimento) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-xs">
          <details>
            <summary className="cursor-pointer font-medium mb-2">
              Debug Info (Desenvolvimento)
            </summary>
            <div className="space-y-2 text-gray-600 dark:text-gray-400">
              <div>
                <strong>Período:</strong> {filters.dateRange.label}
              </div>
              <div>
                <strong>Data Início:</strong> {filters.dateRange.startDate instanceof Date ? 
                  filters.dateRange.startDate.toLocaleDateString('pt-BR') : 'Data inválida'}
              </div>
              <div>
                <strong>Data Fim:</strong> {filters.dateRange.endDate instanceof Date ? 
                  filters.dateRange.endDate.toLocaleDateString('pt-BR') : 'Data inválida'}
              </div>
              <div>
                <strong>Loading:</strong> {JSON.stringify(loadingStates)}
              </div>
              <div>
                <strong>Tem Dados:</strong> {hasData ? 'Sim' : 'Não'}
              </div>
              <div>
                <strong>Última Atualização:</strong> {data?.lastUpdated?.toLocaleString('pt-BR') || 'N/A'}
              </div>
            </div>
          </details>
        </div>
      )}
    </div>
  );
};
