---
alwaysApply: true
---
# Princípios de Segurança em Primeiro Lugar

## Autenticação e Autorização

- **Sempre use os serviços de autenticação existentes** em [src/services/auth](mdc:src/services/auth)
- **Nunca implemente seu próprio sistema de autenticação** ou contorne os mecanismos existentes
- **Verifique permissões explicitamente** antes de permitir acesso a recursos protegidos
- **Utilize as políticas de permissão** em [src/services/permissions/policies](mdc:src/services/permissions/policies)

## Manipulação de Dados Sensíveis

- **Nunca exponha dados sensíveis** em logs, URLs ou estado do cliente
- **Sempre sanitize e valide entradas do usuário** usando Zod ou validadores similares
- **Utilize Server Actions** para operações que manipulam dados sensíveis
- **Minimize o armazenamento de PII** (Informações Pessoalmente Identificáveis)

## Comunicação Segura

- **Use HTTPS** para todas as comunicações (já configurado no ambiente de produção)
- **Implemente rate limiting** para APIs que manipulam dados sensíveis
- **Evite Cross-Site Scripting (XSS)** não inserindo HTML diretamente no DOM
- **Previna CSRF** utilizando os tokens automáticos do Next.js

## Práticas de Código Seguro

- **Mantenha dependências atualizadas** para evitar vulnerabilidades conhecidas
- **Nunca exponha tokens ou chaves** em código-fonte ou em clientes
- **Prefira Server Components** para lógica que não precisa ser exposta ao cliente
- **Implemente validação de entrada em múltiplas camadas** (client e server)
- **Armazene senhas usando hashing adequado** (gerenciado pelo Supabase Auth)

## Modelo de Ameaças Comuns

1. **Injeção de SQL**: Utilize sempre QueryBuilder ou ORM com parâmetros preparados
2. **Quebra de Autenticação**: Siga as práticas do sistema de autenticação existente
3. **Exposição de Dados Sensíveis**: Sanitize dados antes de enviá-los ao cliente
4. **CSRF**: Utilize os mecanismos do Next.js para proteção automática
5. **Falhas de Configuração**: Siga o padrão de variáveis de ambiente

## Exemplos de Implementação Segura

### Validação com Zod
```typescript
const userSchema = z.object({
  email: z.string().email("Email inválido"),
  password: z.string().min(8, "Senha deve ter pelo menos 8 caracteres")
});

// Server Action com validação
export async function loginUser(formData: unknown) {
  const result = userSchema.safeParse(formData);
  if (!result.success) {
    return { success: false, errors: result.error.format() };
  }
  // Processar login com dados validados...
}
```

### Verificação de Permissão
```typescript
import { checkPermission } from "@/services/permissions";

// Server Component ou Server Action
async function getUserData(userId: string) {
  // Verificar permissão antes de acessar dados
  const hasPermission = await checkPermission("user:read", { userId });
  if (!hasPermission) {
    throw new Error("Acesso não autorizado");
  }
  
  // Buscar dados do usuário de forma segura...
}
```

