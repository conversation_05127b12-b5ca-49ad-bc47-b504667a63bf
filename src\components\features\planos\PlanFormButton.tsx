'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface PlanFormButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  loading?: boolean
  fullWidth?: boolean
  children: React.ReactNode
  className?: string
}

interface PlanFormSubmitButtonProps extends Omit<PlanFormButtonProps, 'variant' | 'children'> {
  submitting?: boolean
  submitText?: string
  submittingText?: string
  children?: React.ReactNode
}

interface PlanFormCancelButtonProps extends Omit<PlanFormButtonProps, 'variant'> {
  onCancel?: () => void
}

export function PlanFormButton({ variant = 'default', size = 'default', icon, iconPosition = 'left', loading = false, fullWidth = false, children, className, disabled, ...props }: PlanFormButtonProps) {
  const isDisabled = disabled || loading

  return (
    <Button variant={variant} size={size} disabled={isDisabled} className={cn(fullWidth && 'w-full', loading && 'cursor-not-allowed opacity-70', className)} {...props}>
      {loading && (
        <div className="mr-2">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        </div>
      )}

      {!loading && icon && iconPosition === 'left' && <span className="mr-2 flex-shrink-0">{icon}</span>}

      <span>{children}</span>

      {!loading && icon && iconPosition === 'right' && <span className="ml-2 flex-shrink-0">{icon}</span>}
    </Button>
  )
}

export function PlanFormSubmitButton({ submitting = false, submitText = 'Salvar', submittingText = 'Salvando...', icon, className, children, onClick, ...props }: PlanFormSubmitButtonProps) {
  return (
    <PlanFormButton 
      type="submit"
      variant="default" 
      loading={submitting} 
      icon={icon} 
      className={cn('bg-teal-600 hover:bg-teal-700 dark:bg-teal-600 dark:hover:bg-teal-700 text-white font-semibold', className)} 
      onClick={onClick}
      {...props}
    >
      {children || (submitting ? submittingText : submitText)}
    </PlanFormButton>
  )
}

export function PlanFormCancelButton({ onCancel, children = 'Cancelar', className, ...props }: PlanFormCancelButtonProps) {
  return (
    <PlanFormButton variant="outline" onClick={onCancel} className={cn('border-input hover:bg-accent hover:text-accent-foreground', className)} {...props}>
      {children}
    </PlanFormButton>
  )
}

export function PlanFormDeleteButton({ children = 'Excluir', className, ...props }: Omit<PlanFormButtonProps, 'variant'>) {
  return (
    <PlanFormButton variant="destructive" className={cn('bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700', className)} {...props}>
      {children}
    </PlanFormButton>
  )
}
