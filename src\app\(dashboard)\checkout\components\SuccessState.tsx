import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, ArrowLeft, Receipt } from 'lucide-react'
import Link from 'next/link'

interface SuccessStateProps {
  title?: string
  message: string
  showBackToProfile?: boolean
  showViewPayments?: boolean
  paymentAmount?: number
  studentName?: string
}

export function SuccessState({
  title = 'Pagamento Confirmado!',
  message,
  showBackToProfile = true,
  showViewPayments = false,
  paymentAmount,
  studentName
}: SuccessStateProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount)
  }

  return (
    <Card className="p-8 text-center border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="flex justify-center mb-4">
        <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
          <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
      </div>

      <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h2>

      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
        {message}
      </p>

      {/* Detalhes do pagamento */}
      {(paymentAmount || studentName) && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6 max-w-sm mx-auto">
          {paymentAmount && (
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-700 dark:text-gray-300">Valor:</span>
              <span className="font-semibold text-gray-800 dark:text-gray-200">
                {formatCurrency(paymentAmount)}
              </span>
            </div>
          )}
          {studentName && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-700 dark:text-gray-300">Aluno:</span>
              <span className="font-semibold text-gray-800 dark:text-gray-200">
                {studentName}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Próximos passos */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
        <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
          Próximos passos:
        </h3>
        <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
          <li>• O administrador da academia foi notificado</li>
          <li>• Seu pagamento será validado em breve</li>
          <li>• Você receberá uma confirmação quando aprovado</li>
        </ul>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {showBackToProfile && (
          <Button asChild className="bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-100 dark:text-gray-900 dark:hover:bg-gray-200">
            <Link href="/perfil" className="gap-2">
              <ArrowLeft className="w-4 h-4" />
              Voltar ao Perfil
            </Link>
          </Button>
        )}

        {/* {showViewPayments && (
          <Button variant="outline" asChild className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800">
            <Link href="/perfil?tab=mensalidades" className="gap-2">
              <Receipt className="w-4 h-4" />
              Ver Pagamentos
            </Link>
          </Button>
        )} */}
      </div>
    </Card>
  )
}
