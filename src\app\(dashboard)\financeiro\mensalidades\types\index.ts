export interface TuitionMetrics {
  paidTuitions: number;
  scheduledTuitions: number;
  overdueTuitions: number;
  monthlyRevenueAverage: number;
  expectedMonthlyRevenue?: number;
}

export interface TuitionValueMetrics {
  paidTuitionsValue: number;
  scheduledTuitionsValue: number;
  overdueTuitionsValue: number;
  monthlyRevenueAverage: number;
  expectedMonthlyRevenue?: number;
}

export interface Payment {
  id: string;
  studentId: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'overdue';
  paymentMethod: string | null;
  paidAt: string | null;
  createdAt: string;
  studentName: string;
  studentEmail: string;
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
  payments: number;
}

export interface MonthlyPaymentStatus {
  month: string;
  paid: number;
  overdue: number;
  paidAmount: number;
  overdueAmount: number;
}

export interface PaymentMethodDistribution {
  method: string;
  count: number;
  percentage: number;
}

// Mantendo para compatibilidade retroativa
export interface PaymentMetrics {
  totalPayments: number;
  paidPayments: number;
  pendingPayments: number;
  failedPayments: number;
  totalRevenue: number;
  averagePayment: number;
} 