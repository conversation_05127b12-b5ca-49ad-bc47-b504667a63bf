import { NextRequest, NextResponse } from 'next/server';
import { getDashboardKPIs } from '@/app/(dashboard)/financeiro/dashboard/actions/dashboard-actions';
import { DateRange } from '@/app/(dashboard)/financeiro/dashboard/types/dashboard-types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { currentRange, previousRange }: { 
      currentRange: DateRange; 
      previousRange: DateRange; 
    } = body;

    // Validar dados de entrada
    if (!currentRange || !previousRange) {
      return NextResponse.json(
        { success: false, error: 'Parâmetros currentRange e previousRange são obrigatórios' },
        { status: 400 }
      );
    }

    // Chamar a Server Action
    const result = await getDashboardKPIs(currentRange, previousRange);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Erro na API de KPIs do dashboard:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}
