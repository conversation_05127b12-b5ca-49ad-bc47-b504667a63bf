'use client';

import { useEffect, useState } from 'react';

/**
 * Hook para garantir que componentes sejam renderizados apenas no cliente
 * Evita problemas de hidratação quando há diferenças entre server e client
 */
export function useHydrationSafe() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}

/**
 * Hook para gerar IDs estáveis que não causam problemas de hidratação
 */
export function useStableId(prefix: string = 'id') {
  const [id, setId] = useState<string>('');

  useEffect(() => {
    setId(`${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  }, [prefix]);

  return id;
}

/**
 * Hook para valores que podem diferir entre server e client
 */
export function useClientValue<T>(clientValue: T, serverValue: T) {
  const [value, setValue] = useState(serverValue);

  useEffect(() => {
    setValue(clientValue);
  }, [clientValue]);

  return value;
}