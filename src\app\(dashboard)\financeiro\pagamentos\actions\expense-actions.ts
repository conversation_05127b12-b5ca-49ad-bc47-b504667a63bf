'use server';

import { createClient } from '@/services/supabase/server';
import { revalidatePath } from 'next/cache';

/**
 * Interface para dados de despesa
 */
export interface ExpenseData {
  category_id?: string;
  supplier_name: string;
  supplier_document?: string;
  amount: number;
  currency?: string;
  description?: string;
  due_date?: string;
  payment_method_id?: string;
  status: 'pending' | 'paid' | 'overdue' | 'canceled';
  is_recurring?: boolean;
  recurrence_config?: any;
  metadata?: any;
}

/**
 * Resultado de operações de despesa
 */
export interface ExpenseActionResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Adicionar nova despesa
 */
export async function addExpense(expenseData: ExpenseData): Promise<ExpenseActionResult> {
  try {
    const supabase = await createClient();

    // Obter o tenant_id do usuário autenticado
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Buscar o tenant_id do usuário
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.tenant_id) {
      return {
        success: false,
        error: 'Não foi possível identificar o tenant do usuário'
      };
    }

    // Validações básicas
    if (!expenseData.supplier_name?.trim()) {
      return {
        success: false,
        error: 'Nome do fornecedor é obrigatório'
      };
    }

    if (!expenseData.amount || expenseData.amount <= 0) {
      return {
        success: false,
        error: 'Valor deve ser maior que zero'
      };
    }

    // Preparar dados para inserção
    const insertData = {
      ...expenseData,
      tenant_id: userData.tenant_id, // Adicionar o tenant_id
      currency: expenseData.currency || 'BRL',
      metadata: expenseData.metadata || {},
      created_at: new Date().toISOString(),
    };

    // Log para debug
    console.log('Dados sendo inseridos:', {
      tenant_id: insertData.tenant_id,
      payment_method_id: insertData.payment_method_id,
      supplier_name: insertData.supplier_name,
      amount: insertData.amount
    });

    const { data, error } = await supabase
      .from('expenses')
      .insert([insertData])
      .select(`
        *,
        expense_categories (
          id,
          name,
          color
        ),
        payment_methods (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Erro ao adicionar despesa:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Revalidar a página para atualizar os dados
    revalidatePath('/financeiro/pagamentos');

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Erro ao adicionar despesa:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Atualizar despesa existente
 */
export async function updateExpense(id: string, expenseData: Partial<ExpenseData>): Promise<ExpenseActionResult> {
  try {
    const supabase = await createClient();
    
    // Preparar dados para atualização
    const updateData = {
      ...expenseData,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('expenses')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        expense_categories (
          id,
          name,
          color
        ),
        payment_methods (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Erro ao atualizar despesa:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Revalidar a página para atualizar os dados
    revalidatePath('/financeiro/pagamentos');

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Erro ao atualizar despesa:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Marcar despesa como paga
 */
export async function markExpenseAsPaid(id: string, paymentMethodId?: string): Promise<ExpenseActionResult> {
  try {
    const supabase = await createClient();
    
    const updateData: any = {
      status: 'paid',
      paid_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (paymentMethodId) {
      updateData.payment_method_id = paymentMethodId;
    }

    const { data, error } = await supabase
      .from('expenses')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        expense_categories (
          id,
          name,
          color
        ),
        payment_methods (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Erro ao marcar despesa como paga:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Revalidar a página para atualizar os dados
    revalidatePath('/financeiro/pagamentos');

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Erro ao marcar despesa como paga:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Cancelar despesa
 */
export async function cancelExpense(id: string): Promise<ExpenseActionResult> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('expenses')
      .update({
        status: 'canceled',
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select(`
        *,
        expense_categories (
          id,
          name,
          color
        ),
        payment_methods (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Erro ao cancelar despesa:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Revalidar a página para atualizar os dados
    revalidatePath('/financeiro/pagamentos');

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Erro ao cancelar despesa:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Deletar despesa
 */
export async function deleteExpense(id: string): Promise<ExpenseActionResult> {
  try {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erro ao deletar despesa:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Revalidar a página para atualizar os dados
    revalidatePath('/financeiro/pagamentos');

    return {
      success: true
    };
  } catch (error) {
    console.error('Erro ao deletar despesa:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
