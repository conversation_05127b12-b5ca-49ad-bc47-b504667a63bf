"use client";

/**
 * Seção de Receitas do Dashboard Financeiro
 * Contém todos os KPIs, gráficos e análises relacionadas a receitas
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, DollarSign } from 'lucide-react';
import { cn } from '@/lib/utils';

import { DashboardLayoutProps } from '../types/dashboard-types';
import { LoadingStates } from './LoadingStates';
import { EnhancedKPICard } from './EnhancedKPICard';
import {
  MonthlyRevenueChart,
  ModalityRevenueChart,
  PaymentStatusChart,
  PaymentMethodsChart
} from './charts';

// ============================================================================
// TIPOS
// ============================================================================

interface RevenueSectionProps {
  kpis: DashboardLayoutProps['kpis'];
  data?: DashboardLayoutProps['data'];
  loading?: boolean;
  error?: string | null;
  className?: string;
}

// ============================================================================
// SEÇÃO DE KPIs DE RECEITA
// ============================================================================

interface RevenueKPISectionProps {
  kpis: DashboardLayoutProps['kpis'];
  loading?: boolean;
}

const RevenueKPISection: React.FC<RevenueKPISectionProps> = ({ kpis, loading }) => {
  if (loading) {
    return <LoadingStates.KPIs />;
  }

  // Dados simulados para sparklines (serão implementados com dados reais nas próximas iterações)
  const generateSparklineData = (trend: string) => {
    const baseData = [100, 105, 98, 110, 115, 108, 120];
    if (trend === 'up') return baseData.map((v, i) => v + i * 2);
    if (trend === 'down') return baseData.map((v, i) => v - i * 1.5);
    return baseData;
  };

  return (
    <div className="space-y-6">
      {/* KPIs de Receita */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <EnhancedKPICard
          title="Receita Total"
          metric={kpis.totalRevenue}
          icon={<DollarSign className="h-5 w-5" />}
          description="Total de receitas pagas no período"
          sparklineData={generateSparklineData(kpis.totalRevenue.trend)}
          variant="default"
        />
        <EnhancedKPICard
          title="Receita por Aluno"
          metric={kpis.averageRevenuePerUser}
          icon={<TrendingUp className="h-5 w-5" />}
          description="Receita média por aluno ativo"
          sparklineData={generateSparklineData(kpis.averageRevenuePerUser.trend)}
          variant="default"
        />
        <EnhancedKPICard
          title="Margem de Lucro"
          metric={kpis.profitMargin}
          icon={<TrendingUp className="h-5 w-5" />}
          description="Percentual de lucro sobre a receita"
          sparklineData={generateSparklineData(kpis.profitMargin.trend)}
          variant="default"
        />
      </div>
    </div>
  );
};

// ============================================================================
// COMPONENTE PRINCIPAL DA SEÇÃO DE RECEITAS
// ============================================================================

export const RevenueSection: React.FC<RevenueSectionProps> = ({
  kpis,
  data,
  loading = false,
  error = null,
  className
}) => {
  // Mostrar erro se houver
  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Erro ao carregar dados de receitas: {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Loading completo
  if (loading && !data) {
    return (
      <div className={cn("space-y-6", className)}>
        <LoadingStates.Dashboard />
      </div>
    );
  }

  return (
    <div className={cn("space-y-8", className)}>
      {/* Seção 1: KPIs de Receita */}
      <section>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Indicadores de Receita
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Principais métricas de receita com comparações e tendências
              </p>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <span>Métricas de Receita</span>
              </div>
            </div>
          </div>
        </div>
        <RevenueKPISection kpis={kpis} loading={loading} />
      </section>

      {/* Seção 2: Análise Comparativa de Receitas */}
      {/* <section>
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Análise Comparativa de Receitas
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Comparações detalhadas de receitas com período anterior
          </p>
        </div>
        <ComparisonMetrics
          kpis={kpis}
          periodLabel={data?.lastUpdated ?
            new Date(data.lastUpdated).toLocaleDateString('pt-BR', {
              month: 'long',
              year: 'numeric'
            }) : 'Período Atual'
          }
        />
      </section> */}

      {/* Seção 3: Gráficos de Receita */}
      <section>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Análise Visual de Receitas
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Visualizações detalhadas de receitas, modalidades e métodos de pagamento
              </p>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <span>Gráficos de Receita</span>
              </div>
            </div>
          </div>
        </div>

        {/* Grid de Gráficos */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Gráfico de Receita Mensal */}
          <div className="lg:col-span-2">
            <MonthlyRevenueChart />
          </div>

          {/* Gráfico de Receita por Modalidade */}
          <ModalityRevenueChart />

          {/* Gráfico de Status de Pagamentos */}
          <PaymentStatusChart />

          {/* Gráfico de Métodos de Pagamento - Span completo */}
          <div className="lg:col-span-2">
            <PaymentMethodsChart />
          </div>
        </div>
      </section>

      {/* Informações de Atualização */}
      <section className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Última atualização: {data?.lastUpdated ?
            new Date(data.lastUpdated).toLocaleString('pt-BR') :
            'Carregando...'
          }
        </div>
      </section>
    </div>
  );
};
