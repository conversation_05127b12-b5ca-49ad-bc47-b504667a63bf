'use server';

import { z } from 'zod';
import { createTenantServerClient } from '@/services/supabase/server';
import { revalidatePath } from 'next/cache';

/**
 * Schema de validação para configurações gerais de pagamento
 */
const generalSettingsSchema = z.object({
  currency: z.string().min(3, 'Có<PERSON> da moeda deve ter pelo menos 3 caracteres').max(3, 'Código da moeda deve ter no máximo 3 caracteres').toUpperCase(),
  defaultDueDay: z.string().min(1, 'Dia de vencimento é obrigatório'),
});

export type GeneralSettingsInput = z.infer<typeof generalSettingsSchema>;

/**
 * Interface para o resultado das operações
 */
interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
}

/**
 * Interface para os dados das configurações gerais
 */
export interface GeneralSettingsData {
  currency: string;
  defaultDueDay: string;
  allowInstallments?: boolean;
  convenienceFee?: boolean;
  pixDiscount?: boolean;
}

/**
 * saveGeneralSettings – Salva as configurações gerais de pagamento do tenant
 *
 * @param input {GeneralSettingsInput}
 * @returns {Promise<ActionResult>} – Status da operação
 */
export async function saveGeneralSettings(input: unknown): Promise<ActionResult> {
  try {
    // Validar entrada
    const parsed = generalSettingsSchema.safeParse(input);
    if (!parsed.success) {
      return { 
        success: false, 
        errors: parsed.error.format() as unknown as Record<string, string>
      };
    }

    const validatedData = parsed.data;
    const supabase = await createTenantServerClient();

    // Obter tenant_id do contexto
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, errors: { _form: 'Usuário não autenticado' } };
    }

    // Buscar tenant_id do usuário
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.tenant_id) {
      console.error('[saveGeneralSettings] Erro ao buscar tenant_id:', userError);
      return { success: false, errors: { _form: 'Tenant não identificado' } };
    }

    const tenantId = userData.tenant_id;

    // Verificar se já existe configuração para este tenant
    const { data: existingConfig, error: selectError } = await supabase
      .from('tenant_general_payment_settings')
      .select('tenant_id')
      .eq('tenant_id', tenantId)
      .single();

    if (selectError && selectError.code !== 'PGRST116') {
      console.error('[saveGeneralSettings] Erro ao verificar configuração existente:', selectError);
      return { success: false, errors: { _form: 'Erro ao verificar configurações existentes' } };
    }

    const configExists = !!existingConfig;

    if (configExists) {
      // Atualizar configuração existente
      const { error: updateError } = await supabase
        .from('tenant_general_payment_settings')
        .update({
          currency: validatedData.currency,
          default_due_day: validatedData.defaultDueDay,
          updated_at: new Date().toISOString(),
        })
        .eq('tenant_id', tenantId);

      if (updateError) {
        console.error('[saveGeneralSettings] Erro ao atualizar configurações:', updateError);
        return { success: false, errors: { _form: 'Erro ao salvar configurações' } };
      }
    } else {
      // Criar nova configuração
      const { error: insertError } = await supabase
        .from('tenant_general_payment_settings')
        .insert({
          tenant_id: tenantId,
          currency: validatedData.currency,
          default_due_day: validatedData.defaultDueDay,
          // Valores padrão para outras colunas
          allow_installments: false,
          convenience_fee: false,
          pix_discount: false,
        });

      if (insertError) {
        console.error('[saveGeneralSettings] Erro ao criar configurações:', insertError);
        return { success: false, errors: { _form: 'Erro ao salvar configurações' } };
      }
    }

    // Revalidar cache das páginas relacionadas
    revalidatePath('/financeiro/configuracoes');
    revalidatePath('/financeiro');
    revalidatePath('/financeiro/configuracoes', 'page');

    return {
      success: true,
      data: {
        currency: validatedData.currency,
        defaultDueDay: validatedData.defaultDueDay,
      },
    };

  } catch (error) {
    console.error('[saveGeneralSettings] Erro interno:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}

/**
 * getGeneralSettings – Busca as configurações gerais de pagamento do tenant
 *
 * @returns {Promise<ActionResult>} – Configurações ou erro
 */
export async function getGeneralSettings(): Promise<ActionResult> {
  try {
    const supabase = await createTenantServerClient();

    // Obter tenant_id do contexto
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, errors: { _form: 'Usuário não autenticado' } };
    }

    // Buscar tenant_id do usuário
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.tenant_id) {
      console.error('[getGeneralSettings] Erro ao buscar tenant_id:', userError);
      return { success: false, errors: { _form: 'Tenant não identificado' } };
    }

    const tenantId = userData.tenant_id;

    // Buscar configurações do tenant
    const { data: settings, error: settingsError } = await supabase
      .from('tenant_general_payment_settings')
      .select('currency, default_due_day, allow_installments, convenience_fee, pix_discount')
      .eq('tenant_id', tenantId)
      .single();

    if (settingsError) {
      if (settingsError.code === 'PGRST116') {
        // Não existe configuração ainda, retornar valores padrão
        return {
          success: true,
          data: {
            currency: 'BRL',
            defaultDueDay: 'enrollment_date',
            allowInstallments: false,
            convenienceFee: false,
            pixDiscount: false,
          },
        };
      }

      console.error('[getGeneralSettings] Erro ao buscar configurações:', settingsError);
      return { success: false, errors: { _form: 'Erro ao buscar configurações' } };
    }

    return {
      success: true,
      data: {
        currency: settings.currency,
        defaultDueDay: settings.default_due_day,
        allowInstallments: settings.allow_installments,
        convenienceFee: settings.convenience_fee,
        pixDiscount: settings.pix_discount,
      },
    };

  } catch (error) {
    console.error('[getGeneralSettings] Erro interno:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' },
    };
  }
}
