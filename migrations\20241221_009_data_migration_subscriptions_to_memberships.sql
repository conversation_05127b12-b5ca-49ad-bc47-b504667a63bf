-- Migration: Data migration from subscriptions_legacy to memberships
-- Purpose: Migrate existing subscription data to the new memberships system
-- Affected tables: memberships (data inserted), plans (data inserted)
-- Dependencies: All previous migration scripts in this series
-- Date: 2024-12-21
-- IMPORTANT: This script should be run carefully and reviewed before execution

-- Step 1: Create temporary function to migrate subscription data
create or replace function public.migrate_subscriptions_to_memberships()
returns table (
  migrated_plans integer,
  migrated_memberships integer,
  errors text[]
)
language plpgsql
security invoker
set search_path = ''
as $$
declare
  plan_count integer := 0;
  membership_count integer := 0;
  error_list text[] := '{}';
  subscription_record record;
  new_plan_id uuid;
  pricing_config jsonb;
  duration_config jsonb;
  access_config jsonb;
begin
  -- Step 1: Create default plans for each unique subscription configuration
  for subscription_record in 
    select distinct 
      tenant_id,
      plan_name,
      amount,
      frequency,
      coalesce(duration_months, 0) as duration_months
    from public.subscriptions_legacy 
    where plan_name is not null
  loop
    begin
      -- Build pricing configuration based on legacy data
      pricing_config := jsonb_build_object(
        'tipo', 'recurring',
        'valor', coalesce(subscription_record.amount::text, '0'),
        'frequencia', case 
          when subscription_record.frequency = 'monthly' then 'month'
          when subscription_record.frequency = 'yearly' then 'year'
          when subscription_record.frequency = 'weekly' then 'week'
          else 'month'
        end,
        'numeroFrequencia', '1'
      );
      
      -- Build duration configuration
      if subscription_record.duration_months > 0 then
        duration_config := jsonb_build_object(
          'tipo', 'limited',
          'duracao', subscription_record.duration_months,
          'unidadeTempo', 'months'
        );
      else
        duration_config := jsonb_build_object(
          'tipo', 'ongoing'
        );
      end if;
      
      -- Build basic access configuration
      access_config := jsonb_build_object(
        'frequencia', 'unlimited',
        'capacidade', 'unlimited',
        'modalidades', '[]',
        'todasSessoes', true
      );
      
      -- Insert plan
      insert into public.plans (
        tenant_id,
        title,
        plan_type,
        status,
        pricing_config,
        duration_config,
        access_config,
        metadata
      ) values (
        subscription_record.tenant_id,
        subscription_record.plan_name,
        'individual',
        'archived', -- Mark as archived since these are legacy
        pricing_config,
        duration_config,
        access_config,
        jsonb_build_object('migrated_from_legacy', true, 'migration_date', now())
      ) returning id into new_plan_id;
      
      plan_count := plan_count + 1;
      
    exception
      when others then
        error_list := error_list || ('Plan creation error: ' || sqlerrm);
    end;
  end loop;
  
  -- Step 2: Migrate individual subscriptions to memberships
  for subscription_record in 
    select 
      s.id as subscription_id,
      s.tenant_id,
      s.student_id,
      s.plan_name,
      s.amount,
      s.frequency,
      s.status,
      s.start_date,
      s.end_date,
      s.created_at,
      p.id as plan_id
    from public.subscriptions_legacy s
    inner join public.plans p on (
      p.tenant_id = s.tenant_id and 
      p.title = s.plan_name and
      p.metadata ->> 'migrated_from_legacy' = 'true'
    )
  loop
    begin
      -- Insert membership
      insert into public.memberships (
        tenant_id,
        student_id,
        plan_id,
        status,
        start_date,
        end_date,
        created_at,
        metadata
      ) values (
        subscription_record.tenant_id,
        subscription_record.student_id,
        subscription_record.plan_id,
        case 
          when subscription_record.status = 'active' then 'active'::public.membership_status
          when subscription_record.status = 'canceled' then 'canceled'::public.membership_status
          when subscription_record.status = 'expired' then 'expired'::public.membership_status
          when subscription_record.status = 'paused' then 'paused'::public.membership_status
          else 'canceled'::public.membership_status
        end,
        coalesce(subscription_record.start_date, subscription_record.created_at::date),
        subscription_record.end_date,
        subscription_record.created_at,
        jsonb_build_object(
          'migrated_from_legacy', true,
          'legacy_subscription_id', subscription_record.subscription_id,
          'migration_date', now()
        )
      );
      
      membership_count := membership_count + 1;
      
    exception
      when others then
        error_list := error_list || ('Membership creation error for subscription ' || subscription_record.subscription_id || ': ' || sqlerrm);
    end;
  end loop;
  
  return query select plan_count, membership_count, error_list;
end;
$$;

-- Step 2: Add comment for the migration function
comment on function public.migrate_subscriptions_to_memberships() is 'One-time migration function to convert legacy subscriptions to the new plans and memberships system.';

-- Step 3: Create validation function to check migration results
create or replace function public.validate_subscription_migration()
returns table (
  tenant_id uuid,
  legacy_subscriptions bigint,
  migrated_memberships bigint,
  migration_complete boolean
)
language sql
security invoker
set search_path = ''
as $$
  select 
    coalesce(s.tenant_id, m.tenant_id) as tenant_id,
    coalesce(legacy_count, 0) as legacy_subscriptions,
    coalesce(migrated_count, 0) as migrated_memberships,
    coalesce(legacy_count, 0) = coalesce(migrated_count, 0) as migration_complete
  from (
    select tenant_id, count(*) as legacy_count
    from public.subscriptions_legacy
    group by tenant_id
  ) s
  full outer join (
    select tenant_id, count(*) as migrated_count
    from public.memberships
    where metadata ->> 'migrated_from_legacy' = 'true'
    group by tenant_id
  ) m using (tenant_id)
  order by tenant_id;
$$;

-- Step 4: Add comment for the validation function
comment on function public.validate_subscription_migration() is 'Validates that all legacy subscriptions have been migrated to memberships.';

-- Step 5: Instructions for manual execution
-- MANUAL EXECUTION REQUIRED:
-- 1. Review this script carefully
-- 2. Backup your database
-- 3. Run in a transaction first to test:
--    BEGIN;
--    SELECT * FROM public.migrate_subscriptions_to_memberships();
--    SELECT * FROM public.validate_subscription_migration();
--    ROLLBACK; -- or COMMIT if satisfied
-- 4. Run the actual migration:
--    SELECT * FROM public.migrate_subscriptions_to_memberships();
-- 5. Validate results:
--    SELECT * FROM public.validate_subscription_migration(); 