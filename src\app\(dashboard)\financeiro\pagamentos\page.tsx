import { Metadata } from 'next';
import {
  PageHeader,
  PaymentMetrics,
  PaymentMetric,
  Transaction,
  FinancialClient,
  FinancialMetric,
  IncomeTransaction,
  ExpenseTransaction,
} from './components';
import { getPaymentMetrics, getAllPayments } from '@/services/billing/payment-actions';
import { PaymentMetrics as PaymentMetricsData, Payment } from '@/services/billing/payment-types';
import {
  getPaymentMethods,
  getExpenses,
  getExpenseMetrics,
  getExpenseCategories
} from './actions';

export const metadata: Metadata = {
  title: 'Financeiro - Receitas e Despesas',
  description: 'Gestão completa das finanças da academia - receitas e despesas',
};

/**
 * Formatar valor monetário para exibição
 */
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Converter dados das métricas do serviço para o formato do componente
 */
function convertMetricsToDisplay(metrics: PaymentMetricsData): PaymentMetric[] {
  return [
    {
      value: formatCurrency(metrics.totalRevenue),
      label: 'Total Recebido',
      color: 'green',
    },
    {
      value: metrics.paidPayments.toString(),
      label: 'Pagamentos Realizados',
      color: 'blue',
    },
    {
      value: metrics.pendingPayments.toString(),
      label: 'Pendentes',
      color: 'orange',
    },
    {
      value: metrics.overduePayments.toString(),
      label: 'Em Atraso',
      color: 'red',
    },
  ];
}

/**
 * Converter dados de pagamentos do banco para o formato do componente IncomeTransaction
 */
function convertPaymentsToIncomeTransactions(payments: any[]): IncomeTransaction[] {
  return payments.map((payment, index) => ({
    id: payment.id || index.toString(), // Usar ID do payment ou índice como fallback
    transactionType: 'income' as const,
    studentName: payment.students?.users?.full_name || 'Nome não disponível',
    studentUserId: payment.students?.user_id,
    type: getPaymentTypeLabel(payment.payment_type),
    paymentMethod: getPaymentMethodLabel(payment.payment_method),
    date: formatDate(payment.due_date || payment.created_at), // Usar due_date se disponível, senão created_at
    amount: formatCurrency(parseFloat(payment.amount)),
    status: getStatusLabel(payment.status),
    paidAt: payment.paid_at ? formatDate(payment.paid_at) : undefined, // Data de pagamento
    dueDate: payment.due_date ? formatDate(payment.due_date) : undefined, // Data de vencimento
    description: payment.description || undefined,
  }));
}

/**
 * Converter dados de despesas do banco para o formato do componente ExpenseTransaction
 */
function convertExpensesToExpenseTransactions(expenses: any[]): ExpenseTransaction[] {
  return expenses.map((expense, index) => ({
    id: expense.id || index.toString(),
    transactionType: 'expense' as const,
    supplierName: expense.supplier_name,
    categoryName: expense.expense_categories?.name || 'Sem categoria',
    categoryColor: expense.expense_categories?.color || '#6B7280',
    type: getExpenseTypeLabel(expense.description),
    paymentMethod: expense.payment_methods?.name || 'Não definido',
    date: formatDate(expense.due_date || expense.created_at),
    amount: formatCurrency(parseFloat(expense.amount)),
    status: getExpenseStatusLabel(expense.status),
    paidAt: expense.paid_at ? formatDate(expense.paid_at) : undefined,
    dueDate: expense.due_date ? formatDate(expense.due_date) : undefined,
    description: expense.description || undefined,
  }));
}

/**
 * Converter métricas de despesas para o formato do componente
 */
function convertExpenseMetricsToDisplay(metrics: any): FinancialMetric[] {
  return [
    {
      value: formatCurrency(metrics.totalExpenses || 0),
      label: 'Total Gasto',
      color: 'red',
    },
    {
      value: `${(metrics.paidExpenses || 0)} (${formatCurrency(metrics.paidAmount || 0)})`,
      label: 'Despesas Pagas',
      color: 'blue',
    },
    {
      value: `${(metrics.pendingExpenses || 0)} (${formatCurrency(metrics.pendingAmount || 0)})`,
      label: 'Pendentes',
      color: 'orange',
    },
    {
      value: `${(metrics.overdueExpenses || 0)} (${formatCurrency(metrics.overdueAmount || 0)})`,
      label: 'Vencidas',
      color: 'red',
    },
  ];
}

/**
 * Obter label do tipo de pagamento
 */
function getPaymentTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    recurring: 'Mensalidade',
    signup_fee: 'Taxa de Matrícula',
    graduation_fee: 'Taxa de Graduação',
    late_fee: 'Multa por Atraso',
    cancellation_fee: 'Taxa de Cancelamento',
    manual: 'Cobrança Manual',
    product: 'Produto/Serviço',
    initial_payment: 'Pagamento Inicial'
  };
  return labels[type] || type;
}

/**
 * Obter label do método de pagamento
 */
function getPaymentMethodLabel(method: string | null): string {
  if (!method) return 'Não definido';

  const labels: Record<string, string> = {
    pix: 'PIX',
    credit_card: 'Cartão de Crédito',
    debit_card: 'Cartão de Débito',
    cash: 'Dinheiro',
    bank_transfer: 'Transferência Bancária'
  };
  return labels[method] || method;
}

/**
 * Obter label do status
 */
function getStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    pending: 'Pendente',
    paid: 'Pago',
    overdue: 'Vencido',
    canceled: 'Cancelado',
    awaiting_confirmation: 'Aguardando Confirmação'
  };
  return labels[status] || status;
}

/**
 * Obter label do tipo de despesa baseado na descrição
 */
function getExpenseTypeLabel(description: string | null): string {
  if (!description) return 'Despesa';

  // Extrair tipo da descrição ou usar a própria descrição
  const words = description.split(' ');
  return words.length > 3 ? `${words.slice(0, 3).join(' ')}...` : description;
}

/**
 * Obter label do status de despesa
 */
function getExpenseStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    pending: 'Pendente',
    paid: 'Pago',
    overdue: 'Vencido',
    canceled: 'Cancelado'
  };
  return labels[status] || status;
}

/**
 * Formatar data para exibição (timezone do Brasil)
 */
function formatDate(dateString: string): string {
  if (!dateString) return 'Data não disponível';

  // Se for apenas uma data (YYYY-MM-DD), adicionar horário para evitar problemas de timezone
  const dateToFormat = dateString.includes('T') ? dateString : `${dateString}T12:00:00.000Z`;

  const date = new Date(dateToFormat);

  // Verificar se a data é válida
  if (isNaN(date.getTime())) {
    return 'Data inválida';
  }

  return date.toLocaleDateString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

interface PagamentosPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function PagamentosPage({ searchParams }: PagamentosPageProps) {
  // Aguardar searchParams antes de usar
  const params = await searchParams;

  // Determinar tab inicial dos URL params
  const getInitialTab = (): 'income' | 'expense' => {
    const tabParam = params.tab;
    if (tabParam === 'income' || tabParam === 'expense') {
      return tabParam;
    }
    return 'income'; // Padrão
  };

  const initialTab = getInitialTab();

  // Buscar métricas reais usando a action existente
  const metricsResult = await getPaymentMetrics();

  // Buscar pagamentos com filtro padrão (status "paid") para coincidir com o filtro padrão
  const paymentsResult = await getAllPayments({
    limit: 20,
    offset: 0,
    status: 'paid' // Filtro padrão para coincidir com useFinancialFilters
  });

  // Buscar despesas reais
  const expensesResult = await getExpenses({
    limit: 20,
    offset: 0,
    status: 'paid' // Filtro padrão para coincidir com useFinancialFilters
  });

  // Buscar métricas de despesas
  const expenseMetricsResult = await getExpenseMetrics();

  // Buscar formas de pagamento
  const paymentMethods = await getPaymentMethods();

  // Usar métricas padrão em caso de erro
  const defaultMetrics: PaymentMetric[] = [
    {
      value: 'R$ 0,00',
      label: 'Total Recebido',
      color: 'green',
    },
    {
      value: '0',
      label: 'Pagamentos',
      color: 'blue',
    },
    {
      value: '0',
      label: 'Pendentes',
      color: 'orange',
    },
    {
      value: '0',
      label: 'Em Atraso',
      color: 'red',
    },
  ];

  const defaultExpenseMetrics: FinancialMetric[] = [
    {
      value: 'R$ 0,00',
      label: 'Total Gasto',
      color: 'red',
    },
    {
      value: '0 (R$ 0,00)',
      label: 'Despesas Pagas',
      color: 'blue',
    },
    {
      value: '0 (R$ 0,00)',
      label: 'Pendentes',
      color: 'orange',
    },
    {
      value: '0 (R$ 0,00)',
      label: 'Vencidas',
      color: 'red',
    },
  ];

  const metrics = metricsResult.success && metricsResult.data
    ? convertMetricsToDisplay(metricsResult.data)
    : defaultMetrics;

  const expenseMetrics = expenseMetricsResult.success && expenseMetricsResult.data
    ? convertExpenseMetricsToDisplay(expenseMetricsResult.data)
    : defaultExpenseMetrics;

  // Converter pagamentos para o formato esperado pelos componentes
  const transactions = paymentsResult.success && paymentsResult.data
    ? convertPaymentsToIncomeTransactions(paymentsResult.data)
    : [];

  // Converter despesas para o formato esperado pelos componentes
  const expenseTransactions = expensesResult.success && expensesResult.data
    ? convertExpensesToExpenseTransactions(expensesResult.data)
    : [];

  // Verificar se há mais dados para carregar
  const hasMore = paymentsResult.success ? paymentsResult.hasMore || false : false;
  const hasMoreExpenses = expensesResult.success ? expensesResult.hasMore || false : false;

  return (
    <>
      <PageHeader
        title="Financeiro"
        description="Gestão completa das finanças da academia - receitas e despesas."
      />

      <FinancialClient
        initialTab={initialTab}
        incomeMetrics={metrics}
        incomeTransactions={transactions}
        expenseMetrics={expenseMetrics}
        expenseTransactions={expenseTransactions}
        hasMoreIncome={hasMore}
        hasMoreExpenses={hasMoreExpenses}
        paymentMethods={paymentMethods}
      />
    </>
  );
}