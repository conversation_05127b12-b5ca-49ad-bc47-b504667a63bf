'use client';

import { useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Building, Tag, FileText, Plus } from 'lucide-react';
import { DespesaFormValues } from '../../schemas/despesa-schema';
import { ExpenseCategory } from '../../../components/types';
import { AddCategoryModal } from '../AddCategoryModal';

interface BasicInfoSectionProps {
  categories: ExpenseCategory[];
}

export function BasicInfoSection({ categories }: BasicInfoSectionProps) {
  const form = useFormContext<DespesaFormValues>();
  const [localCategories, setLocalCategories] = useState<ExpenseCategory[]>(categories);
  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);

  // Sincronizar estado local com as categorias recebidas
  useEffect(() => {
    setLocalCategories(categories);
  }, [categories]);

  const handleAddCategory = (newCategory: ExpenseCategory) => {
    setLocalCategories(prev => [...prev, newCategory]);
    // Automaticamente selecionar a nova categoria
    form.setValue('categoryId', newCategory.id);
  };
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Informações Básicas
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Dados principais da despesa
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Nome do Fornecedor */}
        <FormField
          control={form.control}
          name="supplierName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Nome do Fornecedor
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <Building className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    {...field}
                    placeholder="Ex: João Silva - Instrutor"
                    className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Categoria */}
        <FormField
          control={form.control}
          name="categoryId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Categoria
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
                    <Tag className="h-4 w-4 text-gray-400" />
                  </div>
                  <Select
                    onValueChange={(value) => {
                      if (value === 'add-new') {
                        setIsAddCategoryModalOpen(true);
                      } else {
                        field.onChange(value);
                      }
                    }}
                    value={field.value}
                  >
                    <SelectTrigger className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500">
                      <SelectValue placeholder="Selecione uma categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      {localCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: category.color }}
                            />
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}

                      {/* Separador */}
                      {localCategories.length > 0 && (
                        <div className="border-t border-gray-200 dark:border-gray-700 my-1" />
                      )}

                      {/* Opção para adicionar nova categoria */}
                      <SelectItem value="add-new" className="text-tenant-primary">
                        <div className="flex items-center space-x-2">
                          <Plus className="w-3 h-3" />
                          <span>Adicionar nova categoria</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tipo da Despesa */}
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem className="md:col-span-2">
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Tipo da Despesa
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Ex: Salário Mensal, Aluguel, Equipamentos"
                  className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Modal para adicionar nova categoria */}
      <AddCategoryModal
        open={isAddCategoryModalOpen}
        onOpenChange={setIsAddCategoryModalOpen}
        onCategoryAdd={handleAddCategory}
      />
    </div>
  );
}
