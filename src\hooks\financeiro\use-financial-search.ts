import { useState, useCallback } from 'react';
import { getAllPayments } from '@/services/billing/payment-actions';
import { getExpenses } from '@/app/(dashboard)/financeiro/pagamentos/actions';
import { FinancialFilterState, TransactionType } from '@/app/(dashboard)/financeiro/pagamentos/components/types';

export function useFinancialSearch() {
  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const performSearch = useCallback(async (
    searchValue: string,
    offset = 0,
    filters?: FinancialFilterState,
    transactionType: TransactionType = 'income'
  ) => {
    try {
      setIsSearching(true);

      // Construir parâmetros de busca com base nos filtros
      const searchParams: any = {
        limit: 20,
        offset,
        searchText: searchValue.trim() || undefined,
        transactionType // Adicionar tipo de transação
      };

      // Aplicar filtros se fornecidos
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          // Suporte a múltiplos status
          searchParams.status = filters.status.length === 1 ? filters.status[0] : filters.status;
        }

        if (filters.paymentType && filters.paymentType.length > 0) {
          // Suporte a múltiplos tipos
          searchParams.paymentType = filters.paymentType.length === 1 ? filters.paymentType[0] : filters.paymentType;
        }

        if (filters.paymentMethod && filters.paymentMethod.length > 0) {
          // Suporte a múltiplos métodos
          searchParams.paymentMethod = filters.paymentMethod.length === 1 ? filters.paymentMethod[0] : filters.paymentMethod;
        }

        // Filtro específico para despesas - categorias
        if (filters.categories && filters.categories.length > 0 && transactionType === 'expense') {
          searchParams.categories = filters.categories.length === 1 ? filters.categories[0] : filters.categories;
        }

        if (filters.minAmount) {
          searchParams.minAmount = filters.minAmount;
        }

        if (filters.maxAmount) {
          searchParams.maxAmount = filters.maxAmount;
        }

        if (filters.startDate) {
          searchParams.startDate = filters.startDate.toISOString().split('T')[0];
        }

        if (filters.endDate) {
          searchParams.endDate = filters.endDate.toISOString().split('T')[0];
        }
      }

      // Para receitas, usar a função existente de pagamentos
      if (transactionType === 'income') {
        const result = await getAllPayments(searchParams);
        return result;
      } else {
        // Para despesas, usar a nova função de busca de despesas
        const expenseParams: any = {
          limit: searchParams.limit,
          offset: searchParams.offset,
          search: searchParams.searchText
        };

        // Mapear filtros para parâmetros de despesas
        if (filters?.status && filters.status.length > 0) {
          // Suporte a múltiplos status
          expenseParams.status = filters.status.length === 1 ? filters.status[0] : filters.status;
        }

        if (filters?.categories && filters.categories.length > 0) {
          // Suporte a múltiplas categorias
          expenseParams.category_id = filters.categories.length === 1 ? filters.categories[0] : filters.categories;
        }

        if (filters?.paymentMethod && filters.paymentMethod.length > 0) {
          // Suporte a múltiplos métodos de pagamento
          expenseParams.payment_method_id = filters.paymentMethod.length === 1 ? filters.paymentMethod[0] : filters.paymentMethod;
        }

        if (filters?.startDate) {
          expenseParams.start_date = filters.startDate.toISOString().split('T')[0];
        }

        if (filters?.endDate) {
          expenseParams.end_date = filters.endDate.toISOString().split('T')[0];
        }

        const result = await getExpenses(expenseParams);
        return result;
      }
    } catch (error) {
      console.error('Erro ao buscar transações financeiras:', error);
      return {
        success: false,
        error: 'Erro interno ao buscar transações',
        data: null,
        hasMore: false
      };
    } finally {
      setIsSearching(false);
    }
  }, []);

  const updateSearchText = useCallback((text: string) => {
    setSearchText(text);
  }, []);

  return {
    searchText,
    isSearching,
    performSearch,
    updateSearchText,
    setSearchText
  };
}
