/**
 * Utilitários para formatação de valores das configurações gerais
 */

/**
 * Formata o código da moeda para exibição
 */
export function formatCurrency(currency: string): string {
  switch (currency.toUpperCase()) {
    case 'BRL':
      return 'Real Brasileiro (BRL)';
    case 'USD':
      return 'Dólar Americano (USD)';
    case 'EUR':
      return 'Euro (EUR)';
    default:
      return currency.toUpperCase();
  }
}

/**
 * Formata o dia de vencimento para exibição
 */
export function formatDueDay(dueDay: string): string {
  if (dueDay === 'enrollment_date') {
    return 'Na data de matrícula';
  }
  
  // Verificar se é um número válido
  const dayNumber = parseInt(dueDay, 10);
  if (!isNaN(dayNumber) && dayNumber >= 1 && dayNumber <= 31) {
    return `Dia ${dayNumber}`;
  }
  
  return dueDay;
}

/**
 * Lista de opções de moeda disponíveis
 */
export const CURRENCY_OPTIONS = [
  { value: 'BRL', label: 'Real Brasileiro (BRL)' },
  { value: 'USD', label: 'Dólar Americano (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
] as const;

/**
 * Lista de opções de dia de vencimento
 */
export const DUE_DAY_OPTIONS = [
  { value: 'enrollment_date', label: 'Na data de matrícula' },
  { value: '1', label: 'Dia 1' },
  { value: '5', label: 'Dia 5' },
  { value: '10', label: 'Dia 10' },
  { value: '15', label: 'Dia 15' },
  { value: '20', label: 'Dia 20' },
  { value: '25', label: 'Dia 25' },
  { value: '30', label: 'Dia 30' },
] as const;
