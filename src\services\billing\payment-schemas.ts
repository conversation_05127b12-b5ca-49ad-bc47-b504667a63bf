/**
 * Schemas de Validação para o Sistema de Cobranças
 * Baseado no documento: docs/planejamento-sistema-cobrancas.md
 */

import { z } from 'zod'

// Enum schemas
export const paymentTypeSchema = z.enum([
  'recurring',
  'signup_fee',
  'graduation_fee',
  'late_fee',
  'cancellation_fee',
  'manual',
  'product'
])

export const paymentStatusSchema = z.enum([
  'pending',
  'paid',
  'overdue',
  'canceled',
  'awaiting_confirmation'
])

export const paymentManagementModalSchema = z
  .object({
    amount: z.string().min(1, 'Valor é obrigatório.'),
    status: z.enum(['pending', 'paid', 'overdue', 'cancelled', 'awaiting_confirmation']),
    payment_method: z.string().optional(),
    paid_at: z.date().optional(),
    due_date: z.date({
      required_error: 'Data de vencimento é obrigatória.',
    }),
    description: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.status === 'paid') {
      if (!data.paid_at) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['paid_at'],
          message: 'Data do pagamento é obrigatória.',
        })
      }
      if (!data.payment_method || data.payment_method.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['payment_method'],
          message: 'Método de pagamento é obrigatório.',
        })
      }
    }

    if (data.status === 'overdue') {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (data.due_date >= today) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['status'],
          message:
            'O status só pode ser "Atrasado" se a data de vencimento já passou.',
        })
      }
    }

    // Validação para status pendente com data de vencimento no passado
    if (data.status === 'pending') {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (data.due_date < today) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['due_date'],
          message:
            'Pagamentos pendentes não podem ter data de vencimento no passado. Considere alterar o status para "Atrasado" ou ajustar a data de vencimento.',
        })
      }
    }
  })

export type PaymentManagementModalData = z.infer<
  typeof paymentManagementModalSchema
>

// Schema para criar cobrança manual (conforme documento)
export const criarCobrancaManualSchema = z.object({
  alunoId: z.string().uuid('ID do aluno deve ser um UUID válido'),
  valor: z.number().positive('Valor deve ser maior que zero'),
  descricao: z.string().min(1, 'Descrição é obrigatória'),
  dataVencimento: z.string().date('Data de vencimento deve ser uma data válida').optional(),
  metadata: z.record(z.any()).optional().default({})
})

// Schema para criar taxa de graduação (conforme documento)
export const criarTaxaGraduacaoSchema = z.object({
  alunoId: z.string().uuid('ID do aluno deve ser um UUID válido'),
  beltLevelId: z.string().uuid('ID do nível da faixa deve ser um UUID válido'),
  graduationId: z.string().uuid('ID da graduação deve ser um UUID válido')
})

// Schema para criar taxa de inscrição
export const criarTaxaInscricaoSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula deve ser um UUID válido')
})

// Schema para criar taxa de cancelamento
export const criarTaxaCancelamentoSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula deve ser um UUID válido'),
  motivo: z.string().optional()
})

// Schema para atualizar status de pagamento (conforme documento)
export const atualizarStatusPagamentoSchema = z.object({
  paymentId: z.string().uuid('ID do pagamento deve ser um UUID válido'),
  novoStatus: paymentStatusSchema,
  motivo: z.string().optional()
})

// Schema para atualizar detalhes de pagamento (valor, status, método, datas, descrição)
export const atualizarDetalhesPagamentoSchema = z.object({
  paymentId: z.string().uuid('ID do pagamento deve ser um UUID válido'),
  amount: z.number().positive().optional(),
  status: paymentStatusSchema.optional(),
  payment_method: z.string().optional().nullable(),
  paid_at: z.string().datetime().optional().nullable(),
  due_date: z.string().date().optional(),
  description: z.string().optional()
})

// Schema para buscar pagamentos por estudante
export const buscarPagamentosPorAlunoSchema = z.object({
  studentId: z.string().uuid('ID do estudante deve ser um UUID válido'),
  limit: z.number().int().positive().max(100).optional().default(10),
  offset: z.number().int().min(0).optional().default(0)
})

// Schema para buscar pagamentos por membership
export const buscarPagamentosPorMembershipSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula deve ser um UUID válido'),
  limit: z.number().int().positive().max(100).optional().default(10),
  offset: z.number().int().min(0).optional().default(0)
})

// Schema para buscar todos os pagamentos
export const buscarTodosPagamentosSchema = z.object({
  tenantId: z.string().uuid('ID do tenant deve ser um UUID válido').optional(),
  limit: z.number().int().positive().max(100).optional().default(20),
  offset: z.number().int().min(0).optional().default(0),
  status: z.union([paymentStatusSchema, z.array(paymentStatusSchema)]).optional(),
  paymentType: z.union([paymentTypeSchema, z.array(paymentTypeSchema)]).optional(),
  paymentMethod: z.union([z.string(), z.array(z.string())]).optional(),
  studentId: z.string().uuid('ID do estudante deve ser um UUID válido').optional(),
  startDate: z.string().date('Data de início deve ser uma data válida').optional(),
  endDate: z.string().date('Data de fim deve ser uma data válida').optional(),
  minAmount: z.number().positive('Valor mínimo deve ser maior que zero').optional(),
  maxAmount: z.number().positive('Valor máximo deve ser maior que zero').optional(),
  searchText: z.string().min(1, 'Texto de busca deve ter pelo menos 1 caractere').optional()
})

// Schema para processar pagamentos em atraso
export const processarPagamentosAtrasadosSchema = z.object({
  tenantId: z.string().uuid('ID do tenant deve ser um UUID válido').optional()
})

// Schema para métricas de pagamentos
export const metricasPagamentosSchema = z.object({
  startDate: z.string().date('Data de início deve ser uma data válida').optional(),
  endDate: z.string().date('Data de fim deve ser uma data válida').optional(),
  paymentType: paymentTypeSchema.optional(),
  status: paymentStatusSchema.optional()
})

// Schema para relatórios de pagamentos
export const relatoriosPagamentosSchema = z.object({
  startDate: z.string().date('Data de início deve ser uma data válida'),
  endDate: z.string().date('Data de fim deve ser uma data válida'),
  paymentType: paymentTypeSchema.optional(),
  status: paymentStatusSchema.optional(),
  studentId: z.string().uuid().optional(),
  membershipId: z.string().uuid().optional()
})

// Schema para configuração de cobrança recorrente
export const configuracaoCobrancaRecorrenteSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula deve ser um UUID válido'),
  amount: z.number().positive('Valor deve ser maior que zero'),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
  nextBillingDate: z.string().date('Data da próxima cobrança deve ser uma data válida')
})

// Schema para configuração de multa por atraso
export const configuracaoMultaAtrasoSchema = z.object({
  enabled: z.boolean(),
  amount: z.number().positive().optional(),
  percentage: z.number().min(0).max(100).optional(),
  grace_days: z.number().int().min(0),
  max_fee: z.number().positive().optional()
}).refine(
  (data) => data.enabled ? (data.amount !== undefined || data.percentage !== undefined) : true,
  {
    message: "Quando multa está habilitada, deve ter valor fixo ou percentual definido",
    path: ["amount"]
  }
)

// Tipos inferidos dos schemas
export type CriarCobrancaManualData = z.infer<typeof criarCobrancaManualSchema>
export type CriarTaxaGraduacaoData = z.infer<typeof criarTaxaGraduacaoSchema>
export type CriarTaxaInscricaoData = z.infer<typeof criarTaxaInscricaoSchema>
export type CriarTaxaCancelamentoData = z.infer<typeof criarTaxaCancelamentoSchema>
export type AtualizarStatusPagamentoData = z.infer<typeof atualizarStatusPagamentoSchema>
export type AtualizarDetalhesPagamentoData = z.infer<typeof atualizarDetalhesPagamentoSchema>
export type BuscarPagamentosPorAlunoData = z.infer<typeof buscarPagamentosPorAlunoSchema>
export type BuscarPagamentosPorMembershipData = z.infer<typeof buscarPagamentosPorMembershipSchema>
export type BuscarTodosPagamentosData = z.infer<typeof buscarTodosPagamentosSchema>
export type ProcessarPagamentosAtrasadosData = z.infer<typeof processarPagamentosAtrasadosSchema>
export type MetricasPagamentosData = z.infer<typeof metricasPagamentosSchema>
export type RelatoriosPagamentosData = z.infer<typeof relatoriosPagamentosSchema>
export type ConfiguracaoCobrancaRecorrenteData = z.infer<typeof configuracaoCobrancaRecorrenteSchema>
export type ConfiguracaoMultaAtrasoData = z.infer<typeof configuracaoMultaAtrasoSchema>

// Schema para validação de pagamento completo
export const paymentSchema = z.object({
  id: z.string().uuid(),
  tenant_id: z.string().uuid(),
  student_id: z.string().uuid(),
  amount: z.number().positive(),
  currency: z.string().default('BRL'),
  status: paymentStatusSchema,
  payment_method: z.string().optional(),
  paid_at: z.string().datetime().optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime().optional(),
  membership_id: z.string().uuid().optional(),
  payment_type: paymentTypeSchema,
  description: z.string().optional(),
  due_date: z.string().date().optional(),
  metadata: z.record(z.any()).default({}),
  reference_id: z.string().uuid().optional(),
  billing_cycle: z.string().optional(),
  attempt_count: z.number().int().min(0).default(0),
  last_attempt_at: z.string().datetime().optional()
})

export type PaymentData = z.infer<typeof paymentSchema>
