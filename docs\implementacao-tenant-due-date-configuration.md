# Implementação da Configuração de Data de Vencimento por Tenant

## Resumo

Este documento descreve a implementação da funcionalidade que permite configurar datas de vencimento personalizadas por tenant (academia) no sistema de cobrança.

## Funcionalidade Implementada

### Configurações Disponíveis

A coluna `default_due_day` na tabela `tenant_general_payment_settings` agora suporta os seguintes valores:

- **`enrollment_date`**: Data de vencimento na data da matrícula (comportamento padrão anterior)
- **Dias específicos**: `1`, `5`, `10`, `15`, `20`, `25`, `30` - Dia específico do mês para vencimento

### Função Auxiliar Criada

#### `calculate_due_date_for_tenant(p_tenant_id UUID, p_base_date DATE)`

**Propósito**: Calcular a data de vencimento baseada nas configurações do tenant.

**Lógica**:
1. Busca a configuração `default_due_day` do tenant
2. Se `enrollment_date`: retorna a data base fornecida
3. Se dia específico (1-31):
   - Se o dia atual ≤ dia configurado: usa o mês atual
   - Se o dia atual > dia configurado: usa o próximo mês
   - Trata casos especiais (ex: dia 31 em fevereiro)

**Exemplos**:
```sql
-- Tenant configurado para dia 5, data base 03/01/2024
-- Resultado: 05/01/2024 (mesmo mês)

-- Tenant configurado para dia 5, data base 10/01/2024  
-- Resultado: 05/02/2024 (próximo mês)

-- Tenant configurado para enrollment_date, data base 15/01/2024
-- Resultado: 15/01/2024 (mesma data)
```

## Funções RPCs Atualizadas

### 1. `create_recurring_payments`
- **Mudança**: Usa `calculate_due_date_for_tenant` para calcular a primeira data de vencimento
- **Impacto**: Pagamentos recorrentes agora respeitam a configuração do tenant

### 2. `create_initial_payment`
- **Mudança**: Usa `calculate_due_date_for_tenant` para calcular a data de vencimento
- **Impacto**: Pagamentos iniciais agora respeitam a configuração do tenant

### 3. `create_signup_fee_payment`
- **Mudança**: Usa `calculate_due_date_for_tenant` para calcular a data de vencimento
- **Impacto**: Taxas de inscrição agora respeitam a configuração do tenant

### 4. `process_membership_billing`
- **Mudança**: Usa `calculate_due_date_for_tenant` para calcular a data de vencimento
- **Impacto**: Processamento de cobrança recorrente agora respeita a configuração do tenant

## Compatibilidade

### Backward Compatibility
- **Mantida**: Tenants sem configuração específica continuam usando `enrollment_date`
- **Mantida**: Comportamento anterior permanece inalterado para configuração `enrollment_date`

### Actions e Serviços
- **Não requer mudanças**: Actions em `membership-actions.ts` e `plan-actions.ts` continuam funcionando
- **Não requer mudanças**: Serviços em `payment-service.ts` continuam funcionando
- **Transparente**: A mudança é transparente para o código cliente

## Interface de Configuração

### Opções Disponíveis (formatters.ts)
```typescript
export const DUE_DAY_OPTIONS = [
  { value: 'enrollment_date', label: 'Na data de matrícula' },
  { value: '1', label: 'Dia 1' },
  { value: '5', label: 'Dia 5' },
  { value: '10', label: 'Dia 10' },
  { value: '15', label: 'Dia 15' },
  { value: '20', label: 'Dia 20' },
  { value: '25', label: 'Dia 25' },
  { value: '30', label: 'Dia 30' },
] as const;
```

### Formatação para Exibição
```typescript
export function formatDueDay(dueDay: string): string {
  if (dueDay === 'enrollment_date') {
    return 'Na data de matrícula';
  }
  
  const dayNumber = parseInt(dueDay, 10);
  if (!isNaN(dayNumber) && dayNumber >= 1 && dayNumber <= 31) {
    return `Dia ${dayNumber}`;
  }
  
  return dueDay;
}
```

## Migrations Aplicadas

1. **`add_tenant_due_date_calculation_functions`**: Criação da função auxiliar
2. **`update_create_recurring_payments_with_tenant_due_date`**: Atualização da função de pagamentos recorrentes
3. **`update_create_initial_payment_with_tenant_due_date`**: Atualização da função de pagamento inicial
4. **`update_create_signup_fee_payment_with_tenant_due_date`**: Atualização da função de taxa de inscrição
5. **`update_process_membership_billing_with_tenant_due_date`**: Atualização da função de processamento de cobrança

### Correções de Compatibilidade (search_path)

6. **`fix_calculate_due_date_function_calls`**: Correção das chamadas para função auxiliar
7. **`fix_create_initial_payment_function_call`**: Correção de referências de schema
8. **`fix_create_signup_fee_payment_function_call`**: Correção de referências de schema
9. **`fix_process_membership_billing_function_call`**: Correção de referências de schema
10. **`fix_create_signup_fee_payment_table_references`**: Correção de referências de tabelas
11. **`fix_update_student_financial_status_table_references`**: Correção de função trigger
12. **`fix_update_payments_search_vector_table_references`**: Correção de função de busca
13. **`fix_trigger_update_student_financial_status_function_call`**: Correção de chamada de função no trigger

### Correção do next_billing_date

14. **`fix_create_recurring_payments_update_next_billing_date`**: Correção para atualizar next_billing_date na tabela memberships
15. **`update_create_next_recurring_payment_with_tenant_due_date`**: Atualização da função de criação automática de próximos pagamentos

## Testes Realizados

### Cenários Testados
- ✅ Configuração `enrollment_date` mantém comportamento anterior
- ✅ Dia específico antes do dia atual usa mês atual
- ✅ Dia específico após o dia atual usa próximo mês
- ✅ Dia específico igual ao dia atual usa mês atual
- ✅ Tratamento de dias inexistentes (ex: 31 de fevereiro)

### Resultados dos Testes
```sql
-- Configuração: dia 5, data base: 03/01/2024 → 05/01/2024 ✅
-- Configuração: dia 5, data base: 10/01/2024 → 05/02/2024 ✅
-- Configuração: dia 5, data base: 05/01/2024 → 05/01/2024 ✅
```

### Teste de Integração Completo
```sql
-- Teste realizado com membership real: c0103f6f-7ab7-466c-a9ad-9cc52625d2b9
-- Configuração: enrollment_date
-- Data atual: 19/07/2025
-- Resultado: Pagamento criado com due_date = 2025-08-15 ✅

-- Teste com configuração dia 5:
-- Data atual: 19/07/2025 (já passou o dia 5)
-- Resultado: calculate_due_date_for_tenant → 2025-08-05 ✅
```

### Teste de Atualização do next_billing_date
```sql
-- Membership antes: next_billing_date = 2025-08-19
-- Após create_recurring_payments:
-- - Pagamento criado: due_date = 2025-09-15
-- - Membership atualizada: next_billing_date = 2025-10-15 ✅
-- - updated_at atualizado corretamente ✅
```

## Próximos Passos

1. **Validação em Produção**: Testar com dados reais de diferentes tenants
2. **Monitoramento**: Acompanhar se as datas estão sendo calculadas corretamente
3. **Documentação do Usuário**: Criar guia para configuração das datas de vencimento
4. **Relatórios**: Verificar se relatórios financeiros refletem as novas datas corretamente
