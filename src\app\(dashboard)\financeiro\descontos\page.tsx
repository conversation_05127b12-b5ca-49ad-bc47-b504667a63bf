import { Metadata } from 'next';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Percent, Plus, Edit, Trash2 } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Descontos - Financeiro',
  description: 'Configuração de descontos e códigos promocionais',
};

export default function DescontosPage() {
  return (
    <>
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Novo Desconto
          </Button>
        </div>
      </div>

      {/* Métricas de descontos */}
      <div className="grid gap-4 md:grid-cols-3 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">12</p>
              <p className="text-xs text-muted-foreground">Descontos Ativos</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">R$ 2.340,00</p>
              <p className="text-xs text-muted-foreground">Total Economizado</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">89</p>
              <p className="text-xs text-muted-foreground">Usos Este Mês</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de descontos */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Percent className="h-5 w-5 text-purple-600" />
            <CardTitle>Descontos Configurados</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Exemplos de descontos */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <Percent className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="font-medium">Estudante</p>
                  <p className="text-sm text-muted-foreground">20% de desconto para estudantes</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">20%</Badge>
                <Badge variant="outline" className="text-green-600">Ativo</Badge>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <Percent className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="font-medium">Família</p>
                  <p className="text-sm text-muted-foreground">15% para segundo familiar</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">15%</Badge>
                <Badge variant="outline" className="text-green-600">Ativo</Badge>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <Percent className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="font-medium">Primeira Matrícula</p>
                  <p className="text-sm text-muted-foreground">50% no primeiro mês</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">50%</Badge>
                <Badge variant="outline" className="text-red-600">Inativo</Badge>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
} 