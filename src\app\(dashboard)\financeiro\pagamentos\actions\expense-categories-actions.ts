'use server';

import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { ExpenseCategory } from '../components/types';
import { z } from 'zod';

// Schema de validação para criar categoria
const createCategorySchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  description: z.string().optional(),
  color: z.string().min(1, 'Selecione uma cor'),
});

export type CreateCategoryInput = z.infer<typeof createCategorySchema>;

/**
 * Buscar categorias de despesas do tenant atual
 */
export async function getExpenseCategories(): Promise<ExpenseCategory[]> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      console.error('Usuário não autenticado');
      return [];
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      console.error('Tenant não identificado');
      return [];
    }

    const supabase = await createClient();

    const { data, error } = await supabase
      .from('expense_categories')
      .select('id, name, description, color')
      .eq('tenant_id', tenantId)
      .order('name');

    if (error) {
      console.error('Erro ao buscar categorias de despesas:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Erro ao buscar categorias de despesas:', error);
    return [];
  }
}

/**
 * Criar nova categoria de despesa
 */
export async function createExpenseCategory(input: CreateCategoryInput): Promise<{
  success: boolean;
  data?: ExpenseCategory;
  error?: string;
}> {
  try {
    // Validar dados de entrada
    const validationResult = createCategorySchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Dados inválidos: ' + validationResult.error.errors.map(e => e.message).join(', ')
      };
    }

    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    const tenantId = user.app_metadata?.tenant_id;

    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não identificado'
      };
    }

    const supabase = await createClient();

    // Verificar se já existe uma categoria com o mesmo nome para este tenant
    const { data: existingCategory } = await supabase
      .from('expense_categories')
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('name', validationResult.data.name)
      .single();

    if (existingCategory) {
      return {
        success: false,
        error: 'Já existe uma categoria com este nome'
      };
    }

    // Criar nova categoria
    const { data, error } = await supabase
      .from('expense_categories')
      .insert({
        tenant_id: tenantId,
        name: validationResult.data.name,
        description: validationResult.data.description || null,
        color: validationResult.data.color,
      })
      .select('id, name, description, color')
      .single();

    if (error) {
      console.error('Erro ao criar categoria de despesa:', error);
      return {
        success: false,
        error: 'Erro ao criar categoria. Tente novamente.'
      };
    }

    return {
      success: true,
      data: data as ExpenseCategory
    };

  } catch (error) {
    console.error('Erro ao criar categoria de despesa:', error);
    return {
      success: false,
      error: 'Erro interno do servidor'
    };
  }
}
