---
description: 
globs: 
alwaysApply: true
---
# Implementação Incremental e Estratégia de Testes

## Abordagem Incremental

### Divisão de Tarefas
- Divida implementações complexas em pequenas tarefas independentes
- Estabeleça ordem de implementação que permita validação progressiva
- Certifique-se que cada incremento adiciona valor testável
- Priorize a implementação de interfaces e contratos antes das implementações concretas

### Ciclo de Desenvolvimento
1. **Planejamento**: Defina claramente os requisitos da etapa atual
2. **Implementação**: Escreva o código mínimo necessário para esta etapa
3. **Testes**: Implemente testes específicos para validar a funcionalidade
4. **Revisão**: Avalie o código e os resultados dos testes
5. **Refatoração**: Melhore o código sem alterar seu comportamento externo

## Estratégias de Teste

### Níveis de Teste
- **Testes Unitários**: Para funções e componentes isolados
  - Utilize `jest` e `@testing-library/react` para componentes React
  - Aplique mocks para isolar dependências externas
- **Testes de Integração**: Para verificar interações entre componentes
  - Teste fluxos de dados entre componentes e serviços
  - Verifique integração com APIs e serviços externos
- **Testes End-to-End**: Para validar fluxos completos
  - Implemente com Cypress ou Playwright
  - Simule interações reais do usuário

### Boas Práticas de Testes
- Escreva testes antes ou em paralelo com a implementação
- Mantenha testes independentes entre si
- Evite testes frágeis que quebram com mudanças irrelevantes
- Teste tanto casos de sucesso quanto de falha
- Prefira assertivas específicas sobre comportamento esperado

## Padrões de Implementação

### Para Server Actions
```tsx
// 1. Primeiro, implemente a validação de entrada
'use server';
import { z } from 'zod';

const inputSchema = z.object({
  // definição do schema
});

export async function serverAction(data: unknown) {
  const result = inputSchema.safeParse(data);
  if (!result.success) {
    return { success: false, errors: result.error.format() };
  }
  
  // implementação inicial mínima
  return { success: true, data: {} };
}

// 2. Adicione testes para validação
// __tests__/actions/server-action.test.ts
import { serverAction } from '@/app/actions/server-action';

describe('serverAction validação', () => {
  it('deve rejeitar entradas inválidas', async () => {
    const resultado = await serverAction({});
    expect(resultado.success).toBe(false);
  });
});

// 3. Implemente a lógica de negócios
export async function serverAction(data: unknown) {
  // ... existing code ...
  
  try {
    // implementação da lógica de negócios
    return { success: true, data: { /* resultado */ } };
  } catch (error) {
    return { 
      success: false, 
      errors: { _form: "Erro ao processar operação" } 
    };
  }
}

// 4. Testes da lógica de negócios
describe('serverAction lógica', () => {
  it('deve processar dados válidos corretamente', async () => {
    const dadosValidos = { /* dados válidos */ };
    const resultado = await serverAction(dadosValidos);
    expect(resultado.success).toBe(true);
    expect(resultado.data).toBeDefined();
  });
});
```

### Para Componentes React
```tsx
// 1. Primeiro, implemente a estrutura básica
// src/components/Feature/FeatureComponent.tsx
'use client';
import { useState } from 'react';

export function FeatureComponent() {
  const [estado, setEstado] = useState(initialState);
  
  return (
    <div>
      {/* interface mínima funcional */}
    </div>
  );
}

// 2. Testes para estrutura básica
// __tests__/components/Feature/FeatureComponent.test.tsx
import { render, screen } from '@testing-library/react';
import { FeatureComponent } from '@/components/Feature/FeatureComponent';

describe('FeatureComponent', () => {
  it('deve renderizar corretamente', () => {
    render(<FeatureComponent />);
    // verificações básicas
  });
});

// 3. Adicione interatividade
export function FeatureComponent() {
  // ... existing code ...
  
  const handleAction = () => {
    // implementação da ação
  };
  
  return (
    <div>
      {/* interface com interatividade */}
      <button onClick={handleAction}>Ação</button>
    </div>
  );
}

// 4. Testes de interatividade
describe('FeatureComponent interatividade', () => {
  it('deve responder a interações do usuário', () => {
    render(<FeatureComponent />);
    const button = screen.getByText('Ação');
    fireEvent.click(button);
    // verificar resultado da interação
  });
});
```

### Para Integração com APIs
```tsx
// 1. Primeiro, defina a interface da API
// src/services/api/feature-api.ts
export interface FeatureData {
  // definição da interface
}

export async function fetchFeatureData(): Promise<FeatureData> {
  // implementação mínima ou mock
  return { /* dados de exemplo */ };
}

// 2. Testes para a API
// __tests__/services/api/feature-api.test.ts
import { fetchFeatureData } from '@/services/api/feature-api';

describe('fetchFeatureData', () => {
  it('deve retornar dados no formato correto', async () => {
    const data = await fetchFeatureData();
    expect(data).toMatchObject({
      // estrutura esperada
    });
  });
});

// 3. Implemente a integração real
export async function fetchFeatureData(): Promise<FeatureData> {
  const response = await fetch('/api/feature');
  if (!response.ok) {
    throw new Error('Falha ao buscar dados');
  }
  return response.json();
}

// 4. Testes de integração
// Usando msw para mock de API
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/feature', (req, res, ctx) => {
    return res(ctx.json({ /* dados mockados */ }));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('fetchFeatureData integração', () => {
  it('deve processar resposta da API corretamente', async () => {
    const data = await fetchFeatureData();
    // verificações da integração
  });
  
  it('deve lidar com erros da API', async () => {
    server.use(
      rest.get('/api/feature', (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );
    
    await expect(fetchFeatureData()).rejects.toThrow();
  });
});
```

## Revisão e Documentação

Após cada incremento:

1. **Revise o código** quanto a problemas de:
   - Segurança
   - Performance
   - Acessibilidade
   - Responsividade

2. **Documente as decisões chave**:
   - Padrões implementados
   - Limitações conhecidas
   - Requisitos não implementados nesta fase
   - Plano para próximos incrementos

3. **Atualize testes conforme necessário**:
   - Verifique a cobertura de testes
   - Adicione casos de teste para cenários não cobertos
   - Refatore testes conforme o código evolui

