import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Pause, XCircle, TrendingUp } from 'lucide-react';

interface StatusItem {
  label: string;
  count: number;
  percentage: number;
  color: string;
  bgColor: string;
  icon: React.ReactNode;
  description: string;
}

interface StatusBarProps {
  item: StatusItem;
  index: number;
}

function StatusBar({ item, index }: StatusBarProps) {
  return (
    <div
      className="group animate-in fade-in slide-in-from-bottom-4 duration-300"
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <div className="flex items-center justify-between p-4 rounded-lg border-0 shadow-sm bg-gradient-to-r from-white to-gray-50/30 dark:from-gray-800/50 dark:to-gray-700/30 hover:shadow-md transition-all duration-200">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${item.bgColor} transition-colors duration-200`}>
            {item.icon}
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {item.label}
              </span>
              <Badge variant="outline" className="text-xs">
                {item.count}
              </Badge>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
              {item.description}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="text-right">
            <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              {item.percentage}%
            </span>
            <div className="w-16 mt-1">
              <Progress
                value={item.percentage}
                className="h-1.5"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function StatusBarSkeleton({ index }: { index: number }) {
  return (
    <div
      className="animate-in fade-in slide-in-from-bottom-4 duration-300"
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <div className="flex items-center justify-between p-4 rounded-lg border-0 shadow-sm bg-gradient-to-r from-white to-gray-50/30 dark:from-gray-800/50 dark:to-gray-700/30">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            <div className="w-4 h-4 bg-transparent rounded"></div>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <div className="h-4 w-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
              <div className="h-5 w-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
            </div>
            <div className="h-3 w-32 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded mt-1 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className="h-4 w-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            </div>
            <div className="w-16 mt-1">
              <div className="h-1.5 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface StatusData {
  ativas: number;
  pausadas: number;
  canceladas: number;
  expiradas: number;
  total: number;
}

interface StatusRecorrenciasProps {
  data?: StatusData;
  isLoading?: boolean;
}

export function StatusRecorrencias({ data, isLoading = false }: StatusRecorrenciasProps) {
  // Dados padrão quando não há dados ou está carregando
  const defaultData: StatusData = {
    ativas: 0,
    pausadas: 0,
    canceladas: 0,
    expiradas: 0,
    total: 0,
  };

  const statusData = data || defaultData;

  const calculatePercentage = (value: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  };

  const statusItems: StatusItem[] = [
    {
      label: 'Ativas',
      count: statusData.ativas,
      percentage: calculatePercentage(statusData.ativas, statusData.total),
      color: 'bg-emerald-600',
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
      icon: <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-500" />,
      description: 'Recorrências funcionando normalmente',
    },
    {
      label: 'Pausadas',
      count: statusData.pausadas,
      percentage: calculatePercentage(statusData.pausadas, statusData.total),
      color: 'bg-amber-600',
      bgColor: 'bg-amber-50 dark:bg-amber-900/20',
      icon: <Pause className="w-4 h-4 text-amber-600 dark:text-amber-500" />,
      description: 'Temporariamente suspensas',
    },
    {
      label: 'Canceladas',
      count: statusData.canceladas,
      percentage: calculatePercentage(statusData.canceladas, statusData.total),
      color: 'bg-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      icon: <XCircle className="w-4 h-4 text-red-600 dark:text-red-500" />,
      description: 'Definitivamente encerradas',
    },
    {
      label: 'Expiradas',
      count: statusData.expiradas,
      percentage: calculatePercentage(statusData.expiradas, statusData.total),
      color: 'bg-gray-600',
      bgColor: 'bg-gray-50 dark:bg-gray-900/20',
      icon: <XCircle className="w-4 h-4 text-gray-600 dark:text-gray-500" />,
      description: 'Vencidas por prazo determinado',
    },
  ];

  const totalRecorrencias = statusData.total;

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className="pb-4 bg-gradient-to-r from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <div className="p-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
                <TrendingUp className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
              </div>
              Status das Recorrências
            </CardTitle>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Visão geral de {totalRecorrencias} recorrências
            </p>
          </div>
          <div className="flex items-center gap-2">
            {isLoading ? (
              <div className="h-4 w-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
            ) : (
              <span className="text-sm font-medium text-emerald-600 dark:text-emerald-400">
                {calculatePercentage(statusData.ativas, statusData.total)}% ativas
              </span>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-3">
          {isLoading ? (
            // Mostrar skeleton loading
            Array.from({ length: 4 }).map((_, index) => (
              <StatusBarSkeleton key={index} index={index} />
            ))
          ) : (
            // Mostrar dados reais
            statusItems.map((item, index) => (
              <StatusBar key={item.label} item={item} index={index} />
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
