
import { getPlanById } from '@/app/(dashboard)/academia/actions/plan-actions'
import { PlanFormProvider } from '@/components/features/planos'
import { Metadata } from 'next'
import { EditarPlanoClient } from './EditarPlanoClient'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Editar Plano',
  description: 'Edite as informações e configurações do plano de assinatura.',
}

export default async function EditarPlanoPage({ params }: { params: { id: string } }) {
  const { id } = await params
  const planResult = await getPlanById(id)

  if (!planResult.success || !planResult.data) {
    notFound()
  }
  
  return (
    <PlanFormProvider initialData={planResult.data}>

      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Button asChild variant="ghost" size="sm">
            <Link href="/financeiro/recorrentes/planos">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar aos Planos
            </Link>
          </Button>
        </div>
      </div>

      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Editar Plano</h1>
        <p className="text-muted-foreground">
          Atualize as configurações e informações do plano existente.
        </p>
      </div>

      <EditarPlanoClient />
    </PlanFormProvider>
  )
} 