export interface PaymentDetails {
  id: string
  amount: number
  currency: string
  status: string
  payment_method?: string | null
  due_date?: string | null
  description?: string | null
  membership_id: string | null
  student_id: string
  created_at: string | null
  updated_at: string | null
}

export interface StudentInfo {
  id: string
  name: string
  email: string
}

export interface PlanInfo {
  id: string
  title: string
  billing_period: string
  pricing_config?: any
}

export interface PixSettings {
  pixKey: string
  updatedAt: string
}

export interface CheckoutData {
  payment: PaymentDetails
  student: StudentInfo
  plan: PlanInfo
  pixSettings: PixSettings
}

export interface PixQRCodeData {
  qrCode: string
  pixKey: string
  amount: number
  description: string
  brCode: string
}

export interface CheckoutFormProps {
  paymentId: string
  userId: string
}

export interface PaymentInfoProps {
  payment: PaymentDetails
  plan: PlanInfo
  student: StudentInfo
}

export interface PixQRCodeProps {
  qrCodeData: PixQRCodeData
  loading?: boolean
}

export interface PaymentConfirmationProps {
  paymentId: string
  onConfirm: () => void
  loading?: boolean
  disabled?: boolean
}

export type CheckoutStep = 'loading' | 'ready' | 'confirming' | 'confirmed' | 'error'

export interface CheckoutState {
  step: CheckoutStep
  error?: string
  data?: CheckoutData
  qrCodeData?: PixQRCodeData
}
