-- Migration: Create Pending Recurring Payments Processor
-- Data: 2025-01-25
-- Descrição: Cria função para processar pagamentos recorrentes pendentes
--           que deveriam ter sido criados mas não foram devido a pagamentos antecipados

-- Função para processar pagamentos recorrentes pendentes
-- Esta função deve ser executada periodicamente (ex: diariamente) para criar
-- pagamentos que não foram criados devido a pagamentos antecipados
CREATE OR REPLACE FUNCTION public.process_pending_recurring_payments()
RETURNS jsonb
LANGUAGE plpgsql
SET search_path TO ''
AS $function$
DECLARE
  v_processed_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_results JSONB[] := '{}';
  v_payment_record RECORD;
  v_result JSONB;
BEGIN
  -- Buscar pagamentos recorrentes pagos onde:
  -- 1. O pagamento foi pago
  -- 2. A data de vencimento já passou (CURRENT_DATE >= due_date)
  -- 3. Não existe um próximo pagamento criado para a membership
  FOR v_payment_record IN
    SELECT DISTINCT
      p.id as payment_id,
      p.membership_id,
      p.due_date,
      p.paid_at,
      pl.title as plan_title,
      pl.pricing_config->>'frequency' as frequency
    FROM public.payments p
    JOIN public.memberships m ON m.id = p.membership_id
    JOIN public.plans pl ON pl.id = m.plan_id
    WHERE p.status = 'paid'
      AND p.payment_type = 'recurring'
      AND pl.pricing_config->>'type' = 'recurring'
      AND CURRENT_DATE >= p.due_date  -- A data de vencimento já passou
      AND NOT EXISTS (
        -- Não existe um próximo pagamento para esta membership
        SELECT 1 FROM public.payments p2
        WHERE p2.membership_id = p.membership_id
          AND p2.payment_type = 'recurring'
          AND p2.due_date > p.due_date
          AND p2.status IN ('pending', 'awaiting_confirmation', 'paid')
      )
    ORDER BY p.due_date ASC
  LOOP
    BEGIN
      -- Tentar criar o próximo pagamento recorrente
      SELECT public.create_next_recurring_payment(v_payment_record.payment_id) INTO v_result;
      
      IF (v_result->>'success')::boolean = true THEN
        v_processed_count := v_processed_count + 1;
        v_results := array_append(v_results, jsonb_build_object(
          'payment_id', v_payment_record.payment_id,
          'membership_id', v_payment_record.membership_id,
          'plan_title', v_payment_record.plan_title,
          'original_due_date', v_payment_record.due_date,
          'status', 'success',
          'new_payment_id', v_result->'data'->>'payment_id',
          'new_due_date', v_result->'data'->>'due_date'
        ));
      ELSE
        -- Se falhou, mas não é erro crítico (ex: já existe pagamento), não contar como erro
        IF v_result->>'error' NOT LIKE '%Já existe um pagamento%' THEN
          v_error_count := v_error_count + 1;
        END IF;
        
        v_results := array_append(v_results, jsonb_build_object(
          'payment_id', v_payment_record.payment_id,
          'membership_id', v_payment_record.membership_id,
          'plan_title', v_payment_record.plan_title,
          'original_due_date', v_payment_record.due_date,
          'status', 'skipped',
          'reason', v_result->>'error'
        ));
      END IF;
      
    EXCEPTION
      WHEN OTHERS THEN
        v_error_count := v_error_count + 1;
        v_results := array_append(v_results, jsonb_build_object(
          'payment_id', v_payment_record.payment_id,
          'membership_id', v_payment_record.membership_id,
          'plan_title', v_payment_record.plan_title,
          'original_due_date', v_payment_record.due_date,
          'status', 'error',
          'error', SQLERRM
        ));
    END;
  END LOOP;

  -- Retornar resultado do processamento
  RETURN jsonb_build_object(
    'success', true,
    'processed_count', v_processed_count,
    'error_count', v_error_count,
    'total_analyzed', array_length(v_results, 1),
    'processed_at', NOW(),
    'results', v_results
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Erro interno: ' || SQLERRM,
      'processed_count', v_processed_count,
      'error_count', v_error_count
    );
END;
$function$;

-- Comentário explicativo sobre a função
COMMENT ON FUNCTION public.process_pending_recurring_payments() IS 
'Função para processar pagamentos recorrentes pendentes que deveriam ter sido criados mas não foram devido a pagamentos antecipados. Deve ser executada periodicamente (ex: diariamente).';

-- Função auxiliar para verificar pagamentos que precisam ser processados
-- (útil para monitoramento e debug)
CREATE OR REPLACE FUNCTION public.check_pending_recurring_payments()
RETURNS jsonb
LANGUAGE plpgsql
SET search_path TO ''
AS $function$
DECLARE
  v_pending_count INTEGER := 0;
  v_pending_payments JSONB[] := '{}';
  v_payment_record RECORD;
BEGIN
  -- Buscar pagamentos que precisam ser processados
  FOR v_payment_record IN
    SELECT 
      p.id as payment_id,
      p.membership_id,
      p.due_date,
      p.paid_at,
      pl.title as plan_title,
      pl.pricing_config->>'frequency' as frequency,
      CURRENT_DATE - p.due_date as days_overdue
    FROM public.payments p
    JOIN public.memberships m ON m.id = p.membership_id
    JOIN public.plans pl ON pl.id = m.plan_id
    WHERE p.status = 'paid'
      AND p.payment_type = 'recurring'
      AND pl.pricing_config->>'type' = 'recurring'
      AND CURRENT_DATE >= p.due_date
      AND NOT EXISTS (
        SELECT 1 FROM public.payments p2
        WHERE p2.membership_id = p.membership_id
          AND p2.payment_type = 'recurring'
          AND p2.due_date > p.due_date
          AND p2.status IN ('pending', 'awaiting_confirmation', 'paid')
      )
    ORDER BY p.due_date ASC
  LOOP
    v_pending_count := v_pending_count + 1;
    v_pending_payments := array_append(v_pending_payments, jsonb_build_object(
      'payment_id', v_payment_record.payment_id,
      'membership_id', v_payment_record.membership_id,
      'plan_title', v_payment_record.plan_title,
      'due_date', v_payment_record.due_date,
      'paid_at', v_payment_record.paid_at,
      'frequency', v_payment_record.frequency,
      'days_overdue', v_payment_record.days_overdue
    ));
  END LOOP;

  RETURN jsonb_build_object(
    'pending_count', v_pending_count,
    'checked_at', NOW(),
    'pending_payments', v_pending_payments
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'error', 'Erro interno: ' || SQLERRM,
      'pending_count', v_pending_count
    );
END;
$function$;

-- Comentário explicativo sobre a função de verificação
COMMENT ON FUNCTION public.check_pending_recurring_payments() IS 
'Função para verificar quais pagamentos recorrentes precisam ser processados. Útil para monitoramento e debug.';
