-- Migration: Create memberships table
-- Purpose: Create the main table for storing student memberships (enrollments)
-- Affected tables: memberships (new)
-- Dependencies: 002_create_plans_table.sql
-- Date: 2024-12-21

-- Create memberships table
create table public.memberships (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid not null references public.tenants(id) on delete cascade,
  student_id uuid not null references public.students(id) on delete cascade,
  plan_id uuid not null references public.plans(id) on delete restrict,
  
  -- Membership status and lifecycle
  status public.membership_status not null default 'active',
  start_date date not null default current_date,
  end_date date,
  
  -- Billing information
  next_billing_date date,
  cancel_at timestamptz,
  canceled_at timestamptz,
  
  -- Additional metadata for custom fields
  metadata jsonb not null default '{}',
  
  -- Audit fields
  created_at timestamptz not null default now(),
  updated_at timestamptz,
  
  -- Constraints
  constraint memberships_end_after_start check (end_date is null or end_date >= start_date),
  constraint memberships_canceled_at_with_status check (
    (status = 'canceled' and canceled_at is not null) or 
    (status != 'canceled' and canceled_at is null)
  ),
  constraint memberships_cancel_at_future check (cancel_at is null or cancel_at > now())
);

-- Enable RLS
alter table public.memberships enable row level security;

-- Create policies for tenant isolation and user access
create policy "tenant_isolation_memberships" on public.memberships
  using (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

create policy "users_can_select_memberships" on public.memberships
  for select using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid
  );

create policy "admin_can_insert_memberships" on public.memberships
  for insert with check (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    auth.jwt() ->> 'role' in ('admin', 'owner', 'instructor')
  );

create policy "admin_can_update_memberships" on public.memberships
  for update using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    auth.jwt() ->> 'role' in ('admin', 'owner', 'instructor')
  );

create policy "admin_can_delete_memberships" on public.memberships
  for delete using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    auth.jwt() ->> 'role' in ('admin', 'owner')
  );

-- Students can view their own memberships
create policy "students_can_view_own_memberships" on public.memberships
  for select using (
    tenant_id = (auth.jwt() ->> 'tenant_id')::uuid and
    student_id = (auth.jwt() ->> 'student_id')::uuid
  );

-- Create indexes for performance
create index idx_memberships_tenant_id on public.memberships(tenant_id);
create index idx_memberships_student_id on public.memberships(student_id);
create index idx_memberships_plan_id on public.memberships(plan_id);
create index idx_memberships_status on public.memberships(tenant_id, status);
create index idx_memberships_active on public.memberships(tenant_id, student_id, status) 
  where status = 'active';
create index idx_memberships_billing_date on public.memberships(next_billing_date) 
  where status = 'active' and next_billing_date is not null;
create index idx_memberships_cancel_at on public.memberships(cancel_at) 
  where cancel_at is not null;

-- Create function to update updated_at timestamp
create or replace function public.update_memberships_updated_at()
returns trigger
language plpgsql
security invoker
set search_path = ''
as $$
begin
  new.updated_at := now();
  return new;
end;
$$;

-- Create trigger for updated_at
create trigger update_memberships_updated_at_trigger
  before update on public.memberships
  for each row
  execute function public.update_memberships_updated_at();

-- Create function to set canceled_at when status changes to canceled
create or replace function public.set_membership_canceled_at()
returns trigger
language plpgsql
security invoker
set search_path = ''
as $$
begin
  -- Set canceled_at when status changes to canceled
  if new.status = 'canceled' and old.status != 'canceled' then
    new.canceled_at := now();
  end if;
  
  -- Clear canceled_at if status changes from canceled to something else
  if new.status != 'canceled' and old.status = 'canceled' then
    new.canceled_at := null;
  end if;
  
  return new;
end;
$$;

-- Create trigger for canceled_at management
create trigger set_membership_canceled_at_trigger
  before update on public.memberships
  for each row
  execute function public.set_membership_canceled_at();

-- Add table comment
comment on table public.memberships is 'Student memberships (enrollments) linking students to specific plan versions with status tracking and billing information.';

-- Add column comments  
comment on column public.memberships.plan_id is 'References the specific plan version at the time of enrollment. Immutable for historical accuracy.';
comment on column public.memberships.status is 'Current membership status affecting billing and access permissions.';
comment on column public.memberships.start_date is 'Date when the membership becomes active and billing begins.';
comment on column public.memberships.end_date is 'Date when membership expires (for limited duration plans). NULL for ongoing plans.';
comment on column public.memberships.next_billing_date is 'Next scheduled billing date for recurring payments.';
comment on column public.memberships.cancel_at is 'Scheduled cancellation date (cancellation at end of current billing period).';
comment on column public.memberships.canceled_at is 'Timestamp when membership was actually canceled.'; 