-- Migration: Migrate payments table from subscriptions to memberships
-- Purpose: Update payments table to reference memberships instead of subscriptions
-- Affected tables: payments (modified)
-- Dependencies: 003_create_memberships_table.sql
-- Date: 2024-12-21

-- Step 1: Add new membership_id column temporarily
alter table public.payments 
add column membership_id uuid references public.memberships(id) on delete cascade;

-- Step 2: Create index for the new foreign key
create index idx_payments_membership_id on public.payments(membership_id);

-- Step 3: Add comment for the new column
comment on column public.payments.membership_id is 'References the membership this payment is associated with. Replaces subscription_id.';

-- Step 4: For existing data migration (if subscriptions table still exists)
-- This would be handled in a separate data migration script after both tables coexist
-- For now, we'll just ensure the column exists and is properly indexed

-- Step 5: Eventually, after data migration, we would:
-- 1. Make membership_id NOT NULL
-- 2. Drop subscription_id column
-- 3. Update any views or functions that reference subscription_id

-- Note: The actual data migration and cleanup will be done in subsequent scripts
-- after ensuring all existing subscriptions have been migrated to memberships

-- Add constraint to ensure at least one reference exists (during transition period)
alter table public.payments 
add constraint payments_has_reference 
check (subscription_id is not null or membership_id is not null);

-- Add partial index for payments with membership_id
create index idx_payments_membership_not_null on public.payments(tenant_id, membership_id) 
where membership_id is not null; 