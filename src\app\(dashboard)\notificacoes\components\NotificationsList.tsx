'use client';

import { useState, useMemo } from 'react';
import { useNotifications } from '@/hooks/notifications/use-notifications';
import { NotificationItem } from './NotificationItem';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { BellIcon, CheckIcon, ArchiveBoxIcon } from '@heroicons/react/24/outline';
import { Skeleton } from '@/components/ui/skeleton';
import type { NotificationFilters, Notification } from '@/services/notifications/types/notification-types';

interface NotificationsListProps {
  userId: string;
  filters?: NotificationFilters;
  showActions?: boolean;
}

export function NotificationsList({
  userId,
  filters = {},
  showActions = true
}: NotificationsListProps) {
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // Estabilizar o objeto filters para evitar re-criações
  const stableFilters = useMemo(() => ({
    ...filters,
    limit: 20
  }), [JSON.stringify(filters)]);

  const {
    notifications,
    loading,
    error,
    total,
    hasMore,
    loadMore,
    markAsRead,
    markAllAsRead,
    archive,
    deleteNotification
  } = useNotifications({
    userId,
    filters: stableFilters,
    realTime: true
  });

  const handleSelectNotification = (notificationId: string, selected: boolean) => {
    if (selected) {
      setSelectedNotifications(prev => [...prev, notificationId]);
    } else {
      setSelectedNotifications(prev => prev.filter(id => id !== notificationId));
    }
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(notifications.map(n => n.id));
    }
  };

  const handleBulkMarkAsRead = async () => {
    try {
      await Promise.all(
        selectedNotifications.map(id => markAsRead(id))
      );
      setSelectedNotifications([]);
    } catch (error) {
      console.error('Erro ao marcar notificações como lidas:', error);
    }
  };

  const handleBulkArchive = async () => {
    try {
      await Promise.all(
        selectedNotifications.map(id => archive(id))
      );
      setSelectedNotifications([]);
    } catch (error) {
      console.error('Erro ao arquivar notificações:', error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      await Promise.all(
        selectedNotifications.map(id => deleteNotification(id))
      );
      setSelectedNotifications([]);
    } catch (error) {
      console.error('Erro ao excluir notificações:', error);
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400">
              Erro ao carregar notificações: {error}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading && notifications.length === 0) {
    return (
      <div className="space-y-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="flex gap-4">
                <Skeleton className="h-6 w-6 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <Card>
        <CardContent className="p-12">
          <div className="text-center">
            <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
              Nenhuma notificação encontrada
            </h3>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {filters.status?.includes('unread') 
                ? 'Você está em dia com tudo!'
                : 'Não há notificações para exibir com os filtros selecionados.'
              }
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Ações em lote */}
      {showActions && notifications.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                >
                  {selectedNotifications.length === notifications.length 
                    ? 'Desmarcar Todas' 
                    : 'Selecionar Todas'
                  }
                </Button>
                
                {selectedNotifications.length > 0 && (
                  <span className="text-sm text-muted-foreground">
                    {selectedNotifications.length} selecionada(s)
                  </span>
                )}
              </div>

              {selectedNotifications.length > 0 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkMarkAsRead}
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Marcar como Lidas
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkArchive}
                  >
                    <ArchiveBoxIcon className="h-4 w-4 mr-1" />
                    Arquivar
                  </Button>
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                  >
                    Excluir
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de notificações */}
      <div className="space-y-2">
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            selected={selectedNotifications.includes(notification.id)}
            onSelect={(selected) => handleSelectNotification(notification.id, selected)}
            onMarkAsRead={() => markAsRead(notification.id)}
            onArchive={() => archive(notification.id)}
            onDelete={() => deleteNotification(notification.id)}
            showActions={showActions}
          />
        ))}
      </div>

      {/* Carregar mais */}
      {hasMore && (
        <div className="text-center pt-4">
          <Button
            variant="outline"
            onClick={loadMore}
            disabled={loading}
          >
            {loading ? 'Carregando...' : 'Carregar Mais'}
          </Button>
        </div>
      )}

      {/* Total de notificações */}
      {total > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          Mostrando {notifications.length} de {total} notificações
        </div>
      )}
    </div>
  );
}
