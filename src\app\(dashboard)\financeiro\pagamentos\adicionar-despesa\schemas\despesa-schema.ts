import { z } from 'zod';

// Schema para validação do formulário de despesas
export const despesaFormSchema = z.object({
  supplierName: z.string().min(2, 'Nome do fornecedor deve ter pelo menos 2 caracteres'),
  categoryId: z.string().min(1, 'Selecione uma categoria'),
  type: z.string().min(2, 'Tipo da despesa deve ter pelo menos 2 caracteres'),
  amount: z.string()
    .min(1, 'Valor é obrigatório')
    .refine((val) => {
      const numericValue = parseFloat(val.replace(/[^\d,]/g, '').replace(',', '.'));
      return !isNaN(numericValue) && numericValue > 0;
    }, 'Valor deve ser um número válido maior que zero'),
  paymentMethod: z.string().min(1, 'Selecione um método de pagamento'),
  date: z.string().min(1, 'Data é obrigatória'),
  dueDate: z.string().optional(),
  description: z.string().optional(),
  status: z.enum(['Pago', 'Pendente', 'Vencido']).default('Pendente'),

  // Campos de recorrência
  isRecurring: z.boolean().default(false),
  recurrenceFrequency: z.enum(['monthly', 'quarterly', 'yearly']).optional(),
  recurrenceEndDate: z.string().optional(),
  recurrenceCount: z.string().optional(),
}).refine((data) => {
  // Se é recorrente, deve ter frequência
  if (data.isRecurring && !data.recurrenceFrequency) {
    return false;
  }
  return true;
}, {
  message: 'Frequência é obrigatória para despesas recorrentes',
  path: ['recurrenceFrequency']
});

export type DespesaFormValues = z.infer<typeof despesaFormSchema>;

// Opções para os selects - métodos de pagamento serão carregados do banco de dados

export const statusOptions = [
  { value: 'Pago', label: 'Pago' },
  { value: 'Pendente', label: 'Pendente' },
  { value: 'Vencido', label: 'Vencido' },
];

export const recurrenceFrequencyOptions = [
  { value: 'monthly', label: 'Mensal' },
  { value: 'quarterly', label: 'Trimestral' },
  { value: 'yearly', label: 'Anual' },
];
