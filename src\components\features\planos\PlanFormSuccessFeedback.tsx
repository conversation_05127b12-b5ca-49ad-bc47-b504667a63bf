'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { CheckCircle, RefreshCw, <PERSON>rkles, ArrowLeft } from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'

export function PlanFormSuccessFeedback() {
  const { submittedData, resetForm, planId } = usePlanForm()
  const [isVisible, setIsVisible] = useState(false)
  const [showContent, setShowContent] = useState(false)
  const [countdown, setCountdown] = useState(5)
  const router = useRouter()

  const isEditMode = !!planId

  useEffect(() => {
    if (submittedData) {
      const scrollPosition = Math.max(
        0,
        (document.documentElement.scrollHeight - window.innerHeight) / 2,
      )
      window.scrollTo({
        top: scrollPosition,
        behavior: 'smooth',
      })

      setIsVisible(true)
      const timer = setTimeout(() => setShowContent(true), 150)
      return () => clearTimeout(timer)
    }
  }, [submittedData])

  useEffect(() => {
    if (!submittedData || !isVisible) return

    const redirectTimer = setTimeout(() => {
      router.push('/financeiro/recorrentes/planos')
    }, 5000)

    const countdownTimer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(countdownTimer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => {
      clearTimeout(redirectTimer)
      clearInterval(countdownTimer)
    }
  }, [submittedData, isVisible, router])

  const handleAction = () => {
    setShowContent(false)
    setTimeout(() => {
      setIsVisible(false)
      if (isEditMode) {
        router.push('/financeiro/recorrentes/planos')
      } else {
        resetForm()
        window.location.reload()
      }
    }, 200)
  }

  if (!submittedData || !isVisible) return null

  return (
    <div
      className={cn(
        'fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-all duration-300',
        isVisible ? 'opacity-100' : 'opacity-0',
      )}
    >
      <div
        className={cn(
          'relative bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 max-w-md w-full mx-4 transition-all duration-500 transform',
          showContent
            ? 'scale-100 opacity-100 translate-y-0'
            : 'scale-95 opacity-0 translate-y-4',
        )}
      >
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-teal-500/5 via-transparent to-emerald-500/5 pointer-events-none" />

        <div className="relative p-8 text-center">
          <div className="relative mx-auto mb-6">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/30 dark:to-emerald-900/30 mx-auto">
              <CheckCircle className="h-8 w-8 text-teal-600 dark:text-teal-400" />
            </div>
            <Sparkles className="absolute -top-1 -right-1 h-4 w-4 text-teal-500 dark:text-teal-400 animate-pulse" />
            <Sparkles className="absolute -bottom-1 -left-1 h-3 w-3 text-emerald-500 dark:text-emerald-400 animate-pulse delay-300" />
          </div>

          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
            {isEditMode ? 'Plano Atualizado!' : 'Plano Criado!'}
          </h2>

          <p className="text-gray-600 dark:text-gray-400 mb-2 leading-relaxed">
            Seu plano{' '}
            <span className="font-semibold text-teal-600 dark:text-teal-400">
              "{submittedData.details?.titulo}"
            </span>{' '}
            foi {isEditMode ? 'atualizado' : 'criado'} com sucesso.
          </p>

          <p className="text-sm text-gray-500 dark:text-gray-500 mb-2">
            Todas as configurações foram salvas.
          </p>

          {countdown > 0 && (
            <p className="text-xs text-gray-400 dark:text-gray-600 mb-8">
              Redirecionando em {countdown} segundo{countdown !== 1 ? 's' : ''}
              ...
            </p>
          )}

          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 mb-8 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Tipo:</span>
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {(submittedData.pricing as any)?.tipo === 'recurring'
                  ? 'Recorrente'
                  : (submittedData.pricing as any)?.tipo === 'one-time'
                    ? 'Único'
                    : (submittedData.pricing as any)?.tipo === 'per-session'
                      ? 'Por Aula'
                      : (submittedData.pricing as any)?.tipo === 'trial'
                        ? 'Trial'
                        : 'Personalizado'}
              </span>
            </div>

            {submittedData.academyAccess?.modalidades &&
              submittedData.academyAccess.modalidades.length > 0 && (
                <div className="flex items-center justify-between text-sm mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                  <span className="text-gray-600 dark:text-gray-400">
                    Modalidades:
                  </span>
                  <span className="font-medium text-gray-900 dark:text-gray-100 text-right">
                    {submittedData.academyAccess.modalidades.length} selecionada
                    {submittedData.academyAccess.modalidades.length > 1 ? 's' : ''}
                  </span>
                </div>
              )}
          </div>

          <Button
            onClick={handleAction}
            className="w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-semibold py-3 rounded-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
          >
            {isEditMode ? (
              <ArrowLeft className="h-4 w-4 mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            {isEditMode ? 'Voltar para Planos' : 'Criar Novo Plano'}
          </Button>
        </div>

        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-teal-500 to-emerald-500 rounded-t-full" />
      </div>
    </div>
  )
}