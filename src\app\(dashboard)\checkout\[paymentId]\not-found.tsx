import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { FileX, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card className="p-8 text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                <FileX className="w-8 h-8 text-gray-600 dark:text-gray-400" />
              </div>
            </div>
            
            <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Pagamento não encontrado
            </h1>
            
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              O pagamento que você está tentando acessar não foi encontrado ou não existe.
              Verifique o link ou acesse seus pagamentos através do perfil.
            </p>
            
            <Button asChild>
              <Link href="/perfil" className="gap-2">
                <ArrowLeft className="w-4 h-4" />
                Voltar ao Perfil
              </Link>
            </Button>
          </Card>
        </div>
      </div>
    </div>
  )
}
