'use client'

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Wifi, WifiOff, AlertCircle, RefreshCw } from "lucide-react"

interface RealtimeIndicatorProps {
  status: 'polling' | 'updating' | 'error' | 'idle'
  lastUpdate?: Date
  className?: string
  showText?: boolean
}

export function RealtimeIndicator({ 
  status, 
  lastUpdate, 
  className,
  showText = true 
}: RealtimeIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'polling':
        return {
          icon: RefreshCw,
          text: 'Atualizando...',
          variant: 'secondary' as const,
          iconClass: 'animate-spin'
        }
      case 'updating':
        return {
          icon: RefreshCw,
          text: 'Sincronizando...',
          variant: 'default' as const,
          iconClass: 'animate-spin'
        }
      case 'error':
        return {
          icon: AlertCircle,
          text: 'Erro de conexão',
          variant: 'destructive' as const,
          iconClass: 'text-red-500'
        }
      case 'idle':
      default:
        return {
          icon: Wifi,
          text: 'Conectado',
          variant: 'outline' as const,
          iconClass: 'text-green-500'
        }
    }
  }

  const { icon: Icon, text, variant, iconClass } = getStatusConfig()

  const formatLastUpdate = (date: Date) => {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 60) {
      return 'agora'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes}min atrás`
    } else {
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge variant={variant} className="flex items-center gap-1.5 text-xs">
        <Icon className={cn("h-3 w-3", iconClass)} />
        {showText && text}
      </Badge>
      
      {lastUpdate && status !== 'error' && (
        <span className="text-xs text-muted-foreground">
          Última atualização: {formatLastUpdate(lastUpdate)}
        </span>
      )}
    </div>
  )
}