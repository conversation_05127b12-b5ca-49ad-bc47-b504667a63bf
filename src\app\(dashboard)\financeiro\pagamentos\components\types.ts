// Tipos base para métricas financeiras
export interface FinancialMetric {
  value: string;
  label: string;
  color: 'green' | 'blue' | 'orange' | 'red';
}

// Mantém compatibilidade com código existente
export interface PaymentMetric extends FinancialMetric {}

// Tipos para transações financeiras unificadas
export type TransactionType = 'income' | 'expense';

export interface BaseTransaction {
  id: string | number;
  type: string;
  paymentMethod?: string;
  date: string;
  amount: string;
  status: string;
  paidAt?: string;
  dueDate?: string;
  description?: string;
}

// Transação de receita (pagamento de aluno)
export interface IncomeTransaction extends BaseTransaction {
  transactionType: 'income';
  studentName: string;
  studentUserId?: string;
}

// Transação de despesa
export interface ExpenseTransaction extends BaseTransaction {
  transactionType: 'expense';
  supplierName: string;
  categoryName?: string;
  categoryColor?: string;
}

// União dos tipos de transação
export type Transaction = IncomeTransaction | ExpenseTransaction;

// Tipo legacy para compatibilidade (sem transactionType)
export interface LegacyTransaction extends BaseTransaction {
  studentName: string;
  studentUserId?: string;
}

// Categoria de despesa
export interface ExpenseCategory {
  id: string;
  name: string;
  description?: string;
  color: string;
}

export interface PageHeaderProps {
  title: string;
  description: string;
}

export interface MetricCardProps {
  metric: FinancialMetric;
}

export interface TransactionItemProps {
  transaction: Transaction | LegacyTransaction;
  onExpenseClick?: (expenseId: string) => void;
}

// Props genéricas para métricas financeiras
export interface FinancialMetricsProps {
  metrics: FinancialMetric[];
  loading?: boolean;
  error?: string | null;
}

// Mantém compatibilidade
export interface PaymentMetricsProps extends FinancialMetricsProps {
  metrics: PaymentMetric[];
}

// Props genéricas para filtros financeiros
export interface FinancialFiltersProps {
  onFiltersClick?: () => void;
  onSearchChange?: (value: string) => void;
  onExportClick?: () => void;
  onAddClick?: () => void; // Para adicionar nova despesa
  transactionType?: TransactionType;
}

// Mantém compatibilidade
export interface PaymentFiltersProps extends FinancialFiltersProps {}

// Tipos para filtros financeiros unificados
export interface FinancialFilterState {
  search?: string;
  status?: string[];
  paymentMethod?: string[];
  paymentType?: string[];
  categories?: string[]; // Para despesas
  minAmount?: number;
  maxAmount?: number;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

// Mantém compatibilidade
export interface PaymentFilterState extends FinancialFilterState {}

export interface PaymentFilterPopoverProps {
  filters: PaymentFilterState;
  onFilterChange: (filters: PaymentFilterState) => void;
  onClearFilters?: () => void;
  paymentMethods: PaymentMethodOption[];
}

export interface PaymentMethodOption {
  id: string;
  name: string;
  slug: string;
}

export interface ActivePaymentFilter {
  id: string;
  type: 'status' | 'paymentMethod' | 'paymentType' | 'amount' | 'date';
  label: string;
  value: string;
}

export interface TransactionsListProps {
  transactions: (Transaction | LegacyTransaction)[];
  onLoadMore?: () => void;
  isLoading?: boolean;
  hasMore?: boolean;
  transactionType?: TransactionType;
  onExpenseClick?: (expenseId: string) => void;
}

// Tipos para sistema de tabs
export type FinancialTab = 'income' | 'expense';

export interface FinancialTabsProps {
  activeTab: FinancialTab;
  onTabChange: (tab: FinancialTab) => void;
}

// Props para o cliente principal da página financeira
export interface FinancialClientProps {
  initialTab?: FinancialTab;
  incomeMetrics: FinancialMetric[];
  incomeTransactions: IncomeTransaction[];
  expenseMetrics?: FinancialMetric[];
  expenseTransactions?: ExpenseTransaction[];
  hasMoreIncome?: boolean;
  hasMoreExpenses?: boolean;
  paymentMethods?: PaymentMethodOption[];
}

// Props para formulário de despesas
export interface ExpenseFormProps {
  categories: ExpenseCategory[];
  onSubmit: (expense: Partial<ExpenseTransaction>) => void;
  onCancel: () => void;
  initialData?: Partial<ExpenseTransaction>;
}
