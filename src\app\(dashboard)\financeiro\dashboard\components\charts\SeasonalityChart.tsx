"use client";

/**
 * Componente de Gráfico de Sazonalidade - Fase 5
 * Exibe padrões sazonais de fluxo de caixa ao longo do ano
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, ReferenceLine } from 'recharts';
import { Calendar, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getSeasonalityAnalysis } from '../../actions/metrics/cashflow-actions';
import { SeasonalityData } from '../../types/dashboard-types';
import { formatCurrency } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface SeasonalityChartProps {
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const inflows = payload.find(p => p.dataKey === 'averageInflows');
    const outflows = payload.find(p => p.dataKey === 'averageOutflows');
    const netFlow = payload.find(p => p.dataKey === 'averageNetFlow');

    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          {label}
        </p>
        {inflows && (
          <p className="text-sm text-emerald-600 dark:text-emerald-400">
            <span className="font-medium">Entradas Médias: </span>
            {formatCurrency(inflows.value)}
          </p>
        )}
        {outflows && (
          <p className="text-sm text-red-600 dark:text-red-400">
            <span className="font-medium">Saídas Médias: </span>
            {formatCurrency(outflows.value)}
          </p>
        )}
        {netFlow && (
          <p className={cn(
            "text-sm font-medium",
            netFlow.value >= 0 
              ? "text-emerald-600 dark:text-emerald-400" 
              : "text-red-600 dark:text-red-400"
          )}>
            <span>Fluxo Médio: </span>
            {formatCurrency(netFlow.value)}
          </p>
        )}
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const SeasonalityChart: React.FC<SeasonalityChartProps> = ({
  className
}) => {
  const [data, setData] = useState<SeasonalityData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar dados
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getSeasonalityAnalysis();

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar análise de sazonalidade');
        }

        setData(result.data || []);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar análise de sazonalidade:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Calcular estatísticas
  const bestMonth = data.length > 0 
    ? data.reduce((best, current) => 
        current.averageNetFlow > best.averageNetFlow ? current : best
      )
    : null;
  
  const worstMonth = data.length > 0 
    ? data.reduce((worst, current) => 
        current.averageNetFlow < worst.averageNetFlow ? current : worst
      )
    : null;

  const averageAnnualFlow = data.length > 0 
    ? data.reduce((sum, month) => sum + month.averageNetFlow, 0) / data.length 
    : 0;

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Análise de Sazonalidade
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Padrões de fluxo de caixa ao longo do ano
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Resumo da Sazonalidade */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <TrendingUp className="h-4 w-4 text-emerald-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Melhor Mês
              </span>
            </div>
            <p className="text-sm font-semibold text-emerald-600 dark:text-emerald-400">
              {bestMonth ? bestMonth.monthName : '-'}
            </p>
            <p className="text-xs text-gray-500">
              {bestMonth ? formatCurrency(bestMonth.averageNetFlow) : '-'}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <TrendingDown className="h-4 w-4 text-red-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Pior Mês
              </span>
            </div>
            <p className="text-sm font-semibold text-red-600 dark:text-red-400">
              {worstMonth ? worstMonth.monthName : '-'}
            </p>
            <p className="text-xs text-gray-500">
              {worstMonth ? formatCurrency(worstMonth.averageNetFlow) : '-'}
            </p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1">
              <BarChart3 className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Média Anual
              </span>
            </div>
            <p className={cn(
              "text-sm font-semibold",
              averageAnnualFlow >= 0 
                ? "text-emerald-600 dark:text-emerald-400" 
                : "text-red-600 dark:text-red-400"
            )}>
              {formatCurrency(averageAnnualFlow)}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400 mb-2">Erro ao carregar análise</p>
              <p className="text-sm text-gray-500">{error}</p>
            </div>
          </div>
        ) : data.length === 0 ? (
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Dados insuficientes para análise de sazonalidade
              </p>
              <p className="text-sm text-gray-500 mt-2">
                É necessário pelo menos 12 meses de dados
              </p>
            </div>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="monthName" 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    // Para valores grandes, usar formatação compacta
                    if (Math.abs(value) >= 1000) {
                      return new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL',
                        notation: 'compact',
                        maximumFractionDigits: 1
                      }).format(value);
                    }
                    return formatCurrency(value);
                  }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <ReferenceLine y={0} stroke="#6b7280" strokeDasharray="2 2" />
                <Line 
                  type="monotone"
                  dataKey="averageInflows" 
                  name="Entradas Médias"
                  stroke="#10b981" 
                  strokeWidth={2}
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                />
                <Line 
                  type="monotone"
                  dataKey="averageOutflows" 
                  name="Saídas Médias"
                  stroke="#ef4444" 
                  strokeWidth={2}
                  dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
                />
                <Line 
                  type="monotone"
                  dataKey="averageNetFlow" 
                  name="Fluxo Líquido Médio"
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Insights de Sazonalidade */}
        {data.length > 0 && bestMonth && worstMonth && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start space-x-2">
              <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Insights de Sazonalidade
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  {bestMonth.monthName} é historicamente o melhor mês, enquanto {worstMonth.monthName} apresenta 
                  os menores fluxos. Considere estratégias específicas para cada período.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
