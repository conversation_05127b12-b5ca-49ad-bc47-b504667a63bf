'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { CircularProgress } from '@/components/ui/circular-progress';
import { getTuitionValueMetrics } from '../actions/payment-actions';
import { TuitionValueMetrics } from '../types';
import { formatCurrency } from '@/utils/format-utils';
import { Calendar,TrendingUp } from 'lucide-react';
import { MdPaid } from "react-icons/md";
import { TbCalendarDue } from "react-icons/tb";


export function MensalidadesMetrics() {
  const [metrics, setMetrics] = useState<TuitionValueMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      setLoading(true);
      setError(null);

      const result = await getTuitionValueMetrics();

      if (result.success && result.data) {
        console.log('Métricas recebidas:', result.data);
        setMetrics(result.data);
      } else {
        setError(result.error || 'Erro ao carregar métricas');
      }

      setLoading(false);
    };

    fetchMetrics();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index} className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
            <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
              <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-24 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
              </div>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
              </div>
              <div className="text-center space-y-2">
                <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-20 mx-auto relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
                <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-24 mx-auto relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="col-span-full border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
          <CardContent className="pt-8 pb-8">
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-red-600 dark:text-red-400 text-center font-medium">{error || 'Erro ao carregar métricas'}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Debug: verificar os valores recebidos
  console.log('Métricas no componente:', metrics);

  // Calcular percentuais para os progress circles baseado nos valores
  const totalTuitionsValue = metrics.paidTuitionsValue + metrics.scheduledTuitionsValue + metrics.overdueTuitionsValue;

  // Se não há mensalidades, mostrar 0% para todos
  if (totalTuitionsValue === 0) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="col-span-full border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
          <CardContent className="pt-8 pb-8">
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800/30 dark:to-gray-700/30 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-center font-medium">Nenhuma mensalidade encontrada para o mês atual</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const paidPercentage = (metrics.paidTuitionsValue / totalTuitionsValue) * 100;
  const scheduledPercentage = (metrics.scheduledTuitionsValue / totalTuitionsValue) * 100;
  const overduePercentage = (metrics.overdueTuitionsValue / totalTuitionsValue) * 100;
  
  // Calcular percentual de receita baseado na receita esperada
  let revenuePercentage = 0;
  let expectedRevenue = 0;

  // Usar expectedMonthlyRevenue da API (que agora sempre vem preenchida)
  expectedRevenue = metrics.expectedMonthlyRevenue || 0;

  if (expectedRevenue > 0) {
    revenuePercentage = Math.min((metrics.monthlyRevenueAverage / expectedRevenue) * 100, 100);
  } else {
    // Fallback: se não há receita esperada, mostrar 100% do que foi recebido
    revenuePercentage = metrics.monthlyRevenueAverage > 0 ? 100 : 0;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Mensalidades Pagas */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
        <CardHeader className="pb-4 bg-gradient-to-r from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className="p-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
              <MdPaid className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
            </div>
            Pagas (Mês Atual)
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex items-center justify-center mb-4">
            <CircularProgress
              value={paidPercentage}
              size={64}
              strokeWidth={4}
              className="text-emerald-600 dark:text-emerald-500"
            />
          </div>
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 tracking-tight">
              {formatCurrency(metrics.paidTuitionsValue)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {paidPercentage.toFixed(1)}% do total
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Agendadas */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
        <CardHeader className="pb-4 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
              <Calendar className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            Agendadas (Mês Atual)
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex items-center justify-center mb-4">
            <CircularProgress
              value={scheduledPercentage}
              size={64}
              strokeWidth={4}
              className="text-blue-600 dark:text-blue-500"
            />
          </div>
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 tracking-tight">
              {formatCurrency(metrics.scheduledTuitionsValue)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {scheduledPercentage.toFixed(1)}% do total
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Vencidas */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
        <CardHeader className="pb-4 bg-gradient-to-r from-red-50/50 to-transparent dark:from-red-900/20 dark:to-transparent">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className="p-1 bg-red-100 dark:bg-red-900/30 rounded-full">
              <TbCalendarDue className="w-4 h-4 text-red-600 dark:text-red-400" />
            </div>
            Vencidas (Mês Atual)
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex items-center justify-center mb-4">
            <CircularProgress
              value={overduePercentage}
              size={64}
              strokeWidth={4}
              className="text-red-600 dark:text-red-500"
            />
          </div>
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-red-600 dark:text-red-400 tracking-tight">
              {formatCurrency(metrics.overdueTuitionsValue)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {overduePercentage.toFixed(1)}% do total
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Receita Total Mensal */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
        <CardHeader className="pb-4 bg-gradient-to-r from-purple-50/50 to-transparent dark:from-purple-900/20 dark:to-transparent">
          <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
            <div className="p-1 bg-purple-100 dark:bg-purple-900/30 rounded-full">
              <TrendingUp className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
            Receita Total Mensal
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex items-center justify-center mb-4">
            <CircularProgress
              value={revenuePercentage}
              size={64}
              strokeWidth={4}
              className="text-purple-600 dark:text-purple-500"
            />
          </div>
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400 tracking-tight">
              {formatCurrency(metrics.monthlyRevenueAverage)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
              {revenuePercentage.toFixed(1)}% de {formatCurrency(expectedRevenue)}
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
              Previsto: {formatCurrency(expectedRevenue)}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 