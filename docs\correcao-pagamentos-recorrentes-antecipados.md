# Correção: Pagamentos Recorrentes Antecipados

## 📋 Problema Identificado

O sistema estava gerando automaticamente o próximo pagamento recorrente sempre que um pagamento era marcado como "paid", independentemente de quando o pagamento foi realizado em relação ao ciclo de cobrança.

### Exemplo do Problema:
- **Vencimento**: 15/08/2025
- **Data atual**: 25/07/2025 (ainda dentro do ciclo atual)
- **Problema**: Ao pagar antecipadamente, o sistema gerava automaticamente o pagamento para 15/09/2025

## 🔧 Solução Implementada

### 1. Função Corrigida: `create_next_recurring_payment`

**Arquivo**: `migrations/20250125_001_fix_recurring_payment_timing.sql`

**Nova Lógica**:
```sql
-- Verificar se o pagamento foi feito antecipadamente
IF v_current_date < v_paid_due_date THEN
  RETURN jsonb_build_object(
    'success', false,
    'error', 'Pagamento antecipado detectado. O próximo pagamento será criado automaticamente na data de vencimento.'
  );
END IF;
```

**Comportamento Corrigido**:
- ✅ **Pagamento no prazo** (data atual >= data vencimento): Cria próximo pagamento
- ✅ **Pagamento em atraso** (data atual > data vencimento): Cria próximo pagamento  
- ❌ **Pagamento antecipado** (data atual < data vencimento): NÃO cria próximo pagamento

### 2. Função de Processamento Periódico

**Arquivo**: `migrations/20250125_002_create_pending_recurring_payments_processor.sql`

**Funções Criadas**:

#### `process_pending_recurring_payments()`
- Processa pagamentos que deveriam ter próximos pagamentos criados
- Deve ser executada periodicamente (ex: diariamente)
- Cria pagamentos para ciclos que já venceram

#### `check_pending_recurring_payments()`
- Verifica quais pagamentos precisam ser processados
- Útil para monitoramento e debug
- Retorna lista de pagamentos pendentes

## 🔄 Fluxo Corrigido

### Cenário 1: Pagamento Antecipado
1. **Usuário paga antecipadamente** (ex: 25/07 para vencimento 15/08)
2. **Trigger executa** → `create_next_recurring_payment()`
3. **Função detecta pagamento antecipado** → Retorna erro informativo
4. **Próximo pagamento NÃO é criado** ainda
5. **Processamento periódico** → Quando 15/08 chegar, cria o próximo pagamento

### Cenário 2: Pagamento no Prazo/Atraso
1. **Usuário paga no prazo ou em atraso** (ex: 15/08 ou depois)
2. **Trigger executa** → `create_next_recurring_payment()`
3. **Função cria próximo pagamento** normalmente
4. **Próximo pagamento criado** para 15/09

## 📊 Monitoramento

### Verificar Pagamentos Pendentes
```sql
SELECT public.check_pending_recurring_payments();
```

### Processar Pagamentos Pendentes Manualmente
```sql
SELECT public.process_pending_recurring_payments();
```

### Verificar Logs de Pagamentos Antecipados
```sql
-- Buscar pagamentos que foram detectados como antecipados
SELECT 
    p.id,
    p.due_date,
    p.paid_at,
    p.status,
    CURRENT_DATE - p.due_date as days_difference,
    CASE 
        WHEN CURRENT_DATE < p.due_date THEN 'Antecipado'
        WHEN CURRENT_DATE = p.due_date THEN 'No prazo'
        ELSE 'Em atraso'
    END as payment_timing
FROM payments p
WHERE p.payment_type = 'recurring'
    AND p.status = 'paid'
ORDER BY p.due_date DESC;
```

## 🚀 Implementação de Processamento Automático

### Recomendação: Cron Job Diário

Para automatizar o processamento, configure um cron job que execute diariamente:

```sql
-- Executar diariamente às 00:30
SELECT public.process_pending_recurring_payments();
```

### Alternativa: Edge Function

Criar uma Edge Function que execute periodicamente:

```typescript
// edge-functions/process-recurring-payments/index.ts
import { createClient } from '@supabase/supabase-js'

Deno.serve(async (req) => {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
  )

  const { data, error } = await supabase.rpc('process_pending_recurring_payments')

  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }

  return new Response(JSON.stringify(data), {
    headers: { 'Content-Type': 'application/json' }
  })
})
```

## ✅ Benefícios da Correção

1. **Precisão no Ciclo de Cobrança**: Pagamentos antecipados não geram próximos pagamentos prematuramente
2. **Flexibilidade**: Usuários podem pagar antecipadamente sem afetar o ciclo
3. **Controle**: Sistema mantém controle adequado sobre quando criar próximos pagamentos
4. **Monitoramento**: Funções de verificação permitem acompanhar o status
5. **Recuperação**: Processamento periódico garante que nenhum pagamento seja perdido

## 🔍 Testes Recomendados

1. **Teste Pagamento Antecipado**:
   - Criar membership com vencimento futuro
   - Pagar antes da data de vencimento
   - Verificar que próximo pagamento NÃO foi criado

2. **Teste Pagamento no Prazo**:
   - Pagar na data de vencimento
   - Verificar que próximo pagamento foi criado

3. **Teste Processamento Periódico**:
   - Executar `process_pending_recurring_payments()`
   - Verificar que pagamentos pendentes foram criados

## 📝 Notas Importantes

- A correção é **retrocompatível** - não afeta pagamentos existentes
- Pagamentos já criados incorretamente precisam ser tratados manualmente se necessário
- O processamento periódico deve ser configurado para execução automática
- Logs e monitoramento ajudam a identificar problemas rapidamente
