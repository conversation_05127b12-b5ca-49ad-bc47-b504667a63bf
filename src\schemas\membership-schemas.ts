import { z } from 'zod'

// Enums base
const statusMembresiaSchema = z.enum(['active', 'paused', 'canceled', 'expired'])

// Schema para criação de matrícula
export const criarMembershipSchema = z.object({
  alunoId: z.string().uuid('ID do estudante inválido'),
  planoId: z.string().uuid('ID do plano inválido'),
  dataInicio: z.string().date('Data de início inválida').optional(),
  metadata: z.record(z.unknown()).optional().default({}),
})

// Schema para atualização de status
export const atualizarStatusMembershipSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula inválido'),
  novoStatus: statusMembresiaSchema,
  motivo: z.string().optional(),
})

// Schema para pausar matrícula
export const pausarMembershipSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula inválido'),
  motivo: z.string().optional(),
  metadata: z.record(z.unknown()).optional().default({}),
})

// Schema para cancelar matrícula
export const cancelarMembershipSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula inválido'),
  motivo: z.string().optional(),
  aplicarTaxaCancelamento: z.boolean().optional().default(true),
  metadata: z.record(z.unknown()).optional().default({}),
})

// Schema para buscar matrículas ativas do estudante
export const buscarMembershipsAtivasSchema = z.object({
  alunoId: z.string().uuid('ID do estudante inválido'),
})

// Schema para calcular preço
export const calcularPrecoSchema = z.object({
  planoId: z.string().uuid('ID do plano inválido'),
  dataCalculo: z.string().date('Data de cálculo inválida').optional(),
})

// Schema para processar cobrança
export const processarCobrancaMembershipSchema = z.object({
  membershipId: z.string().uuid('ID da matrícula inválido'),
})

// Relatório visão geral
export const overviewMembershipSchema = z.object({
  dataInicio: z.string().date('Data de início inválida'),
  dataFim: z.string().date('Data de fim inválida'),
})

export const estatisticasMembershipSchema = z.object({
  dataInicio: z.string().date('Data de início inválida'),
  dataFim: z.string().date('Data de fim inválida'),
})

/* Tipos */
export type CriarMembershipData = z.infer<typeof criarMembershipSchema>
export type AtualizarStatusMembershipData = z.infer<typeof atualizarStatusMembershipSchema>
export type PausarMembershipData = z.infer<typeof pausarMembershipSchema>
export type CancelarMembershipData = z.infer<typeof cancelarMembershipSchema>
export type BuscarMembershipsAtivasData = z.infer<typeof buscarMembershipsAtivasSchema>
export type CalcularPrecoData = z.infer<typeof calcularPrecoSchema>
export type ProcessarCobrancaMembershipData = z.infer<typeof processarCobrancaMembershipSchema>
export type OverviewMembershipData = z.infer<typeof overviewMembershipSchema>
export type EstatisticasMembershipData = z.infer<typeof estatisticasMembershipSchema>