---
inclusion: always
---

# Project Structure & Development Guidelines

## Architecture Patterns

### Multi-tenant Design
- **Subdomain-based tenant isolation**: Each academy operates on its own subdomain
- **Database-level isolation**: Use RLS policies with `tenant_auth.tenant_id()` functions
- **Middleware-driven routing**: Tenant detection happens in `middleware.ts`
- **Tenant utilities**: Centralized in `/src/services/tenant/`

### Next.js App Router Structure
```
src/app/
├── (auth-pages)/          # Grouped auth routes (login, signup)
├── protected/             # Authenticated user routes
└── tenant/               # Tenant-specific application routes
```

## Code Style & Conventions

### File Naming
- **Components**: PascalCase (e.g., `StudentCard.tsx`)
- **Pages/Routes**: kebab-case (e.g., `student-profile/page.tsx`)
- **Utilities**: camelCase (e.g., `formatDate.ts`)
- **Database migrations**: `YYYYMMDD_NNN_descriptive_name.sql`

### Component Organization
- Place reusable components in `/src/components/`
- UI primitives from shadcn/ui go in `/src/components/ui/`
- Feature-specific components should be co-located with their pages
- Export components as named exports, not default exports

### Import Patterns
Use path aliases consistently:
```typescript
import { Button } from "@/components/ui/button"
import { useAuth } from "@/hooks/useAuth"
import { studentSchema } from "@/schemas"
```

## Data Management

### State Management Rules
- **Server state**: Use TanStack Query for all API calls and caching
- **Client state**: Use Zustand for global client state
- **Form state**: Use React Hook Form with Zod validation

### Database Interactions
- All database queries must respect tenant isolation
- Use RLS policies instead of manual tenant filtering
- Migrations must be sequential and include rollback instructions

### API Patterns
- Server Actions for mutations when possible
- API routes for complex operations or third-party integrations
- Always validate input with Zod schemas

## Security & Performance

### Authentication Flow
- Supabase Auth handles user authentication
- Middleware enforces tenant access control
- Use `createClient()` from `@/lib/supabase` for server-side operations

### Performance Guidelines
- Implement proper loading states with Suspense boundaries
- Use React Server Components for data fetching when possible
- Optimize images and animations (Lottie files in `/public/`)

## Development Workflow

### Environment Setup
- Copy `.env.example` to `.env` and configure variables
- Run `npm run setup-subdomains` for local multi-tenant development
- Use Docker for consistent development environments

### Testing Approach
- Focus on integration tests for critical user flows
- Test tenant isolation thoroughly
- Validate RLS policies in database tests