/**
 * Componentes otimizados com memoização
 */

import { memo, Suspense } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeftIcon, 
  UsersIcon, 
  QrCodeIcon, 
  navigationIconProps 
} from '@/components/icons/attendance-icons';
import { formatFullDate, formatTime } from '@/lib/date-utils';
import { useClassStatus } from '@/hooks/useClassStatus';

// Componente de capacidade memoizado
export const CapacityDisplay = memo<{ classData: any }>(({ classData }) => {
  // Para aulas livres (sem turma), mostrar apenas presença atual
  if (!classData.class_group_id) {
    const attendanceCount = classData._count?.attendance || 0;
    return <>{attendanceCount} alunos</>;
  }

  return (
    <Suspense fallback="...">
      <CapacityDisplayAsync classData={classData} />
    </Suspense>
  );
});
CapacityDisplay.displayName = 'CapacityDisplay';

// Componente assíncrono para capacidade
const CapacityDisplayAsync = memo<{ classData: any }>(async ({ classData }) => {
  try { 
    const { getClassGroupEnrollments } = await import('../../../turmas/actions/class-group/get-class-group-enrollments');
    const enrollmentsResult = await getClassGroupEnrollments(classData.class_group_id, { status: 'active' });
    
    if (enrollmentsResult.success && 'data' in enrollmentsResult && enrollmentsResult.data) {
      const enrolledCount = enrollmentsResult.data.length;
      return <>{enrolledCount}{classData.max_capacity ? `/${classData.max_capacity}` : ''} alunos</>;
    }
  } catch (error) {
    console.error('Erro ao buscar capacidade:', error);
  }

  return <>...{classData.max_capacity ? `/${classData.max_capacity}` : ''} alunos</>;
});
CapacityDisplayAsync.displayName = 'CapacityDisplayAsync';

// Header de navegação memoizado
export const ClassAttendanceHeader = memo<{
  classId: string;
  isActive: boolean;
}>(({ classId, isActive }) => (
  <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
    <div className="flex gap-2">
      <Button variant="outline" asChild>
        <Link href="/presenca">
          <ArrowLeftIcon {...navigationIconProps} className="mr-2" />
          Voltar
        </Link>
      </Button>
      {isActive && (
        <Button variant="outline" asChild>
          <Link href={`/presenca/${classId}?tab=qr`}>
            <QrCodeIcon {...navigationIconProps} className="mr-2" />
            QR Code
          </Link>
        </Button>
      )}
    </div>
  </div>
));
ClassAttendanceHeader.displayName = 'ClassAttendanceHeader';

// Card de informações da aula memoizado
export const ClassInfoCard = memo<{
  classData: any;
}>(({ classData }) => {
  const { status, statusConfig } = useClassStatus(classData);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              {classData.name || classData.class_group?.name || 'Aula Livre'}
              {classData.class_group?.name && classData.name && classData.name !== classData.class_group.name && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  ({classData.class_group.name})
                </span>
              )}
            </CardTitle>
            <CardDescription>
              {formatFullDate(classData.start_time)} • {formatTime(classData.start_time)} - {formatTime(classData.end_time)}
            </CardDescription>
          </div>
          <Badge 
            variant={statusConfig.variant}
            className={statusConfig.className}
          >
            {statusConfig.icon}
            {statusConfig.label}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <UsersIcon {...navigationIconProps} className="text-muted-foreground" />
            <span className="font-medium text-foreground">Instrutor:</span>
            {classData.instructor?.id ? (
              <Link 
                href={`/perfil/${classData.instructor.id}`}
                className="text-muted-foreground hover:text-primary transition-colors hover:underline"
              >
                {classData.instructor.full_name || classData.instructor.first_name || 'Não definido'}
              </Link>
            ) : (
              <span className="text-muted-foreground">Não definido</span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium text-foreground">Capacidade:</span>
            <span className="text-muted-foreground">
              <CapacityDisplay classData={classData} />
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium text-foreground">Filial:</span>
            <span className="text-muted-foreground">{classData.branch?.name || 'Não definida'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
ClassInfoCard.displayName = 'ClassInfoCard';

// Skeleton otimizado
export const ClassAttendanceSkeleton = memo(() => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="flex items-center justify-between">
      <div>
        <div className="h-8 bg-muted rounded animate-pulse w-64 mb-2" />
        <div className="h-4 bg-muted rounded animate-pulse w-96" />
      </div>
      <div className="flex gap-2">
        <div className="h-10 bg-muted rounded animate-pulse w-24" />
        <div className="h-10 bg-muted rounded animate-pulse w-24" />
      </div>
    </div>

    {/* Card info skeleton */}
    <Card>
      <CardHeader>
        <div className="h-6 bg-muted rounded animate-pulse w-48 mb-2" />
        <div className="h-4 bg-muted rounded animate-pulse w-72" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="h-4 bg-muted rounded animate-pulse" />
          ))}
        </div>
      </CardContent>
    </Card>

    {/* Tabs skeleton */}
    <div className="space-y-4">
      <div className="h-10 bg-muted rounded animate-pulse" />
      <div className="h-96 bg-muted rounded animate-pulse" />
    </div>
  </div>
));
ClassAttendanceSkeleton.displayName = 'ClassAttendanceSkeleton';
