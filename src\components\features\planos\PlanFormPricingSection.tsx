'use client'

import React, { useState, useEffect } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RefreshCw, Calendar, CreditCard, Clock, HelpCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { usePlanForm } from './PlanFormContext'
import { TooltipProvider, TooltipRoot, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
import { PlanFormPerSessionSection } from './PlanFormPerSessionSection'
import { PlanFormTrialSection } from './PlanFormTrialSection'
import { tipoPrecificacaoSchema, unidadeFrequenciaSchema, configuracaoPrecoSchema as pricingSchema } from '@/schemas/plan-schemas'

type PricingFormValues = z.infer<typeof pricingSchema>

interface PlanFormPricingSectionProps {
  onSubmit?: (data: PricingFormValues) => void
  defaultValues?: Partial<PricingFormValues>
}

const pricingTypes = [
  {
    id: 'recurring' as const,
    label: 'Recorrente',
    description: 'Cobrar pagamentos em um cronograma recorrente',
    icon: <RefreshCw className="h-5 w-5" />,
    color: 'blue',
  },
  {
    id: 'one-time' as const,
    label: 'Único',
    description: 'Pagamento único',
    icon: <CreditCard className="h-5 w-5" />,
    color: 'purple',
  },
  // {
  //   id: 'per-session' as const,
  //   label: 'Por aula',
  //   description: 'Cobrança por cada aula que o aluno participar',
  //   icon: <Calendar className="h-5 w-5" />,
  //   color: 'green',
  // },
  // {
  //   id: 'trial' as const,
  //   label: 'Trial',
  //   description: 'Ofereça um período de teste gratuito ou com desconto antes da cobrança regular',
  //   icon: <Clock className="h-5 w-5" />,
  //   color: 'orange',
  // },
] as const

const frequencyOptions = [
  { value: 'day', label: 'Dia(s)' },
  { value: 'week', label: 'Semana(s)' },
  { value: 'month', label: 'Mês(es)' },
  { value: 'year', label: 'Ano(s)' }
]

export function PlanFormPricingSection({ onSubmit, defaultValues }: PlanFormPricingSectionProps) {
  const { formData, updateSection, submissionErrors } = usePlanForm()
  const [selectedType, setSelectedType] = useState<'recurring' | 'one-time' | 'per-session' | 'trial'>(
    formData.pricing?.tipo || 'recurring'
  )

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    watch,
    setValue
  } = useForm<PricingFormValues>({
    resolver: zodResolver(pricingSchema),
    defaultValues: {
      // Valores padrão são ajustados para corresponder aos tipos numéricos do schema
      tipo: formData.pricing?.tipo || selectedType,
      valor: (formData.pricing as any)?.valor ?? 0,
      frequencia: (formData.pricing as any)?.frequencia || 'month',
      numeroFrequencia: (formData.pricing as any)?.numeroFrequencia ?? 1,
      maxPagamentos: (formData.pricing as any)?.maxPagamentos ?? undefined,
      taxaInscricao: (formData.pricing as any)?.taxaInscricao ?? undefined,
      taxaAtraso: (formData.pricing as any)?.taxaAtraso ?? undefined,
      diasAtraso: (formData.pricing as any)?.diasAtraso ?? 5,
      custo: (formData.pricing as any)?.custo ?? undefined,
      renovacaoAutomatica: (formData.pricing as any)?.renovacaoAutomatica ?? false,
      descontos: (formData.pricing as any)?.descontos ?? {
        segundoMembro: undefined,
        terceiroMembro: undefined,
        quartoMembro: undefined,
      }
    }
  })

  useEffect(() => {
    const initialData = { ...watch(), tipo: selectedType } as PricingFormValues
    updateSection('pricing', initialData)
  }, [])

  const formatToCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value)
  }

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLInputElement>, fieldOnChange: (value: number) => void) => {
    const value = e.target.value.replace(/\D/g, '')
    fieldOnChange(Number(value) / 100)
  }

  const valorError = submissionErrors?.pricing?.valor?._errors?.[0]

  // Inputs de moeda agora utilizam valueAsNumber para compatibilidade com o schema

  const [maxPagamentosOption, setMaxPagamentosOption] = useState<'no-limit' | 'custom'>(
    (formData.pricing as any)?.maxPagamentos ? 'custom' : 'no-limit'
  )

  const maxPagamentos = watch('maxPagamentos')

  // Sincronizar mudanças com o context. Para o tipo "trial",
  // os dados detalhados são gerenciados pelo PlanFormTrialSection.
  useEffect(() => {
    const subscription = watch((value) => {
      if (selectedType !== 'trial') {
        updateSection('pricing', { ...value, tipo: selectedType })
      }
    })
    return () => subscription.unsubscribe()
  }, [watch, updateSection, selectedType])

  const handleTypeChange = (type: typeof selectedType) => {
    setSelectedType(type)
    setValue('tipo', type)

    // Apenas atualiza o tipo no contexto. O componente de seção (ex: PlanFormTrialSection)
    // será responsável por montar e popular os dados detalhados.
    updateSection('pricing', { tipo: type })
  }

  const handleFormSubmit = (data: PricingFormValues) => {
    updateSection('pricing', data)
    onSubmit?.(data)
  }

  return (
    <div className="space-y-6">
      {/* Tipos de Preço */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
        {pricingTypes.map((type) => {
          const selected = selectedType === type.id

          const baseSelected = selected
            ? `bg-${type.color}-100 dark:bg-${type.color}-900/30 border-${type.color}-300 dark:border-${type.color}-600 text-${type.color}-700 dark:text-${type.color}-300 hover:bg-${type.color}-200 dark:hover:bg-${type.color}-900/50`
            : 'border-input text-foreground hover:bg-accent hover:text-accent-foreground'

          // As classes acima precisam estar listadas explicitamente para funcionar com Tailwind.
          // Portanto, definimos um mapeamento fixo.

          const selectedClassMap: Record<typeof type.color, string> = {
            blue: 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50',
            purple: 'bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-600 text-purple-700 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-900/50',
            // green: 'bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50',
            // orange: 'bg-orange-100 dark:bg-orange-900/30 border-orange-300 dark:border-orange-600 text-orange-700 dark:text-orange-300 hover:bg-orange-200 dark:hover:bg-orange-900/50',
          }

          const iconColorMap: Record<typeof type.color, string> = {
            blue: 'text-blue-600 dark:text-blue-400',
            purple: 'text-purple-600 dark:text-purple-400',
            // green: 'text-green-600 dark:text-green-400',
            // orange: 'text-orange-600 dark:text-orange-400',
          }

          const classes = selected ? selectedClassMap[type.color] : 'border-input text-foreground hover:bg-accent hover:text-accent-foreground'

          return (
            <Button
              key={type.id}
              type="button"
              variant="outline"
              className={cn('flex flex-col items-center gap-2 p-4 h-auto text-center', classes)}
              onClick={() => handleTypeChange(type.id)}
            >
              <div className={iconColorMap[type.color]}>{type.icon}</div>
              <span className="text-sm font-medium">{type.label}</span>
            </Button>
          )
        })}
      </div>

      {/* Descrição do tipo selecionado */}
      <div className="p-4 bg-muted rounded-lg border-l-4 border-teal-500 dark:border-teal-400">
        <p className="text-sm text-muted-foreground">{pricingTypes.find((t) => t.id === selectedType)?.description}</p>
      </div>

      {/* Conteúdo para Recurring */}
      {selectedType === 'recurring' && (
        <div className="space-y-8">
          {/* Seção de Preço */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Preço</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Valor */}
              <div className="space-y-2">
                <Label htmlFor="valor" className="text-sm font-medium">
                  VALOR
                </Label>
                <Controller
                  name="valor"
                  control={control}
                  render={({ field }) => (
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
                      <Input
                        id="valor"
                        type="text"
                        placeholder="0,00"
                        value={field.value ? formatToCurrency(field.value) : ''}
                        onChange={(e) => handleCurrencyChange(e, field.onChange)}
                        className={cn('pl-8', ((errors as any)?.valor || valorError) && 'border-red-500 focus:border-red-500')}
                      />
                    </div>
                  )}
                />
                {valorError && <p className="text-sm text-red-600">{valorError}</p>}
                {(errors as any)?.valor && <p className="text-sm text-red-600">{(errors as any).valor.message}</p>}
              </div>

              {/* Frequência */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">FREQUÊNCIA</Label>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground whitespace-nowrap">A cada</span>
                  <Input placeholder="1" type="number" min="1" {...register('numeroFrequencia', { valueAsNumber: true })} className="w-16 text-center" />
                  <Controller
                    name="frequencia"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {frequencyOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
                {(errors as any)?.numeroFrequencia && <p className="text-sm text-red-600">{(errors as any).numeroFrequencia.message}</p>}
              </div>

              {/* Máximo de Pagamentos */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label className="text-sm font-medium">MÁXIMO DE PAGAMENTOS</Label>
                  <TooltipProvider>
                    <TooltipRoot>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-sm">
                        <div className="space-y-2">
                          <p className="font-medium">O que é o Máximo de Pagamentos?</p>
                          <p>Define um limite para a quantidade de cobranças recorrentes que podem ser feitas em uma assinatura.</p>
                          <div className="space-y-1">
                            <p className="font-medium">Exemplo prático:</p>
                            <p>• Custo: R$ 100</p>
                            <p>• Frequência: Todo 1 mês</p>
                            <p>• Máximo de Pagamentos: 12</p>
                          </div>
                          <p>O cliente será cobrado R$ 100 por mês, durante 12 meses. Depois disso, a cobrança para automaticamente.</p>
                          <p className="text-xs text-muted-foreground">Se estiver como "Sem Limite", a cobrança continuará indefinidamente até que o plano seja cancelado manualmente.</p>
                        </div>
                      </TooltipContent>
                    </TooltipRoot>
                  </TooltipProvider>
                </div>
                <Select
                  value={maxPagamentosOption}
                  onValueChange={(value) => {
                    setMaxPagamentosOption(value as any)
                    if (value === 'no-limit') {
                      setValue('maxPagamentos', undefined)
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-limit">Sem Limite</SelectItem>
                    <SelectItem value="custom">Personalizado</SelectItem>
                  </SelectContent>
                </Select>
                {maxPagamentosOption === 'custom' && (
                  <Input
                    placeholder="Número de pagamentos"
                    type="number"
                    min="1"
                    {...register('maxPagamentos', { valueAsNumber: true })}
                    className="mt-2"
                  />
                )}
              </div>
            </div>
          </div>

          {/* Seção de Taxas */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Taxas</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Taxa de Inscrição */}
              <div className="space-y-2">
                <Label htmlFor="taxaInscricao" className="text-sm font-medium">
                  TAXA DE INSCRIÇÃO
                </Label>
                <Controller
                  name="taxaInscricao"
                  control={control}
                  render={({ field }) => (
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
                      <Input
                        id="taxaInscricao"
                        type="text"
                        placeholder="0,00"
                        value={field.value ? formatToCurrency(field.value) : ''}
                        onChange={(e) => handleCurrencyChange(e, field.onChange)}
                        className="pl-8"
                      />
                    </div>
                  )}
                />
              </div>

              {/* Taxa de Atraso */}
              <div className="space-y-2">
                <Label htmlFor="taxaAtraso" className="text-sm font-medium">
                  TAXA DE ATRASO
                </Label>
                <Controller
                  name="taxaAtraso"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
                        <Input
                          id="taxaAtraso"
                          type="text"
                          placeholder="0,00"
                          value={field.value ? formatToCurrency(field.value) : ''}
                          onChange={(e) => handleCurrencyChange(e, field.onChange)}
                          className="pl-8"
                        />
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span className="whitespace-nowrap">quando</span>
                        <Input placeholder="5" type="number" min="1" {...register('diasAtraso', { valueAsNumber: true })} className="w-16 text-center" />
                        <span className="whitespace-nowrap">dias de atraso</span>
                      </div>
                    </div>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conteúdo para One-time */}
      {selectedType === 'one-time' && (
        <div className="space-y-8">
          {/* Seção de Preço */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Preço</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Custo */}
              <div className="space-y-2">
                <Label htmlFor="custo" className="text-sm font-medium">
                  CUSTO
                </Label>
                <Controller
                  name="custo"
                  control={control}
                  render={({ field }) => (
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
                      <Input
                        id="custo"
                        type="text"
                        placeholder="0,00"
                        value={field.value ? formatToCurrency(field.value) : ''}
                        onChange={(e) => handleCurrencyChange(e, field.onChange)}
                        className="pl-8"
                      />
                    </div>
                  )}
                />
              </div>

              {/* Renovação Automática */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input id="renovacaoAutomatica" type="checkbox" {...register('renovacaoAutomatica')} className="rounded border-input text-teal-600 focus:ring-teal-500 dark:border-input" />
                  <Label htmlFor="renovacaoAutomatica" className="text-sm font-medium">
                    Cobrar valor da assinatura novamente na renovação automática
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Seção de Taxas */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Taxas</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Taxa de Inscrição */}
              <div className="space-y-2">
                <Label htmlFor="taxaInscricao" className="text-sm font-medium">
                  TAXA DE INSCRIÇÃO
                </Label>
                <Controller
                  name="taxaInscricao"
                  control={control}
                  render={({ field }) => (
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
                      <Input
                        id="taxaInscricao"
                        placeholder="0,00"
                        type="text"
                        value={field.value ? formatToCurrency(field.value) : ''}
                        onChange={(e) => handleCurrencyChange(e, field.onChange)}
                        className="pl-8"
                      />
                    </div>
                  )}
                />
              </div>

              {/* Taxa de Atraso */}
              <div className="space-y-2">
                <Label htmlFor="taxaAtraso" className="text-sm font-medium">
                  TAXA DE ATRASO
                </Label>
                <Controller
                  name="taxaAtraso"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">R$</span>
                        <Input
                          id="taxaAtraso"
                          type="text"
                          placeholder="0,00"
                          value={field.value ? formatToCurrency(field.value) : ''}
                          onChange={(e) => handleCurrencyChange(e, field.onChange)}
                          className="pl-8"
                        />
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span className="whitespace-nowrap">quando</span>
                        <Input placeholder="5" type="number" min="1" {...register('diasAtraso', { valueAsNumber: true })} className="w-16 text-center" />
                        <span className="whitespace-nowrap">dias de atraso</span>
                      </div>
                    </div>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conteúdo para Per-session */}
      {/* {selectedType === 'per-session' && (
        <PlanFormPerSessionSection />
      )} */}

      {/* Conteúdo para Trial */}
      {/* {selectedType === 'trial' && (
        <PlanFormTrialSection />
      )} */}
    </div>
  )
}
