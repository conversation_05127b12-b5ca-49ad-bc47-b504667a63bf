interface StatusItemProps {
  label: string;
  status: string;
  statusColor?: 'green' | 'red' | 'orange' | 'gray';
}

const statusColorMap = {
  green: 'text-green-600 dark:text-green-400',
  red: 'text-red-600 dark:text-red-400',
  orange: 'text-orange-600 dark:text-orange-400',
  gray: 'text-gray-600 dark:text-gray-400'
};

export function StatusItem({ label, status, statusColor = 'gray' }: StatusItemProps) {
  return (
    <div className="flex justify-between items-center">
      <span className="text-sm text-gray-900 dark:text-gray-100">{label}</span>
      <span className={`text-xs font-medium ${statusColorMap[statusColor]}`}>{status}</span>
    </div>
  );
}