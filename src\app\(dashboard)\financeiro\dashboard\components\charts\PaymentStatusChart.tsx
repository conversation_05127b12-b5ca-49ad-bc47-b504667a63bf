"use client";

/**
 * Componente de Gráfico de Status de Pagamentos - Fase 3
 * Exibe a distribuição de pagamentos por status em formato de gráfico de rosca
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import { CheckCircle, Clock, AlertTriangle, XCircle, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { getPaymentStatusChart } from '../../actions/charts/payment-chart-actions';
import { PaymentStatusData } from '../../types/dashboard-types';
import { formatCurrency } from '@/utils/format-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface PaymentStatusChartProps {
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
}

// ============================================================================
// MAPEAMENTO DE ÍCONES POR STATUS
// ============================================================================

const statusIcons = {
  'paid': CheckCircle,
  'pending': Clock,
  'overdue': AlertTriangle,
  'canceled': XCircle,
  'awaiting_confirmation': HelpCircle
};

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100">
          {data.label}
        </p>
        <p className="text-sm text-blue-600 dark:text-blue-400">
          <span className="font-medium">Valor: </span>
          {data.formattedAmount}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Quantidade: </span>
          {data.count} pagamentos
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">Participação: </span>
          {data.percentage.toFixed(1)}%
        </p>
      </div>
    );
  }
  return null;
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const PaymentStatusChart: React.FC<PaymentStatusChartProps> = ({
  className
}) => {
  const [data, setData] = useState<PaymentStatusData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // CARREGAMENTO DE DADOS
  // ============================================================================

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await getPaymentStatusChart();

        if (!result.success) {
          throw new Error(result.error || 'Erro ao carregar dados');
        }

        setData(result.data || []);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        setError(errorMessage);
        console.error('Erro ao carregar gráfico de status de pagamentos:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // ============================================================================
  // CÁLCULOS
  // ============================================================================

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);
  const totalCount = data.reduce((sum, item) => sum + item.count, 0);

  // ============================================================================
  // RENDER
  // ============================================================================

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Status dos Pagamentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Status dos Pagamentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <div className="text-red-500 mb-2">
                <XCircle className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Status dos Pagamentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-400 mb-2">
                <HelpCircle className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                Nenhum pagamento encontrado
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Status dos Pagamentos
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
              <span className="font-medium">Total: {formatCurrency(totalAmount)}</span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={80}
                outerRadius={120}
                paddingAngle={2}
                dataKey="amount"
                animationDuration={800}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Lista de Status */}
        <div className="mt-4 space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          {data.map((item, index) => {
            const IconComponent = statusIcons[item.status as keyof typeof statusIcons] || HelpCircle;
            
            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <IconComponent className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {item.label}
                  </span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {item.formattedAmount}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {item.count} pagamentos • {item.percentage.toFixed(1)}%
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Resumo */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total de Pagamentos</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {totalCount}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Valor Total</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {formatCurrency(totalAmount)}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
