"use client";

/**
 * Gráfico de Retenção por Coorte
 * Exibe a taxa de retenção de alunos agrupados por período de entrada
 */

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { TrendingUp, Users, Calendar, Target } from 'lucide-react';
import { cn } from '@/lib/utils';

import { formatPercentage } from '../../utils/dashboard-utils';

// ============================================================================
// TIPOS
// ============================================================================

interface CohortData {
  period: string;
  month1: number;
  month2: number;
  month3: number;
  month6: number;
  month12: number;
  cohortSize: number;
}

interface RetentionCohortChartProps {
  data?: CohortData[];
  loading?: boolean;
  className?: string;
}

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

// ============================================================================
// COMPONENTE DE TOOLTIP CUSTOMIZADO
// ============================================================================

const CustomTooltip: React.FC<ChartTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">
          Coorte: {label}
        </p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            <span className="font-medium">{entry.name}: </span>
            {formatPercentage(entry.value)}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// ============================================================================
// DADOS MOCK PARA DEMONSTRAÇÃO
// ============================================================================

const mockData: CohortData[] = [
  {
    period: 'Jan 2024',
    month1: 100,
    month2: 85,
    month3: 78,
    month6: 65,
    month12: 55,
    cohortSize: 20
  },
  {
    period: 'Fev 2024',
    month1: 100,
    month2: 88,
    month3: 82,
    month6: 70,
    month12: 0, // Ainda não completou 12 meses
    cohortSize: 25
  },
  {
    period: 'Mar 2024',
    month1: 100,
    month2: 90,
    month3: 85,
    month6: 0, // Ainda não completou 6 meses
    month12: 0,
    cohortSize: 18
  },
  {
    period: 'Abr 2024',
    month1: 100,
    month2: 87,
    month3: 0, // Ainda não completou 3 meses
    month6: 0,
    month12: 0,
    cohortSize: 22
  },
  {
    period: 'Mai 2024',
    month1: 100,
    month2: 0, // Ainda não completou 2 meses
    month3: 0,
    month6: 0,
    month12: 0,
    cohortSize: 15
  }
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const RetentionCohortChart: React.FC<RetentionCohortChartProps> = ({
  data = mockData,
  loading = false,
  className = ''
}) => {
  const [chartData, setChartData] = useState<CohortData[]>([]);

  useEffect(() => {
    if (data) {
      // Filtrar apenas coortes com dados suficientes para análise
      const filteredData = data.filter(cohort => cohort.month2 > 0);
      setChartData(filteredData);
    }
  }, [data]);

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Retenção por Coorte
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (chartData.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Retenção por Coorte
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <Target className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Dados insuficientes para análise de coorte</p>
              <p className="text-sm mt-1">Aguarde mais dados históricos</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calcular média de retenção
  const avgRetention = {
    month1: chartData.reduce((sum, cohort) => sum + cohort.month1, 0) / chartData.length,
    month2: chartData.filter(c => c.month2 > 0).reduce((sum, cohort) => sum + cohort.month2, 0) / chartData.filter(c => c.month2 > 0).length,
    month3: chartData.filter(c => c.month3 > 0).reduce((sum, cohort) => sum + cohort.month3, 0) / chartData.filter(c => c.month3 > 0).length,
    month6: chartData.filter(c => c.month6 > 0).reduce((sum, cohort) => sum + cohort.month6, 0) / chartData.filter(c => c.month6 > 0).length,
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Retenção por Coorte
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {chartData.length} coortes analisadas
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="period" 
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              <Line
                type="monotone"
                dataKey="month1"
                stroke="#10b981"
                strokeWidth={2}
                name="1º Mês"
                connectNulls={false}
              />
              <Line
                type="monotone"
                dataKey="month2"
                stroke="#3b82f6"
                strokeWidth={2}
                name="2º Mês"
                connectNulls={false}
              />
              <Line
                type="monotone"
                dataKey="month3"
                stroke="#f59e0b"
                strokeWidth={2}
                name="3º Mês"
                connectNulls={false}
              />
              <Line
                type="monotone"
                dataKey="month6"
                stroke="#ef4444"
                strokeWidth={2}
                name="6º Mês"
                connectNulls={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Estatísticas de Retenção Média */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">1º Mês</div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              {formatPercentage(avgRetention.month1 || 0)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">2º Mês</div>
            <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {formatPercentage(avgRetention.month2 || 0)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">3º Mês</div>
            <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
              {formatPercentage(avgRetention.month3 || 0)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">6º Mês</div>
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              {formatPercentage(avgRetention.month6 || 0)}
            </div>
          </div>
        </div>

        {/* Insights */}
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Insights de Retenção
            </span>
          </div>
          <div className="text-sm text-blue-700 dark:text-blue-300">
            {avgRetention.month2 > 80 ? (
              "Excelente retenção nos primeiros meses. Continue focando na experiência do aluno."
            ) : avgRetention.month2 > 60 ? (
              "Boa retenção inicial. Considere melhorar o onboarding para reduzir o churn precoce."
            ) : (
              "Retenção baixa nos primeiros meses. Revise o processo de integração de novos alunos."
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
