'use client'

import { useState, useCallback, useRef } from 'react'
import { Card } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Co<PERSON>, Check, QrCode, Smartphone } from 'lucide-react'
import { toast } from 'sonner'
import { PixQRCodeProps } from '../types/checkout-types'
import { PixCanvas } from 'react-qrcode-pix'

export function PixQRCode({ qrCodeData, loading = false }: PixQRCodeProps) {
  const [copied, setCopied] = useState(false)
  const [pixCode, setPixCode] = useState<string>('')
  const pixCodeLoadedRef = useRef(false)

  // Generate stable code for this component instance
  const stableCode = useRef(`RQP${crypto.randomUUID().slice(0, 8)}`).current

  // Stable callback to prevent infinite re-renders
  const handlePixCodeLoad = useCallback((code: string) => {
    if (!pixCodeLoadedRef.current) {
      pixCodeLoadedRef.current = true
      setPixCode(code)
    }
  }, [])

  const handleCopyPixKey = async () => {
    try {
      await navigator.clipboard.writeText(qrCodeData.pixKey)
      setCopied(true)
      toast.success('Chave PIX copiada!')
      
      // Reset do estado após 2 segundos
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('Erro ao copiar chave PIX')
    }
  }

  const handleCopyPixCode = async () => {
    try {
      const codeToUse = pixCode || qrCodeData.brCode
      await navigator.clipboard.writeText(codeToUse)
      toast.success('Código PIX copiado!')
    } catch (error) {
      toast.error('Erro ao copiar código PIX')
    }
  }

  if (loading) {
    return (
      <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="text-center space-y-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto w-1/2" />
          <div className="flex justify-center">
            <div className="w-48 h-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto w-1/3" />
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="p-2 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/30 rounded-full">
              <QrCode className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Pagamento via PIX
            </h2>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Escaneie o QR Code ou copie a chave PIX
          </p>
        </div>

        <Separator />

        {/* QR Code */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="p-4 bg-white rounded-xl border border-gray-200 shadow-sm">
              <PixCanvas
                pixkey={qrCodeData.pixKey}
                merchant="ApexDojo"
                city="Brasil"
                code={stableCode}
                amount={qrCodeData.amount}
                onLoad={handlePixCodeLoad}
                variant="dots"
                color={{
                  eyes: '#1f2937',
                  body: '#374151'
                }}
                padding={14}
                bgColor="#ffffff"
                bgRounded
              />
            </div>
          </div>

          <div className="flex items-center justify-center gap-2 text-sm text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-950/30 rounded-lg py-2 px-4">
            <Smartphone className="w-4 h-4" />
            Abra o app do seu banco e escaneie o código
          </div>
        </div>

        <Separator />

        {/* Chave PIX para cópia manual */}
        <div className="space-y-3">
          <Label htmlFor="pix-key" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Ou copie a chave PIX:
          </Label>

          <div className="flex gap-2">
            <Input
              id="pix-key"
              value={pixCode || qrCodeData.pixKey}
              readOnly
              className="font-mono text-sm bg-gray-50 dark:bg-gray-800"
            />
            <Button
              onClick={handleCopyPixKey}
              variant="outline"
              size="sm"
              className="flex-shrink-0 border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
            >
              {copied ? (
                <Check className="w-4 h-4 text-gray-600" />
              ) : (
                <Copy className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Informações do pagamento */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-green-800 dark:text-green-200">Valor:</span>
              <span className="text-lg font-bold text-green-900 dark:text-green-100">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(qrCodeData.amount)}
              </span>
            </div>

            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-green-800 dark:text-green-200">Descrição:</span>
              <span className="text-sm text-green-900 dark:text-green-100 text-right max-w-[200px]">
                {qrCodeData.description}
              </span>
            </div>
          </div>
        </div>

        {/* Botão para copiar código completo (para desenvolvedores) */}
        {process.env.NODE_ENV === 'development' && (
          <details className="text-xs">
            <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
              Código PIX completo (desenvolvimento)
            </summary>
            <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
              <div className="flex gap-2">
                <Input
                  value={pixCode || qrCodeData.brCode}
                  readOnly
                  className="font-mono text-xs"
                />
                <Button
                  onClick={handleCopyPixCode}
                  variant="outline"
                  size="sm"
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </details>
        )}
      </div>
    </Card>
  )
}
