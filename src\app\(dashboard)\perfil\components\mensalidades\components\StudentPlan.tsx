'use client'

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

import {
  CreditCard,
  Calendar,
  Users,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Trash2,
  AlertTriangle
} from "lucide-react"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { StudentPlanProps } from "../types/types"
import { useStudentPlan } from "../hooks"
import { PlanChangeModal } from "./PlanChangeModal"
import { formatDateBrazil } from "@/utils/format"
import { useAdminStatus } from "@/hooks/user/Permissions/useAdminStatus"
import { toast } from "sonner"
import { cancelStudentPlan } from "../../../actions/plan-actions"

// Função utilitária para formatar datas no formato brasileiro considerando timezone
const formatPlanDate = (dateString: string) => {
  // Usar a função utilitária existente que já resolve problemas de timezone
  const formattedDate = formatDateBrazil(dateString);

  // Converter para o formato usado pelo date-fns
  const [day, month, year] = formattedDate.split('/');
  const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

  return format(date, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
      return <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
    case 'paused':
      return <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
    case 'canceled':
    case 'expired':
      return <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
    default:
      return <AlertCircle className="h-4 w-4 text-gray-500" />
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'active':
      return 'Ativo'
    case 'paused':
      return 'Pausado'
    case 'canceled':
      return 'Cancelado'
    case 'expired':
      return 'Expirado'
    default:
      return 'Desconhecido'
  }
}

const getStatusClassName = (status: string) => {
  switch (status) {
    case 'active':
      return 'text-green-600 border-green-600 dark:text-green-400 dark:border-green-400'
    case 'paused':
      return 'text-yellow-600 border-yellow-600 dark:text-yellow-400 dark:border-yellow-400'
    case 'canceled':
    case 'expired':
      return 'text-red-600 border-red-600 dark:text-red-400 dark:border-red-400'
    default:
      return 'text-gray-600 border-gray-600 dark:text-gray-400 dark:border-gray-400'
  }
}

export function StudentPlan({ userId, loading: externalLoading, onPlanUpdate }: StudentPlanProps) {
  const {
    currentPlan,
    availablePlans,
    loading,
    error,
    changePlan,
    refetch
  } = useStudentPlan(userId)

  const { isAdmin } = useAdminStatus()

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [changingPlan, setChangingPlan] = useState(false)
  const [cancelingPlan, setCancelingPlan] = useState(false)
  const [showCancelDialog, setShowCancelDialog] = useState(false)

  const isLoading = loading || externalLoading || changingPlan

  const handlePlanChange = async (planId: string, reason?: string) => {
    try {
      setChangingPlan(true)
      await changePlan(planId, reason)
      setIsModalOpen(false)

      // Pequeno delay para garantir que o servidor processou a mudança
      await new Promise(resolve => setTimeout(resolve, 500))

      // Garantir que os dados do plano sejam atualizados
      await refetch()

      // Notificar componente pai para atualizar dados de pagamento
      if (onPlanUpdate) {
        onPlanUpdate()
      }
    } catch (error) {
      console.error('Erro ao alterar plano:', error)
    } finally {
      setChangingPlan(false)
    }
  }

  const handleCancelPlan = async () => {
    if (!currentPlan?.membership_id) {
      toast.error('Não foi possível identificar a matrícula para cancelamento')
      return
    }

    try {
      setCancelingPlan(true)

      const result = await cancelStudentPlan({
        membershipId: currentPlan.membership_id,
        motivo: 'Cancelamento solicitado pelo administrador',
        aplicarTaxaCancelamento: false
      })

      if (result.success) {
        toast.success('Plano cancelado com sucesso')
        // Recarregar dados do plano
        await refetch()
        // Atualizar dados de pagamento no componente pai
        onPlanUpdate?.()
        setShowCancelDialog(false)
      } else {
        toast.error(result.errors?._form || 'Erro ao cancelar plano')
      }
    } catch (error) {
      console.error('Erro ao cancelar plano:', error)
      toast.error('Erro interno ao cancelar plano')
    } finally {
      setCancelingPlan(false)
    }
  }

  if (error) {
    return (
      <Card className="bg-white dark:bg-slate-800">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertCircle className="h-4 w-4" />
            <p className="text-sm">Erro ao carregar plano: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className="bg-white dark:bg-slate-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-2 text-gray-900 dark:text-gray-100">
            <CreditCard className="h-5 w-5 text-primary" />
            Plano Atual
          </CardTitle>
          {!isLoading && currentPlan && isAdmin && (
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => setIsModalOpen(true)}
                disabled={changingPlan}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                {changingPlan ? 'Alterando...' : 'Alterar Plano'}
              </Button>
              {currentPlan.status === 'active' && (
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => setShowCancelDialog(true)}
                  disabled={cancelingPlan}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Cancelar Plano
                </Button>
              )}
            </div>
          )}
        </CardHeader>
        
        <CardContent className="space-y-4">
          {isLoading ? (
            <div className="space-y-4 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="h-6 w-32 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
                <div className="h-6 w-20 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
              </div>
              <div className="h-4 w-full bg-slate-200 dark:bg-slate-700 rounded-md"></div>
              <div className="h-4 w-3/4 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
              <div className="grid grid-cols-2 gap-4">
                <div className="h-16 w-full bg-slate-200 dark:bg-slate-700 rounded-md"></div>
                <div className="h-16 w-full bg-slate-200 dark:bg-slate-700 rounded-md"></div>
              </div>
            </div>
          ) : currentPlan ? (
            <>
              {/* Cabeçalho do Plano */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {currentPlan.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    R$ {currentPlan.price.toFixed(2)} / {currentPlan.billing_period}
                  </p>
                </div>
                <Badge variant="outline" className={`flex items-center gap-1 ${getStatusClassName(currentPlan.status)}`}>
                  {getStatusIcon(currentPlan.status)}
                  {getStatusLabel(currentPlan.status)}
                </Badge>
              </div>

              {/* Modalidades Incluídas */}
              {currentPlan.modalities && currentPlan.modalities.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Modalidades Incluídas
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {currentPlan.modalities.map((modality, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {modality}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Informações de Data */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Data de Início
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatPlanDate(currentPlan.start_date)}
                  </p>
                </div>
                
                {currentPlan.next_billing_date && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Próxima Cobrança
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formatPlanDate(currentPlan.next_billing_date)}
                    </p>
                  </div>
                )}
              </div>

              {/* Data de Fim (se aplicável) */}
              {currentPlan.end_date && (
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>Plano expira em:</strong> {formatPlanDate(currentPlan.end_date)}
                  </p>
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <CreditCard className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Nenhum Plano Ativo
              </h3>
              {isAdmin && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-6 text-center max-w-sm">
                  Este aluno não possui um plano ativo no momento.
                </p>
              )}
              {isAdmin && (
              <Button
                onClick={() => setIsModalOpen(true)}
                className="flex items-center gap-2"
                size="default"
              >
                <CreditCard className="h-4 w-4" />
                Atribuir Plano
              </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Alert Dialog de Cancelamento */}
      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
              <AlertTriangle className="h-5 w-5" />
              Cancelar Plano
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300">
              Tem certeza que deseja cancelar este plano? Esta ação não pode ser desfeita e o aluno perderá acesso às modalidades incluídas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowCancelDialog(false)}
              disabled={cancelingPlan}
            >
              Manter Plano
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelPlan}
              disabled={cancelingPlan}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {cancelingPlan ? 'Cancelando...' : 'Confirmar Cancelamento'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Modal de Alteração de Plano */}
      <PlanChangeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        currentPlan={currentPlan}
        availablePlans={availablePlans}
        onPlanChange={handlePlanChange}
        loading={changingPlan}
      />
    </>
  )
}
