'use client'

import { useState, useEffect } from 'react'

interface VisibilityState {
  isTabVisible: boolean
  isWindowFocused: boolean
  isComponentMounted: boolean
}

export function useVisibilityChange() {
  const [visibilityState, setVisibilityState] = useState<VisibilityState>({
    isTabVisible: true,
    isWindowFocused: true,
    isComponentMounted: true
  })

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return

    // Initial state
    setVisibilityState(prev => ({
      ...prev,
      isTabVisible: !document.hidden,
      isWindowFocused: document.hasFocus()
    }))

    // Handle visibility change (tab switching)
    const handleVisibilityChange = () => {
      setVisibilityState(prev => ({
        ...prev,
        isTabVisible: !document.hidden
      }))
    }

    // Handle window focus/blur
    const handleFocus = () => {
      setVisibilityState(prev => ({
        ...prev,
        isWindowFocused: true
      }))
    }

    const handleBlur = () => {
      setVisibilityState(prev => ({
        ...prev,
        isWindowFocused: false
      }))
    }

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)
    window.addEventListener('blur', handleBlur)

    // Cleanup on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('blur', handleBlur)
      
      setVisibilityState(prev => ({
        ...prev,
        isComponentMounted: false
      }))
    }
  }, [])

  return visibilityState
}