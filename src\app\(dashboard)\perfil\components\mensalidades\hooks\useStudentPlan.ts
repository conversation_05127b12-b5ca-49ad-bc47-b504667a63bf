'use client'

import { useEffect, useState, useCallback, useRef } from "react"
import { StudentPlan, AvailablePlan } from "../types/types"
import { useVisibilityChange } from './useVisibilityChange'
import { REALTIME_CONFIG } from '../config/realtime'

interface UseStudentPlanReturn {
  currentPlan: StudentPlan | null
  availablePlans: AvailablePlan[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  changePlan: (planId: string, reason?: string) => Promise<void>
  // Real-time specific
  isPolling: boolean
  lastUpdate: Date | null
  connectionStatus: 'connected' | 'disconnected' | 'error'
}

export function useStudentPlan(userId: string): UseStudentPlanReturn {
  const [currentPlan, setCurrentPlan] = useState<StudentPlan | null>(null)
  const [availablePlans, setAvailablePlans] = useState<AvailablePlan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Real-time polling state
  const [isPolling, setIsPolling] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'error'>('connected')

  const { isTabVisible, isWindowFocused } = useVisibilityChange()
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastDataHashRef = useRef<string>('')

  // Create data hash for change detection
  const createDataHash = useCallback((data: any) => {
    return JSON.stringify({
      currentPlanId: data.currentPlan?.id || null,
      currentPlanName: data.currentPlan?.name || null,
      currentPlanStatus: data.currentPlan?.status || null,
      availablePlansCount: data.availablePlans?.length || 0,
      availablePlansIds: data.availablePlans?.map((p: any) => p.id).sort() || []
    })
  }, [])

  // Calculate polling interval based on visibility
  const getPollingInterval = useCallback(() => {
    if (!REALTIME_CONFIG.ENABLE_REAL_TIME) return 0 // Disabled
    if (!isTabVisible) return REALTIME_CONFIG.POLLING_INTERVALS.BACKGROUND
    if (!isWindowFocused) return REALTIME_CONFIG.POLLING_INTERVALS.INACTIVE
    return REALTIME_CONFIG.POLLING_INTERVALS.ACTIVE_FOCUSED
  }, [isTabVisible, isWindowFocused])

  const carregarDadosPlano = async (isPollingCall = false) => {
    try {
      if (!isPollingCall) {
        setLoading(true)
      }
      setError(null)
      setConnectionStatus('connected')

      // Adicionar timestamp para evitar cache
      const timestamp = new Date().getTime()

      // Buscar plano atual do aluno
      const currentPlanResponse = await fetch(`/api/user/${userId}/current-plan?t=${timestamp}`, {
        cache: 'no-store'
      })
      const currentPlanData = await currentPlanResponse.json()

      let planData = null
      if (currentPlanData.error) {
        console.warn('Nenhum plano ativo encontrado:', currentPlanData.error)
        setCurrentPlan(null)
      } else {
        planData = currentPlanData
        setCurrentPlan(currentPlanData)
      }

      // Buscar planos disponíveis
      const availablePlansResponse = await fetch(`/api/plans/available?t=${timestamp}`, {
        cache: 'no-store'
      })
      const availablePlansData = await availablePlansResponse.json()

      if (availablePlansData.error) {
        throw new Error(availablePlansData.error)
      }

      setAvailablePlans(availablePlansData || [])

      // Check for data changes during polling
      if (isPollingCall) {
        const currentData = {
          currentPlan: planData,
          availablePlans: availablePlansData || []
        }

        const currentHash = createDataHash(currentData)
        if (lastDataHashRef.current && lastDataHashRef.current !== currentHash) {
          setLastUpdate(new Date())
          console.log('Student plan data updated via polling')
        }
        lastDataHashRef.current = currentHash
      }

      setConnectionStatus('connected')
    } catch (error) {
      console.error('Erro ao carregar dados do plano:', error)
      setError(error instanceof Error ? error.message : 'Erro desconhecido')
      setConnectionStatus('error')

      // Reset para valores padrão em caso de erro
      setCurrentPlan(null)
      setAvailablePlans([])
    } finally {
      if (!isPollingCall) {
        setLoading(false)
      }
      setIsPolling(false)
    }
  }

  const changePlan = async (planId: string, reason?: string) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/user/${userId}/change-plan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId, reason }),
      })

      const result = await response.json()

      if (result.error) {
        throw new Error(result.error)
      }

      // Recarregar dados após mudança
      await carregarDadosPlano()
    } catch (error) {
      console.error('Erro ao alterar plano:', error)
      setError(error instanceof Error ? error.message : 'Erro ao alterar plano')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Start/stop polling based on visibility
  useEffect(() => {
    if (!userId) return

    const startPolling = () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }

      const interval = getPollingInterval()
      if (interval > 0) {
        setConnectionStatus('connected')
        pollingIntervalRef.current = setInterval(async () => {
          setIsPolling(true)
          await carregarDadosPlano(true)
        }, interval)
      } else {
        setConnectionStatus('disconnected')
      }
    }

    // Start polling after initial load
    const timer = setTimeout(startPolling, 2000)

    return () => {
      clearTimeout(timer)
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }
    }
  }, [userId, getPollingInterval])

  // Restart polling when visibility changes
  useEffect(() => {
    if (!userId) return

    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
    }

    const interval = getPollingInterval()
    if (interval > 0 && (isTabVisible || isWindowFocused)) {
      setConnectionStatus('connected')
      pollingIntervalRef.current = setInterval(async () => {
        setIsPolling(true)
        await carregarDadosPlano(true)
      }, interval)
    } else {
      setConnectionStatus('disconnected')
    }
  }, [isTabVisible, isWindowFocused, getPollingInterval, userId])

  useEffect(() => {
    if (userId) {
      carregarDadosPlano()
    }
  }, [userId])

  return {
    currentPlan,
    availablePlans,
    loading,
    error,
    refetch: carregarDadosPlano,
    changePlan,
    // Real-time properties
    isPolling,
    lastUpdate,
    connectionStatus
  }
}
