/**
 * Hook para gerenciamento de variáveis de template
 * Fornece acesso às variáveis disponíveis e exemplos
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { TemplateManagementService, TemplateEngine } from '@/services/notifications';
import type {
  TemplateVariable,
  NotificationType
} from '@/services/notifications/types/notification-types';

interface UseTemplateVariablesReturn {
  // Estado
  variables: TemplateVariable[];
  exampleVariables: Record<string, any>;
  loading: boolean;
  error: string | null;
  
  // Operações
  loadVariables: (type: NotificationType) => Promise<void>;
  generateExamples: (type: NotificationType) => Promise<void>;
  getVariablesByCategory: () => Record<string, TemplateVariable[]>;
  
  // Utilitários
  clearError: () => void;
  findVariable: (key: string) => TemplateVariable | undefined;
  getRequiredVariables: () => TemplateVariable[];
  getOptionalVariables: () => TemplateVariable[];
}

export function useTemplateVariables(
  initialType?: NotificationType
): UseTemplateVariablesReturn {
  const [variables, setVariables] = useState<TemplateVariable[]>([]);
  const [exampleVariables, setExampleVariables] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const templateService = new TemplateManagementService();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const loadVariables = useCallback(async (type: NotificationType) => {
    setLoading(true);
    setError(null);

    try {
      const result = await templateService.getAvailableVariables(type);
      
      if (result.success) {
        setVariables(result.data || []);
      } else {
        setError(result.error || 'Erro ao carregar variáveis');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, [templateService]);

  const generateExamples = useCallback(async (type: NotificationType) => {
    setError(null);

    try {
      const result = await templateService.createExampleVariables(type);
      
      if (result.success) {
        setExampleVariables(result.data || {});
      } else {
        setError(result.error || 'Erro ao gerar exemplos');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, [templateService]);

  const getVariablesByCategory = useCallback((): Record<string, TemplateVariable[]> => {
    const categorized: Record<string, TemplateVariable[]> = {};
    
    variables.forEach(variable => {
      const category = variable.category || 'Outras';
      if (!categorized[category]) {
        categorized[category] = [];
      }
      categorized[category].push(variable);
    });

    // Ordenar categorias por importância
    const orderedCategories: Record<string, TemplateVariable[]> = {};
    const categoryOrder = ['academy', 'student', 'payment', 'class', 'enrollment', 'event', 'system', 'user'];
    
    categoryOrder.forEach(category => {
      const categoryKey = Object.keys(categorized).find(key => 
        key.toLowerCase() === category
      );
      if (categoryKey && categorized[categoryKey]) {
        orderedCategories[categoryKey] = categorized[categoryKey];
      }
    });

    // Adicionar categorias restantes
    Object.keys(categorized).forEach(category => {
      if (!orderedCategories[category]) {
        orderedCategories[category] = categorized[category];
      }
    });

    return orderedCategories;
  }, [variables]);

  const findVariable = useCallback((key: string): TemplateVariable | undefined => {
    return variables.find(variable => variable.variable_key === key);
  }, [variables]);

  const getRequiredVariables = useCallback((): TemplateVariable[] => {
    return variables.filter(variable => variable.is_required);
  }, [variables]);

  const getOptionalVariables = useCallback((): TemplateVariable[] => {
    return variables.filter(variable => !variable.is_required);
  }, [variables]);

  // Carregar variáveis automaticamente se tipo inicial fornecido
  useEffect(() => {
    if (initialType) {
      loadVariables(initialType);
      generateExamples(initialType);
    }
  }, [initialType, loadVariables, generateExamples]);

  return {
    // Estado
    variables,
    exampleVariables,
    loading,
    error,
    
    // Operações
    loadVariables,
    generateExamples,
    getVariablesByCategory,
    
    // Utilitários
    clearError,
    findVariable,
    getRequiredVariables,
    getOptionalVariables
  };
}

/**
 * Hook para buscar variáveis de um tipo específico
 */
export function useTemplateVariablesForType(type: NotificationType) {
  return useTemplateVariables(type);
}

/**
 * Hook para validar se variáveis estão sendo usadas corretamente
 */
export function useVariableUsage(
  template: string,
  availableVariables: TemplateVariable[]
): {
  usedVariables: string[];
  missingRequired: TemplateVariable[];
  unknownVariables: string[];
  unusedOptional: TemplateVariable[];
} {
  const [result, setResult] = useState({
    usedVariables: [] as string[],
    missingRequired: [] as TemplateVariable[],
    unknownVariables: [] as string[],
    unusedOptional: [] as TemplateVariable[]
  });

  useEffect(() => {
    const usedVariables = TemplateEngine.extractVariables(template);
    const availableKeys = availableVariables.map(v => v.variable_key);
    const requiredVariables = availableVariables.filter(v => v.is_required);
    const optionalVariables = availableVariables.filter(v => !v.is_required);

    // Variáveis obrigatórias não utilizadas
    const missingRequired = requiredVariables.filter(
      variable => !usedVariables.includes(variable.variable_key)
    );

    // Variáveis desconhecidas (não estão na lista de disponíveis)
    const unknownVariables = usedVariables.filter(
      variable => !availableKeys.includes(variable) && !isAcademyVariable(variable)
    );

    // Variáveis opcionais não utilizadas
    const unusedOptional = optionalVariables.filter(
      variable => !usedVariables.includes(variable.variable_key)
    );

    setResult({
      usedVariables,
      missingRequired,
      unknownVariables,
      unusedOptional
    });
  }, [template, availableVariables]);

  return result;
}

/**
 * Verifica se uma variável é uma variável da academia (sempre disponível)
 */
function isAcademyVariable(variableName: string): boolean {
  const academyVariables = [
    'academyName',
    'academyLogo',
    'primaryColor',
    'secondaryColor',
    'academySlug'
  ];
  
  return academyVariables.includes(variableName);
}

/**
 * Hook para sugestões de variáveis baseadas no contexto
 */
export function useVariableSuggestions(
  currentTemplate: string,
  templateType: NotificationType,
  availableVariables: TemplateVariable[]
): TemplateVariable[] {
  const [suggestions, setSuggestions] = useState<TemplateVariable[]>([]);

  useEffect(() => {
    const usedVariables = TemplateEngine.extractVariables(currentTemplate);
    
    // Sugerir variáveis não utilizadas, priorizando as obrigatórias
    const unused = availableVariables.filter(
      variable => !usedVariables.includes(variable.variable_key)
    );

    // Ordenar por importância: obrigatórias primeiro, depois por categoria
    const sorted = unused.sort((a, b) => {
      if (a.is_required && !b.is_required) return -1;
      if (!a.is_required && b.is_required) return 1;
      
      // Ordenar por categoria
      const categoryOrder = ['academy', 'student', 'payment', 'class'];
      const aIndex = categoryOrder.indexOf(a.category || '');
      const bIndex = categoryOrder.indexOf(b.category || '');
      
      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      
      return a.variable_name.localeCompare(b.variable_name);
    });

    setSuggestions(sorted.slice(0, 5)); // Limitar a 5 sugestões
  }, [currentTemplate, templateType, availableVariables]);

  return suggestions;
}
